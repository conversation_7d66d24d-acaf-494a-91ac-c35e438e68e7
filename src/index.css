@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', sans-serif;
}

/* Custom animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px , 0px) scale(1);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-fade-in-left {
  animation: fade-in-left 1s ease-out forwards;
}

.animate-fade-in-right {
  animation: fade-in-right 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fade-in-up 1s ease-out forwards;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* === Custom Scrollbar Styles (Overlay pill, trackless, thumb only on scrollbar hover) ===
   To remove, delete everything between these START/END comments.
   Shows a floating, shaded pill scrollbar thumb ONLY when mouse is over the scrollbar area.
   No visible scrollbar track at any time.
   Last updated: 2025-04-14
   ================================================ */

/* Webkit browsers: reserve scrollbar area, but make it invisible */
::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

/* Hide scrollbar track completely */
::-webkit-scrollbar-track {
  background: transparent;
}

/* Show pill (thumb) at low opacity by default for subtle discoverability */
::-webkit-scrollbar-thumb {
  background: rgba(40, 40, 40, 0.7);
  border-radius: 999px;
  box-shadow: 0 2px 8px rgba(40, 40, 40, 0.18);
  border: 1px solid rgba(255,255,255,0.15);
  min-height: 40px;
  opacity: 0.2; /* Subtle, always visible */
  transition: opacity 0.2s, background 0.2s;
  pointer-events: auto;
}

/* On hover, make the pill more visible */
::-webkit-scrollbar-thumb:hover {
  opacity: 0.5;
  background: rgba(60, 60, 60, 0.7);
}

/* Firefox: Hide scrollbar always (no overlay support in Firefox) */
html {
  scrollbar-width: none !important;
  scrollbar-color: transparent transparent !important;
}

/* === END Custom Scrollbar Styles === */
