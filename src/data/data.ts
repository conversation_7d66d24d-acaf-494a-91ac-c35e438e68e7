// Data exported from public/data/data.json
// This file provides type-safe access to the data

// Type definitions
export interface Course {
  id: number;
  slug: string;
  title: string;
  duration: number;
  capacity: number;
  level: string;
  description: string;
  highlights: string[];
  curriculum: string[];
  dates: string[];
  image: string;
  certification: number[]; // array of certification IDs
  active: boolean;
  featured: boolean;
  gallery: string[];
  price: {
    shared: number;
    private: number;
    sharedTwin: number;
  };
  programOverview?: string;
  scheduleId?: string;
  instructors: number[];
  excursion?: number[];
  prerequisites: string[];
}

export interface Certification {
  id: number;
  name: string;
  link: string;
}

export interface Retreat {
  id: number;
  slug: string;
  title: string;
  duration: number;
  dates: string[];
  capacity: number;
  theme: string;
  location: string;
  description: string;
  highlights: string[];
  includes: string[];
  price: {
    shared: number;
    private: number;
    sharedTwin: number;
  };
  image: string;
  instructors: number[];
  active: boolean;
}

export interface Teacher {
  id: number;
  name: string;
  role: string;
  founder: boolean;
  bio: string;
  image: string;
  specialties: string[];
  experience: string;
  status: string;
}

export interface Schedule {
  id: string;
  title: string;
  dailySchedule: { time: string; activity: string }[];
}

export interface CommonData {
  accommodation: {
    type: string;
    description: string;
    features: string[]
  };
  food: string;
  location: {
    address: string;
    description: string;
    mapLink: string
  };
  includedExcursions: {
    id: number;
    activity: string
  }[];
  thingsToDo: string[];
  whatsIncluded: string[];
  whatsNotIncluded: string[];
  howToGetThere: string;
}

export interface Data {
  courses: Course[];
  certifications: Certification[];
  retreats: Retreat[];
  teachers: Teacher[];
  schedules: Schedule[];
  common: CommonData;
}

// Data
export const data: Data = {
  "courses": [
    {
      "id": 1,
      "slug": "200-hours-yoga-teacher-training",
      "title": "200-Hours Yoga Teacher Training",
      "duration": 28,
      "capacity": 12,
      "level": "Beginner to Intermediate",
      "description": "Our comprehensive 200-hour Yoga Teacher Training program is designed for aspiring teachers and dedicated practitioners who wish to deepen their understanding of yoga. This Yoga Alliance certified course covers asana, pranayama, meditation, anatomy, philosophy, teaching methodology, and more.",
      "highlights": [
        "Yoga Alliance Certified",
        "Small batch sizes (max 12 students)",
        "Experienced international faculty",
        "Accommodation and meals included",
        "Excursions to sacred sites"
      ],
      "curriculum": [
        "Asana practice and alignment",
        "Teaching methodology",
        "Anatomy and physiology",
        "Yoga philosophy and ethics",
        "Pranayama and meditation techniques",
        "Sanskrit and mantras",
        "Ayurvedic principles"
      ],
      "dates": ["03/06/2025","03/07/2025","03/08/2025","03/09/2025","03/10/2025","03/11/2025","03/12/2025","03/01/2026","01/02/2026","03/03/2026"],
      "price": {"shared": 1199,"private": 1599,"sharedTwin": 2278},
      "active": true,
      "featured": true,
      "image": "/images/TTC-Banner.webp",
      "gallery": [
        "/images/banner.jpeg",
        "https://placehold.co/1200x800/e0f2fe/075985?text=Meditation+Session",
        "/images/school.jpeg",
        "https://placehold.co/1200x800/dcfce7/166534?text=Philosophy+Discussion",
        "https://placehold.co/1200x800/ffe4e6/9f1239?text=Pranayama+Practice",
        "https://placehold.co/1200x800/e0f2fe/075985?text=Some+Session",
        "https://placehold.co/1200x800/fae8ff/86198f?text=Some+Class"
      ],
      "certification": [1],
      "programOverview": "This foundational 200-hour program provides a comprehensive introduction to the philosophy, practice, and teaching methodology of Hatha and Vinyasa yoga. Suitable for aspiring teachers and dedicated students seeking to deepen their personal practice and understanding.",
      "scheduleId": "schedule1",
      "instructors": [1, 4, 5],
      "excursion": [1, 2, 3, 4],
      "prerequisites": []
    },
    {
      "id": 2,
      "slug": "300-hours-advanced-teacher-training",
      "title": "300-Hours Advanced Teacher Training",
      "duration": 42,
      "capacity": 10,
      "level": "Intermediate to Advanced",
      "description": "Take your teaching and practice to the next level with our advanced 300-hour program. Building on the foundation of a 200-hour certification, this course delves deeper into advanced asanas, therapeutic applications, subtle body anatomy, advanced teaching skills, and specialized yoga styles.",
      "highlights": [
        "Yoga Alliance Certified",
        "Limited to 15 students per batch",
        "Specialized modules with expert teachers",
        "Private accommodation options",
        "Personalized mentorship"
      ],
      "curriculum": [
        "Advanced asana and sequencing",
        "Therapeutic yoga applications",
        "Subtle body anatomy (chakras, nadis)",
        "Advanced pranayama and meditation",
        "Yoga for specific conditions",
        "Business of yoga",
        "Specialized teaching skills"
      ],
      "dates": ["15/09/2025","15/01/2026"],
      "price": {"shared": 1799,"private": 2399,"sharedTwin": 3418},
      "active": false,
      "featured": false,
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=300-Hour+TTC",
      "gallery": [
        "https://placehold.co/1200x800/fef3c7/854d0e?text=Yoga+Class",
        "https://placehold.co/1200x800/e0f2fe/075985?text=Meditation+Session",
        "https://placehold.co/1200x800/fae8ff/86198f?text=Anatomy+Class",
        "https://placehold.co/1200x800/dcfce7/166534?text=Philosophy+Discussion",
        "https://placehold.co/1200x800/ffe4e6/9f1239?text=Pranayama+Practice",
        "https://placehold.co/1200x800/e0f2fe/075985?text=Some+Session",
        "https://placehold.co/1200x800/fae8ff/86198f?text=Some+Class"
      ],
      "certification": [1],
      "programOverview": "This advanced 300-hour program is designed for certified teachers who want to deepen their expertise, refine their teaching skills, and explore specialized areas of yoga. It includes mentorship, advanced techniques, and therapeutic applications.",
      "scheduleId": "schedule2",
      "instructors": [1, 4],
      "excursion": [1, 2, 3, 4],
      "prerequisites": []
    },
    {
      "id": 3,
      "slug": "100-hour-meditation-teacher-training",
      "title": "100-Hour Meditation Teacher Training",
      "duration": 14,
      "capacity": 20,
      "level": "All Levels",
      "description": "Specialize in the art and science of meditation with our focused 100-hour Meditation Teacher Training. Learn various meditation techniques from different traditions, understand the science behind meditation, and develop the skills to guide others in their meditation journey.",
      "highlights": [
        "Comprehensive meditation toolkit",
        "Neuroscience of meditation",
        "Daily silent meditation practice",
        "Teaching practicum",
        "Certification to teach meditation"
      ],
      "curriculum": [
        "Various meditation techniques",
        "History and philosophy of meditation",
        "Science and benefits of meditation",
        "Teaching methodology",
        "Creating guided meditations",
        "Working with different populations",
        "Establishing a personal practice"
      ],
      "dates": ["20/01/2025","15/04/2025","10/08/2025","05/12/2025"],
      "price": {"shared": 1200,"private": 1600,"sharedTwin": 2400},
      "active": true,
      "featured": false,
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Meditation+TTC",
      "gallery": [],
      "certification": [2],
      "programOverview": "This 100-hour program immerses you in the theory and practice of meditation, equipping you with the skills to guide others and deepen your own practice. It covers diverse techniques, neuroscience, and teaching methodology.",
      "scheduleId": "schedule1",
      "instructors": [1, 4],
      "excursion": [1, 2, 3, 4],
      "prerequisites": []
    },
    {
      "id": 4,
      "slug": "50-hour-yin-yoga-certification",
      "title": "50-Hour Yin Yoga Certification",
      "duration": 7,
      "capacity": 20,
      "level": "All Levels",
      "description": "Dive deep into the practice of Yin Yoga with our specialized 50-hour certification program. Learn the principles of Yin Yoga, understand the effects on fascia and connective tissues, explore meridian theory, and develop skills to create therapeutic Yin sequences for various needs.",
      "highlights": [
        "Focus on anatomy of fascia",
        "Traditional Chinese Medicine concepts",
        "Meridian theory",
        "Prop usage and modifications",
        "Therapeutic applications"
      ],
      "curriculum": [
        "Principles and practice of Yin Yoga",
        "Anatomy of connective tissues",
        "Meridians and energy channels",
        "Sequencing for different effects",
        "Mindfulness in Yin Yoga",
        "Modifications and use of props",
        "Teaching methodology"
      ],
      "dates": ["05/02/2025","20/05/2025","15/09/2025","10/11/2025"],
      "price": {"shared": 800,"private": 1000,"sharedTwin": 1500},
      "active": true,
      "featured": false,
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Yin+Yoga+Certification",
      "gallery": [],
      "certification": [2],
      "programOverview": "This 50-hour Yin Yoga program explores the principles, anatomy, and therapeutic benefits of Yin Yoga. It equips you to design and teach safe, effective Yin classes for diverse needs.",
      "scheduleId": "schedule2",
      "instructors": [1, 4],
      "excursion": [1, 2, 3, 4],
      "prerequisites": []
    },
    {
      "id": 5,
      "slug": "100-hour-ayurveda-foundations",
      "title": "100-Hour Ayurveda Foundations",
      "duration": 14,
      "capacity": 20,
      "level": "All Levels",
      "description": "Explore the ancient science of Ayurveda and its application to yoga and modern life. This comprehensive program covers Ayurvedic principles, dosha assessment, dietary guidelines, lifestyle recommendations, and Ayurvedic approaches to yoga practice.",
      "highlights": [
        "Personal dosha assessment",
        "Ayurvedic cooking classes",
        "Herb identification and usage",
        "Self-care practices",
        "Integration with yoga"
      ],
      "curriculum": [
        "History and philosophy of Ayurveda",
        "Tridosha theory (Vata, Pitta, Kapha)",
        "Ayurvedic anatomy and physiology",
        "Nutrition and diet",
        "Daily routines (Dinachshanti)",
        "Seasonal practices (Rituchshanti)",
        "Ayurvedic yoga therapy"
      ],
      "dates": ["01/03/2025","10/06/2025","05/10/2025"],
      "price": {"shared": 1400,"private": 1800,"sharedTwin": 2800},
      "active": true,
      "featured": true,
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Ayurveda+Foundations",
      "gallery": [],
      "certification": [3],
      "programOverview": "This 100-hour Ayurveda program introduces the fundamental principles of Ayurveda, its integration with yoga, and practical applications for health and well-being.",
      "scheduleId": "schedule1",
      "instructors": [1, 4],
      "excursion": [1, 2, 3, 4],
      "prerequisites": []
    },
    {
      "id": 6,
      "slug": "25-hour-yoga-nidra-training",
      "title": "25-Hour Yoga Nidra Training",
      "duration": 4,
      "capacity": 20,
      "level": "All Levels",
      "description": "Learn the powerful practice of Yoga Nidra, often called 'yogic sleep,' in this specialized certification program. Understand the stages of Yoga Nidra, its effects on the nervous system, and how to guide others through this transformative practice.",
      "highlights": ["Daily Yoga Nidra practice","Script writing workshop","Recording techniques","Applications for different needs","Take-home resources"],
      "curriculum": ["History and origins of Yoga Nidra","Stages of Yoga Nidra practice","Neuroscience of Yoga Nidra","Creating effective scripts","Voice modulation techniques","Applications for stress, trauma, and sleep","Teaching methodology"],
      "dates": ["15/01/2025","05/04/2025","20/07/2025","15/10/2025"],
      "price": {"shared": 500,"private": 700,"sharedTwin": 1000},
      "active": true,
      "featured": false,
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Yoga+Nidra+Training",
      "gallery": [],
      "certification": [2],
      "programOverview": "This 25-hour Yoga Nidra program provides a deep dive into the theory and practice of yogic sleep, equipping you to guide transformative sessions for diverse needs.",
      "scheduleId": "schedule2",
      "instructors": [1, 4],
      "excursion": [1, 2, 3, 4],
      "prerequisites": []
    },
    {
      "id": 7,
      "title": "500 Hours Yoga Teacher Training",
      "slug": "500-hours-yoga-teacher-training",
      "duration": 70,
      "capacity": 10,
      "level": "Advance",
      "description": "Some Random Description",
      "programOverview": "Some Random Program Overview",
      "highlights": [
          "Yoga Alliance Certified",
          "Highlight 2"
      ],
      "curriculum": [
          "subject 1",
          "subject 2"
      ],
      "dates": [
          "22/04/2025"
      ],
      "price": {
          "shared": 1000,
          "private": 2000,
          "sharedTwin": 3000
      },
      "active": true,
      "featured": true,
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=500-Hour+TTC",
      "gallery": [],
      "certification": [
          1
      ],
      "scheduleId": "schedule2",
      "instructors": [
          1
      ],
      "excursion": [
          1,
          2,
          3,
          4
      ],
      "prerequisites": [
          "Basic Understanding of Yoga"
      ]
    }
  ],
  "certifications": [
    {"id": 1,"name": "Yoga Alliance","link": "#"},
    {"id": 2,"name": "Yoga Capital","link": "#"},
    {"id": 3,"name": "Shanti Makan","link": "#"}
  ],
  "retreats": [
    {
      "id": 1,
      "slug": "himalayan-meditation-retreat",
      "title": "Himalayan Meditation Retreat",
      "duration": 7,
      "dates": ["10/01/2025", "15/03/2025", "05/10/2025"],
      "capacity": 20,
      "theme": "Meditation",
      "location": "Shanti Yog Peeth, Rishikesh",
      "description": "Immerse yourself in the serene energy of the Himalayas with our signature meditation retreat. This transformative week focuses on deepening your meditation practice.",
      "highlights": [
        "Daily meditation sessions",
        "Mountain hiking",
        "Silent periods",
        "Pranayama practices",
        "Sacred site visits"
      ],
      "includes": [
        "Accommodation in mountain ashram",
        "Three vegetarian meals daily",
        "Guided meditation sessions",
        "Yoga classes",
        "Nature walks",
        "Transportation to excursion sites"
      ],
      "price": {"shared": 1200,"private": 1500,"sharedTwin": 2000},
      "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Himalayan+Retreat",
      "instructors": [4],
      "active": true
    }
  ],
  "teachers": [
    {
      "id": 1,
      "name": "Yogini Swati",
      "role": "Pranayama & Meditation",
      "founder": true,
      "bio": "With over 15+ years of practice and study, Swati brings profound wisdom and authenticity to her teachings. Trained in traditional Hatha and Kundalini yoga in Rishikesh, she has dedicated her life to sharing the transformative power of yoga with practitioners from around the world.",
      "image": "/images/teachers/yogini-swati.jpg",
      "specialties": ["Hatha Yoga", "Kundalini Yoga", "Meditation", "Philosophy"],
      "experience": "15+ years",
      "status": "active"
    },
    {
      "id": 4,
      "name": "Shubham Arya",
      "role": "Hatha Yoga & Ashtanga Yoga",
      "founder": false,
      "bio": "Yogi Shubham has studied with meditation masters throughout India and Tibet. His gentle approach to breathwork and meditation helps students quiet their minds and connect with their inner wisdom. She leads our meditation retreats and specialized pranayama workshops.",
      "image": "https://placehold.co/1000x1000/e2e8f0/1e293b?text=Lakshmi+Nair",
      "specialties": ["Meditation", "Pranayama", "Yoga Nidra", "Sound Healing"],
      "experience": "20 years",
      "status": "active"
    },
    {
      "id": 5,
      "name": "Boba Fett",
      "role": "Horse Riding",
      "founder": false,
      "bio": "Lakshmi has studied with meditation masters throughout India and Tibet. Her gentle approach to breathwork and meditation helps students quiet their minds and connect with their inner wisdom. She leads our meditation retreats and specialized pranayama workshops.",
      "image": "",
      "specialties": ["Meditation", "Pranayama", "Yoga Nidra", "Sound Healing"],
      "experience": "20 years",
      "status": "active"
    }
  ],
  "schedules": [
    {
      "id": "schedule1",
      "title": "Standard TTC Schedule",
      "dailySchedule": [
        { "time": "06:00 - 06:30", "activity": "Pranayama & Meditation" },
        { "time": "06:30 - 08:00", "activity": "Asana Practice (Hatha/Vinyasa)" },
        { "time": "08:00 - 09:00", "activity": "Breakfast" },
        { "time": "09:30 - 11:00", "activity": "Teaching Methodology / Alignment" },
        { "time": "11:15 - 12:45", "activity": "Anatomy / Philosophy" },
        { "time": "13:00 - 14:00", "activity": "Lunch" },
        { "time": "14:30 - 16:00", "activity": "Workshops / Practicum" },
        { "time": "16:30 - 18:00", "activity": "Evening Asana / Restorative" },
        { "time": "18:30 - 19:30", "activity": "Dinner" },
        { "time": "19:30 - 20:30", "activity": "Evening Satsang / Chanting (Optional)" }
      ]
    },
    {
      "id": "schedule2",
      "title": "Advanced TTC Schedule",
      "dailySchedule": [
        { "time": "06:00 - 06:30", "activity": "Advanced Pranayama & Meditation" },
        { "time": "06:30 - 08:30", "activity": "Advanced Asana Practice" },
        { "time": "08:30 - 09:30", "activity": "Breakfast" },
        { "time": "10:00 - 12:00", "activity": "Specialized Modules / Practicum" },
        { "time": "12:15 - 13:30", "activity": "Anatomy / Philosophy" },
        { "time": "13:30 - 14:30", "activity": "Lunch" },
        { "time": "15:00 - 17:00", "activity": "Therapeutic Applications / Workshops" },
        { "time": "17:30 - 19:00", "activity": "Evening Asana / Restorative" },
        { "time": "19:00 - 20:00", "activity": "Dinner" },
        { "time": "20:00 - 21:00", "activity": "Satsang / Q&A / Chanting" }
      ]
    }
  ],
  "common": {
    "accommodation": {
      "type": "Shared Twin Room",
      "description": "Clean and comfortable shared rooms (2 students per room) with attached western-style bathrooms. Single room upgrades may be available upon request for an additional fee.",
      "features": ["Attached Bathroom", "Hot Water", "Wi-Fi Access", "Bed Linens & Towels"]
    },
    "food": "Three daily nutritious and delicious vegetarian meals (breakfast, lunch, dinner) are included, prepared with local ingredients. Vegan and gluten-free options available upon request.",
    "location": {
      "address": "Shanti Yog Peeth, Tapovan, Rishikesh, Uttarakhand, India",
      "description": "Nestled in the serene foothills of the Himalayas in the Tapovan area of Rishikesh, known as the Yoga Capital of the World. Our school offers a peaceful environment conducive to learning and practice, just a short walk from the holy Ganges river.",
      "mapLink": "https://maps.google.com/?q=Shanti+Yog+Peeth+Rishikesh"
    },
    "includedExcursions": [
      {"id": 1, "activity": "Sunrise trek to Kunjapuri Temple"},
      {"id": 2, "activity": "Visit to Vashishta Gufa (Cave)"},
      {"id": 3, "activity": "Local market exploration"},
      {"id": 4, "activity": "Ganga Aarti ceremony attendance"}
    ],
    "thingsToDo": [
      "White water rafting",
      "Bungee jumping",
      "Explore the Beatles Ashram",
      "Visit local temples and ashrams",
      "Enjoy cafes in Tapovan"
    ],
    "whatsIncluded": [
      "Course tuition and manual",
      "Yoga Alliance certification upon completion",
      "Shared accommodation for the duration",
      "Three vegetarian meals per day",
      "Scheduled excursions",
      "Yoga mat and props usage"
    ],
    "whatsNotIncluded": [
      "Airfare and travel to Rishikesh",
      "Indian Visa fees",
      "Personal expenses (laundry, snacks, etc.)",
      "Travel insurance",
      "Optional activities or therapies"
    ],
    "howToGetThere": "The nearest airport is Jolly Grant Airport in Dehradun (DED), approximately 45 minutes drive from Rishikesh. We can arrange airport pickup for an additional fee. Alternatively, you can travel by train to Haridwar (HW) or Rishikesh (RKSH) and take a taxi/auto-rickshaw."
  }
};

// Export individual sections for convenience
export const { courses, certifications, retreats, teachers, schedules, common } = data;

// Export default for easy importing
export default data;
