import React, { useState, useEffect, useCallback } from 'react';

interface GalleryImage {
  id: number;
  src: string;
  alt: string;
  category: string;
  width?: number;
  height?: number;
}

const Gallery: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedImage) return;

      switch (e.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowRight':
          navigateImage('next');
          break;
        case 'ArrowLeft':
          navigateImage('prev');
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage]);

  const images: GalleryImage[] = [
    {
      id: 1,
      src: '../images/misc/1.jpg',
      alt: 'Yoga by the Ganges',
      category: 'practice'
    },
    {
      id: 2,
      src: '../images/misc/3.jpg',
      alt: 'Meditation session',
      category: 'meditation'
    },
    {
      id: 3,
      src: '../images/misc/11.jpg',
      alt: 'Teacher demonstration',
      category: 'teachers'
    },
    {
      id: 4,
      src: '../images/misc/4.jpg',
      alt: 'Rishikesh views',
      category: 'location'
    },
    {
      id: 5,
      src: '../images/misc/5.jpg',
      alt: 'Group class',
      category: 'practice'
    },
    {
      id: 6,
      src: '../images/misc/6.jpg',
      alt: 'Advanced pose',
      category: 'practice'
    },
    {
      id: 7,
      src: '../images/misc/7.jpg',
      alt: 'Meditation retreat',
      category: 'meditation'
    },
    {
      id: 8,
      src: '../images/misc/8.jpg',
      alt: 'Rishikesh temple',
      category: 'location'
    }
  ];

  const categories = ['all', 'practice', 'meditation', 'teachers', 'location'];
  
  const filteredImages = activeCategory === 'all' 
    ? images 
    : images.filter(img => img.category === activeCategory);

  const navigateImage = (direction: 'next' | 'prev') => {
    if (!selectedImage) return;
    
    const currentIndex = filteredImages.findIndex(img => img.id === selectedImage.id);
    let newIndex;
    
    if (direction === 'next') {
      newIndex = currentIndex === filteredImages.length - 1 ? 0 : currentIndex + 1;
    } else {
      newIndex = currentIndex === 0 ? filteredImages.length - 1 : currentIndex - 1;
    }
    
    setSelectedImage(filteredImages[newIndex]);
  };

  const openLightbox = useCallback((image: GalleryImage) => {
    setSelectedImage(image);
    document.body.style.overflow = 'hidden';
  }, []);

  const closeLightbox = useCallback(() => {
    setSelectedImage(null);
    document.body.style.overflow = 'auto';
  }, []);

  // Simulate loading state
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, [activeCategory]);

  return (
    <section id="gallery" className="py-20 md:py-32 bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
            <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Gallery</span>
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 ml-4"></div>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
            Glimpses of <span className="text-amber-500 dark:text-amber-400">Shanti Yog Peeth</span>
          </h2>
          
          <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Explore visual moments from our yoga school, capturing the essence of practice, 
            meditation, and the serene beauty of Rishikesh.
          </p>
        </div>
        
        {/* Filter tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 transform hover:scale-105 ${
                activeCategory === category
                  ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-lg scale-105'
                  : 'bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600 shadow-md'
              }`}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </button>
          ))}
        </div>
        
        {/* Gallery grid with masonry-like layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-fr">
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 8 }).map((_, index) => (
              <div key={`skeleton-${index}`} className="relative aspect-square rounded-lg bg-slate-200 dark:bg-slate-700 animate-pulse" />
            ))
          ) : (
            filteredImages.map((image) => (
              <div 
                key={image.id}
                className="relative overflow-hidden rounded-lg shadow-lg transition-transform duration-300 hover:scale-[1.02] hover:shadow-xl"
                onClick={() => openLightbox(image)}
              >
                <div className="aspect-square overflow-hidden bg-slate-200 dark:bg-slate-700">
                  <img 
                    src={image.src} 
                    alt={image.alt} 
                    className="w-full h-full object-cover transition-transform duration-700 hover:scale-110"
                    loading="lazy"
                  />
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent opacity-0 hover:opacity-100 transition-all duration-300 flex items-end">
                  <div className="p-6 transform translate-y-4 hover:translate-y-0 transition-transform duration-300">
                    <p className="text-white font-medium text-lg">{image.alt}</p>
                    <span className="text-amber-300 text-sm">
                      {image.category.charAt(0).toUpperCase() + image.category.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        
        {/* Lightbox with navigation */}
        {selectedImage && (
          <div 
            className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
            onClick={closeLightbox}
          >
            <div 
              className="relative max-w-5xl w-full max-h-[90vh]"
              onClick={(e) => e.stopPropagation()}
            >
              <img 
                src={selectedImage.src} 
                alt={selectedImage.alt} 
                className="max-w-full max-h-[80vh] object-contain mx-auto shadow-2xl rounded-lg"
              />
              
              {/* Navigation buttons */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('prev');
                }}
                className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-300"
                aria-label="Previous image"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('next');
                }}
                className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/20 flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-300"
                aria-label="Next image"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>

              {/* Close button */}
              <button 
                onClick={closeLightbox}
                className="absolute top-4 right-4 w-10 h-10 rounded-full bg-white/20 flex items-center justify-center text-white hover:bg-white/30 transition-colors duration-300"
                aria-label="Close lightbox"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* Image info */}
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/10 backdrop-blur-md p-4 rounded-lg text-center w-full max-w-lg">
                <h3 className="text-white text-lg font-medium">{selectedImage.alt}</h3>
                <p className="text-amber-300 text-sm mt-1">
                  {selectedImage.category.charAt(0).toUpperCase() + selectedImage.category.slice(1)}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Gallery;