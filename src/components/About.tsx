import React, { useEffect, useRef } from 'react';

const About: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (imageRef.current) imageRef.current.classList.add('animate-fade-in-left');
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-right');
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.unobserve(sectionRef.current);
      }
    };
  }, []);

  return (
    <section 
      id="about" 
      ref={sectionRef}
      className="py-20 md:py-32 relative overflow-hidden"
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-amber-100 dark:bg-indigo-900/20 rounded-bl-full opacity-50 dark:opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-amber-100 dark:bg-indigo-900/20 rounded-tr-full opacity-50 dark:opacity-30"></div>
      
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-20">
          <div 
            ref={imageRef}
            className="lg:w-1/2 relative opacity-0"
          >
            <div className="relative z-10">
              <img 
                src="images/school.jpeg" 
                alt="Yoga practice in Rishikesh" 
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-amber-400 dark:bg-indigo-600 rounded-lg -z-10"></div>
            </div>
            <div className="absolute top-1/4 -left-8 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-lg z-20 max-w-xs transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <p className="text-sm italic text-slate-600 dark:text-slate-300">
                "Yoga is the journey of the self, through the self, to the self."
              </p>
              <p className="text-right text-xs mt-2 text-slate-500 dark:text-slate-400">— The Bhagavad Gita</p>
            </div>
          </div>
          
          <div 
            ref={contentRef}
            className="lg:w-1/2 opacity-0"
          >
            <div className="flex items-center mb-4">
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
              <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Our Story</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
              Embracing Tradition in the <br />
              <span className="text-amber-500 dark:text-amber-400">Shanti Yog Peeth of the World</span>
            </h2>
            
            <p className="text-slate-700 dark:text-slate-300 mb-6 leading-relaxed">
              Established in 2010, Shanti Yog Peeth stands as a beacon of authentic yogic wisdom in Rishikesh, the Yoga Capital of the World. Located in the spiritual heart of India, our internationally accredited school offers transformative 200-hour and 300-hour Yoga Teacher Training programs certified by Yoga Alliance. Our unique approach combines traditional Hatha, Ashtanga, and Kundalini practices with modern teaching methodologies, making ancient wisdom accessible to contemporary practitioners.
            </p>
            
            <p className="text-slate-700 dark:text-slate-300 mb-8 leading-relaxed">
              Nestled along the sacred banks of the Ganges River in Rishikesh, our facility provides an ideal environment for deep spiritual practice and learning. We specialize in traditional Vedic philosophy, advanced pranayama techniques, therapeutic yoga, and meditation practices. Our expert instructors, each with minimum 7 and maximum 30 years of experience, guide students through comprehensive programs that include Sanskrit studies, Ayurveda basics, anatomy, and teaching methodology. The serene Himalayan setting, combined with modern amenities and authentic teaching approach, creates an unparalleled environment for yoga education and personal transformation.
            </p>
            
            <div className="grid grid-cols-2 gap-6 mb-8">
              <div className="flex flex-col items-center p-4 bg-amber-100/50 dark:bg-slate-800/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-1">15+</span>
                <span className="text-sm text-slate-600 dark:text-slate-400 text-center">Years of Experience</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-amber-100/50 dark:bg-slate-800/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-1">1400+</span>
                <span className="text-sm text-slate-600 dark:text-slate-400 text-center">Students Trained</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-amber-100/50 dark:bg-slate-800/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-1">12</span>
                <span className="text-sm text-slate-600 dark:text-slate-400 text-center">Expert Teachers</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-amber-100/50 dark:bg-slate-800/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-1">8</span>
                <span className="text-sm text-slate-600 dark:text-slate-400 text-center">Yoga Styles</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;