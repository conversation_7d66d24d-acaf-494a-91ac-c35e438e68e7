import React, { useState, useEffect, useRef } from 'react';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  quote: string;
  image: string;
  rating: number;
}

const Testimonials: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const intervalRef = useRef<number | null>(null);
  
  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: '<PERSON>',
      location: 'New York, USA',
      quote: 'My month at Shanti Yog Peeth was truly life-changing. The teachers\' deep knowledge and the serene environment of Rishikesh created the perfect space for transformation. I left with not only improved physical practice but a profound shift in perspective.',
      image: 'https://placehold.co/400x400/e2e8f0/1e293b?text=Sarah+J',
      rating: 5
    },
    {
      id: 2,
      name: '<PERSON>',
      location: 'Barcelona, Spain',
      quote: 'As a yoga instructor myself, I came to deepen my practice and was blown away by the authenticity and depth of teaching. <PERSON><PERSON><PERSON>\'s guidance helped me connect to the spiritual aspects of yoga in ways I never experienced before. Eternally grateful!',
      image: 'https://placehold.co/400x400/e2e8f0/1e293b?text=<PERSON>+T',
      rating: 5
    },
    {
      id: 3,
      name: '<PERSON> Chen',
      location: 'Melbourne, Australia',
      quote: 'The 200-hour teacher training exceeded all my expectations. The curriculum was comprehensive, the teachers were knowledgeable and supportive, and the location by the Ganges was magical. I left feeling confident to share yoga with others.',
      image: 'https://placehold.co/400x400/e2e8f0/1e293b?text=Emma+C',
      rating: 5
    },
    {
      id: 4,
      name: 'Thomas Weber',
      location: 'Berlin, Germany',
      quote: 'I came to Shanti Yog Peeth with chronic back pain and stress from my corporate job. After just two weeks, I experienced significant physical relief and learned tools to manage stress that I still use daily. This place is a hidden gem in Rishikesh.',
      image: 'https://placehold.co/400x400/e2e8f0/1e293b?text=Thomas+W',
      rating: 4
    }
  ];
  
  const nextTestimonial = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    setActiveIndex((prev) => (prev + 1) % testimonials.length);
    
    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
  };
  
  const prevTestimonial = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    setActiveIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    
    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
  };
  
  useEffect(() => {
    intervalRef.current = window.setInterval(() => {
      nextTestimonial();
    }, 8000);
    
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isAnimating]);
  
  const handleDotClick = (index: number) => {
    if (isAnimating || index === activeIndex) return;
    
    setIsAnimating(true);
    setActiveIndex(index);
    
    setTimeout(() => {
      setIsAnimating(false);
    }, 500);
    
    if (intervalRef.current) clearInterval(intervalRef.current);
    intervalRef.current = window.setInterval(() => {
      nextTestimonial();
    }, 8000);
  };
  
  return (
    <section id="testimonials" className="py-20 md:py-32 bg-amber-50/50 dark:bg-slate-800/30">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
            <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Testimonials</span>
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 ml-4"></div>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
            Transformative <span className="text-amber-500 dark:text-amber-400">Experiences</span>
          </h2>
          
          <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Hear from our students about how their journey at Shanti Yog Peeth has impacted their lives,
            both on and off the mat.
          </p>
        </div>
        
        <div className="relative max-w-4xl mx-auto">
          {/* Testimonial carousel */}
          <div className="relative overflow-hidden rounded-2xl bg-white dark:bg-slate-800 shadow-xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${activeIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div 
                  key={testimonial.id}
                  className="w-full flex-shrink-0 p-8 md:p-12"
                >
                  <div className="flex flex-col md:flex-row gap-8 items-center">
                    <div className="md:w-1/3">
                      <div className="relative">
                        <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-amber-100 dark:border-slate-700">
                          <img 
                            src={testimonial.image} 
                            alt={testimonial.name} 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="absolute -bottom-2 -right-2 bg-amber-400 dark:bg-indigo-600 text-white p-2 rounded-full">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.983 3v7.391c0 5.704-3.731 9.57-8.983 10.609l-.995-2.151c2.432-.917 3.995-3.638 3.995-5.849h-4v-10h10zm14.017 0v7.391c0 5.704-3.748 9.571-9 10.609l-.996-2.151c2.433-.917 3.996-3.638 3.996-5.849h-3.983v-10h9.983z"/>
                          </svg>
                        </div>
                      </div>
                      <div className="text-center mt-4">
                        <h3 className="font-bold text-slate-900 dark:text-white">{testimonial.name}</h3>
                        <p className="text-sm text-slate-500 dark:text-slate-400">{testimonial.location}</p>
                        <div className="flex justify-center mt-2">
                          {[...Array(5)].map((_, i) => (
                            <svg 
                              key={i}
                              className={`w-4 h-4 ${i < testimonial.rating ? 'text-amber-400' : 'text-slate-300 dark:text-slate-600'}`}
                              fill="currentColor" 
                              viewBox="0 0 20 20" 
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    <div className="md:w-2/3">
                      <blockquote className="text-slate-700 dark:text-slate-300 text-lg italic leading-relaxed">
                        "{testimonial.quote}"
                      </blockquote>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Navigation arrows */}
            <button 
              onClick={prevTestimonial}
              className="absolute top-1/2 left-4 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/80 dark:bg-slate-700/80 flex items-center justify-center text-slate-700 dark:text-white shadow-md hover:bg-amber-400 dark:hover:bg-indigo-600 transition-colors duration-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <button 
              onClick={nextTestimonial}
              className="absolute top-1/2 right-4 transform -translate-y-1/2 w-10 h-10 rounded-full bg-white/80 dark:bg-slate-700/80 flex items-center justify-center text-slate-700 dark:text-white shadow-md hover:bg-amber-400 dark:hover:bg-indigo-600 transition-colors duration-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
          
          {/* Dots indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  activeIndex === index 
                    ? 'bg-amber-500 dark:bg-indigo-500 w-6' 
                    : 'bg-slate-300 dark:bg-slate-600 hover:bg-amber-300 dark:hover:bg-indigo-400'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              ></button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;