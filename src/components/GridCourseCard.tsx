import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, Award } from 'lucide-react';

interface Course {
  id: number;
  slug: string;
  title: string;
  image: string;
  duration: number;
  capacity: number;
  certification: number[];
  level: string;
  description: string;
  highlights: string[];
  featured: boolean;
  active: boolean;
  price: {
    shared: number;
    private: number;
    sharedTwin: number;
  };
}

interface Certification {
  id: number;
  name: string;
  link: string;
}

interface GridCourseCardProps {
  course: Course;
  certifications: Certification[];
}

const GridCourseCard: React.FC<GridCourseCardProps> = ({ course, certifications }) => {
  const navigate = useNavigate();

  return (
    <div
      key={course.id}
      className="group bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer flex flex-col h-full relative"
      onClick={() => navigate(`/courses/${course.slug}`)}
    >
      <div className="aspect-[16/9] relative overflow-hidden bg-slate-100 dark:bg-slate-700">
        <img
          src={course.image}
          alt={course.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300 absolute inset-0"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end p-6">
          <div>
            <h3 className="text-2xl font-bold text-white mb-2">{course.title}</h3>
            <div className="flex items-center space-x-4">
              <span className="flex items-center text-amber-400">
                <Clock className="w-4 h-4 mr-1" />
                {course.duration} Days
              </span>
              <span className="flex items-center text-amber-400">
                <Award className="w-4 h-4 mr-1" />
                {course.certification
                  .map(certId => {
                    const cert = certifications.find(c => c.id === certId);
                    return cert ? cert.name : certId;
                  })
                  .join(', ')
                }
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 flex flex-col flex-1 h-full">
        <div>
          <div className="flex items-center gap-2 mb-4">
            <span className="px-3 py-1 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-sm font-medium rounded-full">
              {course.level}
            </span>
            <span className="px-3 py-1 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-sm font-medium rounded-full">
              ${course.price.shared}
            </span>
          </div>

          <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2">
            {course.description}
          </p>

          <div className="flex flex-wrap gap-2 mb-6">
            {course.highlights.slice(0, 3).map((highlight, index) => (
              <span
                key={index}
                className="text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 px-2 py-1 rounded-full"
              >
                {highlight}
              </span>
            ))}
          </div>
        </div>

        <div className="flex justify-between items-center space-x-4 mt-auto pt-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/courses/${course.slug}`);
            }}
            className="flex-1 px-4 py-2 bg-white hover:bg-slate-50 dark:bg-slate-700 dark:hover:bg-slate-600 text-amber-500 dark:text-amber-400 border border-amber-500 dark:border-amber-400 rounded-lg font-medium transition-colors duration-300 text-sm text-center"
          >
            Learn More
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              navigate('/apply');
            }}
            className="flex-1 px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm text-center"
          >
            Apply Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default GridCourseCard;
