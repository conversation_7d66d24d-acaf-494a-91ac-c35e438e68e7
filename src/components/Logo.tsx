import React from 'react';
import { <PERSON><PERSON> as <PERSON> } from 'lucide-react';
import { Link } from 'react-router-dom';

interface LogoProps {
  darkMode: boolean;
  inFooter?: boolean;
}

const Logo: React.FC<LogoProps> = ({ darkMode, inFooter = false }) => {
  return (
    <Link to="/" className="flex items-center space-x-2">
      <Lotus 
        size={32} 
        className={`${darkMode ? 'text-amber-400' : 'text-amber-500'} transition-colors duration-300`} 
      />
      <span className={`text-xl font-bold tracking-tight ${inFooter ? 'text-white' : 'text-slate-900 dark:text-white'}`}>
        Shanti<span className={`${darkMode ? 'text-amber-400' : 'text-amber-700'}`}>YogPeeth</span>
      </span>
    </Link>
  );
};

export default Logo;