import React, { useState } from 'react';
import { Clock, Award, BookOpen, ArrowRight } from 'lucide-react';

interface Teacher {
  id: number;
  name: string;
  role: string;
  bio: string;
  image: string;
  specialties: string[];
  experience: string;
}

const Teachers: React.FC = () => {
  const [activeTeacher, setActiveTeacher] = useState<number>(1);

  const teachers: Teacher[] = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      role: 'Founder & Master Yogi',
      bio: 'With over 30 years of practice and study, <PERSON><PERSON><PERSON> brings profound wisdom and authenticity to his teachings. Trained in traditional Hatha and Kundalini yoga in Rishikesh, he has dedicated his life to sharing the transformative power of yoga with practitioners from around the world.',
      image: 'https://placehold.co/400x400/e2e8f0/1e293b?text=<PERSON><PERSON><PERSON>+<PERSON>',
      specialties: ['Hatha Yoga', 'Kundalini Yoga', 'Meditation', 'Philosophy'],
      experience: '30+ years'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      role: 'Senior Yoga Instructor',
      bio: '<PERSON><PERSON> combines her background in dance and movement therapy with traditional yoga practices to create flowing, mindful sequences. Her classes focus on alignment, breath awareness, and finding joy in movement. She specializes in Vinyasa and Yin yoga.',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Priya+Patel',
      specialties: ['Vinyasa Flow', 'Yin Yoga', 'Prenatal Yoga'],
      experience: '15 years'
    },
    {
      id: 3,
      name: 'David Chen',
      role: 'Ashtanga Specialist',
      bio: 'David discovered Ashtanga yoga during a spiritual journey to India and has been devoted to the practice ever since. His teaching style is dynamic yet compassionate, helping students build strength and flexibility while honoring their bodies\' limitations.',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=David+Chen',
      specialties: ['Ashtanga Yoga', 'Power Yoga', 'Handstands'],
      experience: '12 years'
    },
    {
      id: 4,
      name: 'Lakshmi Nair',
      role: 'Meditation & Pranayama Guide',
      bio: 'Lakshmi has studied with meditation masters throughout India and Tibet. Her gentle approach to breathwork and meditation helps students quiet their minds and connect with their inner wisdom. She leads our meditation retreats and specialized pranayama workshops.',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Lakshmi+Nair',
      specialties: ['Meditation', 'Pranayama', 'Yoga Nidra', 'Sound Healing'],
      experience: '20 years'
    }
  ];

  const currentTeacher = teachers.find(t => t.id === activeTeacher) || teachers[0];

  return (
    <section id="teachers" className="py-16 md:py-24 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden -z-10">
        <div className="absolute -top-20 -right-20 w-64 h-64 md:w-96 md:h-96 bg-amber-100 dark:bg-indigo-900/20 rounded-full opacity-50 dark:opacity-30"></div>
        <div className="absolute bottom-20 -left-20 w-64 h-64 md:w-80 md:h-80 bg-amber-100 dark:bg-indigo-900/20 rounded-full opacity-50 dark:opacity-30"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 md:w-16 mr-4"></div>
            <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Our Guides</span>
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 md:w-16 ml-4"></div>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-900 dark:text-white">
            Meet Our <span className="text-amber-500 dark:text-amber-400">Expert Teachers</span>
          </h2>

          <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Our internationally certified instructors bring decades of experience and deep knowledge
            of yogic traditions to guide you on your transformative journey.
          </p>
        </div>

        {/* Teacher Card - New Design with Fixed Height */}
        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden max-w-5xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-0">
            {/* Teacher Image - Fixed size */}
            <div className="md:col-span-5 lg:col-span-4 relative">
              <div className="relative h-[300px] md:h-full">
                <img
                  src={currentTeacher.image}
                  alt={currentTeacher.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent"></div>
                <div className="absolute bottom-0 left-0 p-4 md:p-6 text-white">
                  <h3 className="text-xl md:text-2xl font-bold">{currentTeacher.name}</h3>
                  <p className="text-amber-300">{currentTeacher.role}</p>
                </div>
              </div>
            </div>

            {/* Teacher Info - Fixed height with scrollable content if needed */}
            <div className="md:col-span-7 lg:col-span-8 p-6 md:p-8 flex flex-col h-full" style={{ minHeight: '400px' }}>
              {/* Scrollable content area */}
              <div className="flex-grow overflow-y-auto" style={{ minHeight: '280px' }}>
                <p className="text-slate-700 dark:text-slate-300 mb-6 leading-relaxed">
                  {currentTeacher.bio}
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                  <div>
                    <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-3 flex items-center">
                      <BookOpen className="w-4 h-4 mr-2 text-amber-500 dark:text-amber-400" />
                      Specialties
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {currentTeacher.specialties.map((specialty, index) => (
                        <span
                          key={index}
                          className="text-xs md:text-sm bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 px-2 py-1 rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-3 flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-amber-500 dark:text-amber-400" />
                      Experience
                    </h4>
                    <p className="text-slate-600 dark:text-slate-400">
                      {currentTeacher.experience}
                    </p>
                  </div>
                </div>
              </div>

              {/* Fixed position buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mt-4 pt-4 border-t border-slate-100 dark:border-slate-700">
                <button className="flex-1 py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm md:text-base">
                  Book a Private Session
                </button>
                <a
                  href={`/teachers#${currentTeacher.id}`}
                  className="flex-1 py-2 px-4 border border-amber-400 dark:border-indigo-600 text-amber-500 dark:text-indigo-400 hover:bg-amber-50 dark:hover:bg-indigo-900/20 rounded-lg font-medium transition-colors duration-300 text-center text-sm md:text-base"
                >
                  View Full Profile
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Teacher selector - Improved */}
        <div className="mt-8 flex flex-col md:flex-row justify-center items-center gap-6">
          <div className="flex flex-wrap gap-3 justify-center">
            {teachers.map((teacher) => (
              <button
                key={teacher.id}
                onClick={() => setActiveTeacher(teacher.id)}
                className={`relative overflow-hidden rounded-full w-12 h-12 md:w-14 md:h-14 transition-all duration-300 ${
                  activeTeacher === teacher.id
                    ? 'ring-2 ring-amber-400 dark:ring-indigo-500 scale-110'
                    : 'opacity-70 hover:opacity-100'
                }`}
              >
                <img
                  src={teacher.image}
                  alt={teacher.name}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>

          {/* View All Teachers link */}
          <a
            href="/teachers"
            className="inline-flex items-center text-amber-500 dark:text-amber-400 font-medium hover:text-amber-600 dark:hover:text-amber-300 transition-colors group"
          >
            View All Teachers
            <ArrowRight className="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default Teachers;