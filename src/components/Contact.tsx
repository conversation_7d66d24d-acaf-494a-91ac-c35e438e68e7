import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Instagram, Facebook, Twitter, Loader2 } from 'lucide-react';

// Form validation schema
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  subject: z.string().min(1, 'Please select a subject'),
  message: z.string().min(10, 'Message must be at least 10 characters')
});

type ContactFormData = z.infer<typeof contactFormSchema>;

const Contact: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitStatus, setSubmitStatus] = React.useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [submitCount, setSubmitCount] = React.useState(0);
  const [lastSubmitTime, setLastSubmitTime] = React.useState(0);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema)
  });

  const checkRateLimit = () => {
    const now = Date.now();
    const timeSinceLastSubmit = now - lastSubmitTime;
    const MAX_SUBMISSIONS = 5;
    const RATE_LIMIT_WINDOW = 3600000; // 1 hour in milliseconds
    const MIN_TIME_BETWEEN_SUBMITS = 60000; // 1 minute in milliseconds

    if (timeSinceLastSubmit < MIN_TIME_BETWEEN_SUBMITS) {
      return 'Please wait at least 1 minute between submissions';
    }

    if (submitCount >= MAX_SUBMISSIONS && timeSinceLastSubmit < RATE_LIMIT_WINDOW) {
      return 'Maximum submission limit reached. Please try again later';
    }

    return null;
  };

  const onSubmit = async (data: ContactFormData) => {
    const rateLimitMessage = checkRateLimit();
    if (rateLimitMessage) {
      setSubmitStatus({
        type: 'error',
        message: rateLimitMessage
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('https://hub.rksh.in/mailer/mailer.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'contact',
          ...data
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to send message');
      }
      
      setSubmitStatus({
        type: 'success',
        message: 'Thank you for your message! We will get back to you soon.'
      });
      
      reset();
    } catch (error) {
      setSubmitStatus({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to send message. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus(null), 5000);
    }
  };

  return (
    <section id="contact" className="py-20 md:py-32 bg-amber-50/50 dark:bg-slate-800/30 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-amber-100 dark:bg-indigo-900/20 rounded-bl-full opacity-50 dark:opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-amber-100 dark:bg-indigo-900/20 rounded-tr-full opacity-50 dark:opacity-30"></div>
      
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
            <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Get in Touch</span>
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 ml-4"></div>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
            Begin Your <span className="text-amber-500 dark:text-amber-400">Yoga Journey</span>
          </h2>
          
          <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Have questions about our classes, retreats, or teacher training programs? 
            Reach out to us and start your transformative journey today.
          </p>
        </div>
        
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Contact info */}
          <div className="lg:w-1/3">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg h-full">
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-6">Contact Information</h3>
              
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mr-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-slate-900 dark:text-white">Address</h4>
                    <p className="text-slate-600 dark:text-slate-300 mt-1">
                      123 Yoga Street, Laxman Jhula<br />
                      Rishikesh, Uttarakhand 249302<br />
                      India
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mr-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-slate-900 dark:text-white">Phone</h4>
                    <p className="text-slate-600 dark:text-slate-300 mt-1">
                      +91 91058 19642<br />
                      +91 82794 76822
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mr-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-medium text-slate-900 dark:text-white">Email</h4>
                    <p className="text-slate-600 dark:text-slate-300 mt-1">
                      <EMAIL>
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <h4 className="font-medium text-slate-900 dark:text-white mb-4">Follow Us</h4>
                <div className="flex space-x-4">
                  <a 
                    href="#" 
                    className="w-10 h-10 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 hover:bg-amber-400 dark:hover:bg-indigo-600 hover:text-white dark:hover:text-white transition-colors duration-300"
                  >
                    <Instagram size={20} />
                  </a>
                  <a 
                    href="#" 
                    className="w-10 h-10 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 hover:bg-amber-400 dark:hover:bg-indigo-600 hover:text-white dark:hover:text-white transition-colors duration-300"
                  >
                    <Facebook size={20} />
                  </a>
                  <a 
                    href="#" 
                    className="w-10 h-10 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 hover:bg-amber-400 dark:hover:bg-indigo-600 hover:text-white dark:hover:text-white transition-colors duration-300"
                  >
                    <Twitter size={20} />
                  </a>
                </div>
              </div>
            </div>
          </div>
          
          {/* Contact form */}
          <div className="lg:w-2/3">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-6">Send Us a Message</h3>
              
              {submitStatus && (
                <div className={`p-4 mb-6 rounded-lg ${
                  submitStatus.type === 'success' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-300' 
                    : 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-300'
                }`}>
                  {submitStatus.message}
                </div>
              )}
              
              <form onSubmit={handleSubmit(onSubmit)} noValidate>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Your Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      {...register('name')}
                      className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                        errors.name ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
                      }`}
                    />
                    {errors.name && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      {...register('email')}
                      className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                        errors.email ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
                      }`}
                    />
                    {errors.email && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      {...register('phone')}
                      className="w-full px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Subject *
                    </label>
                    <select
                      id="subject"
                      {...register('subject')}
                      className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                        errors.subject ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
                      }`}
                    >
                      <option value="">Select a subject</option>
                      <option value="General Inquiry">General Inquiry</option>
                      <option value="Class Information">Class Information</option>
                      <option value="Teacher Training">Teacher Training</option>
                      <option value="Retreat Booking">Retreat Booking</option>
                      <option value="Other">Other</option>
                    </select>
                    {errors.subject && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.subject.message}</p>
                    )}
                  </div>
                </div>
                
                <div className="mb-6">
                  <label htmlFor="message" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Your Message *
                  </label>
                  <textarea
                    id="message"
                    {...register('message')}
                    rows={5}
                    className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                      errors.message ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
                    }`}
                  ></textarea>
                  {errors.message && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.message.message}</p>
                  )}
                </div>
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full py-3 px-4 flex items-center justify-center rounded-lg font-medium transition-colors duration-300 ${
                    isSubmitting
                      ? 'bg-amber-300 dark:bg-indigo-500 cursor-not-allowed'
                      : 'bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700'
                  } text-slate-900 dark:text-white`}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    'Send Message'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
        
        {/* Map */}
        <div className="mt-16 rounded-xl overflow-hidden shadow-lg h-96 relative">
          <iframe
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d13769.859173531701!2d78.31450672646582!3d30.120743115325506!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39093e67cf93f111%3A0xcc78804a9346b853!2sLaxman%20Jhula%2C%20Tapovan%2C%20Rishikesh%2C%20Uttarakhand%20249192!5e0!3m2!1sen!2sin!4v1653395799545!5m2!1sen!2sin"
            width="100%"
            height="100%"
            style={{ border: 0 }}
            allowFullScreen
            loading="lazy"
            referrerPolicy="no-referrer-when-downgrade"
          ></iframe>
        </div>
      </div>
    </section>
  );
};

export default Contact;