import React, { useState } from 'react';
import { Link } from 'react-router-dom';

interface YogaStyle {
  id: number;
  title: string;
  description: string;
  level: string;
  duration: string;
  image: string;
  benefits: string[];
  slug: string;
}

const YogaStyles: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('all');
  
  const styles: YogaStyle[] = [
    {
      id: 1,
      title: 'Hatha Yoga',
      description: 'Traditional yoga focusing on physical postures and breath control to balance body and mind.',
      level: 'All Levels',
      duration: '90 min',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Hatha+Yoga',
      benefits: ['Improves flexibility', 'Builds strength', 'Reduces stress', 'Enhances focus'],
      slug: 'hatha-yoga'
    },
    {
      id: 2,
      title: 'Ashtanga Yoga',
      description: 'Dynamic and physically demanding practice that synchronizes breath with a progressive series of postures.',
      level: 'Intermediate',
      duration: '120 min',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Ashtanga+Yoga',
      benefits: ['Builds core strength', 'Improves stamina', 'Detoxifies body', 'Increases focus'],
      slug: 'ashtanga-yoga'
    },
    {
      id: 3,
      title: 'Kundalini Yoga',
      description: 'Spiritual practice that awakens energy at the base of the spine through meditation, mantras, and kriyas.',
      level: 'All Levels',
      duration: '90 min',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Kundalini+Yoga',
      benefits: ['Awakens consciousness', 'Strengthens nervous system', 'Balances glandular system', 'Expands awareness'],
      slug: 'kundalini-yoga'
    },
    {
      id: 4,
      title: 'Yin Yoga',
      description: 'Slow-paced style with postures held for longer periods, targeting deep connective tissues.',
      level: 'Beginner',
      duration: '75 min',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Yin+Yoga',
      benefits: ['Increases flexibility', 'Improves joint mobility', 'Reduces stress', 'Balances energy'],
      slug: 'yin-yoga'
    },
    {
      id: 5,
      title: 'Vinyasa Flow',
      description: 'Dynamic practice coordinating movement with breath to flow from one pose to the next.',
      level: 'All Levels',
      duration: '60 min',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Vinyasa+Flow',
      benefits: ['Improves cardiovascular health', 'Builds strength', 'Enhances flexibility', 'Reduces stress'],
      slug: 'vinyasa-flow'
    },
    {
      id: 6,
      title: 'Meditation',
      description: 'Guided practice to cultivate awareness, clarity, and a peaceful state of mind.',
      level: 'All Levels',
      duration: '45 min',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Meditation',
      benefits: ['Reduces anxiety', 'Improves concentration', 'Promotes emotional health', 'Enhances self-awareness'],
      slug: 'meditation'
    }
  ];
  
  const filteredStyles = activeTab === 'all' 
    ? styles 
    : styles.filter(s => s.level.toLowerCase().includes(activeTab.toLowerCase()));
  
  return (
    <section id="yoga-styles" className="py-20 md:py-32 bg-amber-50/50 dark:bg-slate-800/30">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
            <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Explore Yoga</span>
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 ml-4"></div>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
            Ancient <span className="text-amber-500 dark:text-amber-400">Yoga Traditions</span>
          </h2>
          
          <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Discover the rich traditions and transformative practices of various yoga styles, 
            each offering unique benefits for body, mind, and spirit.
          </p>
        </div>
        
        {/* Filter tabs */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          {['all', 'beginner', 'intermediate', 'advanced'].map((level) => (
            <button
              key={level}
              onClick={() => setActiveTab(level)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                activeTab === level
                  ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                  : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
              }`}
            >
              {level.charAt(0).toUpperCase() + level.slice(1)}
            </button>
          ))}
        </div>
        
        {/* Yoga styles grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredStyles.map((style) => (
            <div 
              key={style.id}
              className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <div className="relative h-64 overflow-hidden">
                <img 
                  src={style.image} 
                  alt={style.title} 
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                />
                <div className="absolute top-4 right-4 bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white text-xs font-bold px-3 py-1 rounded-full">
                  {style.level}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">{style.title}</h3>
                <p className="text-slate-600 dark:text-slate-300 mb-4">{style.description}</p>
                
                <div className="mb-6">
                  <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-2">Benefits:</h4>
                  <div className="flex flex-wrap gap-2">
                    {style.benefits.map((benefit, index) => (
                      <span 
                        key={index}
                        className="text-xs bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 px-2 py-1 rounded-full"
                      >
                        {benefit}
                      </span>
                    ))}
                  </div>
                </div>
                
                <Link 
                  to={`/yoga-styles/${style.slug}`}
                  className="w-full py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 block text-center"
                >
                  Learn More
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default YogaStyles;