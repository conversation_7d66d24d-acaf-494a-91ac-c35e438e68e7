import React, { useRef, useState, useEffect, useCallback } from "react";
import { X, ChevronLeft, ChevronRight } from "lucide-react";

interface ImageGalleryProps {
  images: string[];
  height?: string;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  height = "h-40"
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [isHovering, setIsHovering] = useState(false);
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [modalIndex, setModalIndex] = useState(0);

  // Set initial scroll position
  useEffect(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollLeft = 0;
    }
  }, []);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const scroll = () => {
      if (isHovering || isDragging) return;
      
      const maxScroll = container.scrollWidth - container.clientWidth;
      const currentScrollPosition = container.scrollLeft;
      
      if (currentScrollPosition >= maxScroll - 10) {
        container.scrollLeft = 0;
      } else {
        container.scrollLeft = currentScrollPosition + 1;
      }
    };

    const intervalId = setInterval(scroll, 30);
    return () => clearInterval(intervalId);
  }, [isHovering, isDragging]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartX(e.pageX - (scrollContainerRef.current?.offsetLeft || 0));
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();
    const x = e.pageX - (scrollContainerRef.current.offsetLeft || 0);
    const walk = (x - startX) * 2;
    scrollContainerRef.current.scrollLeft = scrollContainerRef.current.scrollLeft - walk;
    setStartX(e.pageX - (scrollContainerRef.current.offsetLeft || 0));
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    setIsHovering(false);
  };

  const openModal = useCallback((image: string, index: number) => {
    if (!isDragging) {
      setModalImage(image);
      setModalIndex(index);
    }
  }, [isDragging]);

  const closeModal = useCallback(() => {
    setModalImage(null);
  }, []);

  const nextImage = useCallback(() => {
    setModalIndex((prev) => (prev + 1) % images.length);
    setModalImage(images[(modalIndex + 1) % images.length]);
  }, [images, modalIndex]);

  const prevImage = useCallback(() => {
    setModalIndex((prev) => (prev - 1 + images.length) % images.length);
    setModalImage(images[(modalIndex - 1 + images.length) % images.length]);
  }, [images, modalIndex]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!modalImage) return;
      
      switch (e.key) {
        case 'Escape':
          closeModal();
          break;
        case 'ArrowRight':
          nextImage();
          break;
        case 'ArrowLeft':
          prevImage();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [modalImage, modalIndex, nextImage, prevImage, closeModal]);

  if (!images.length) return null;

  return (
    <>
      <div 
        className="relative w-full overflow-hidden bg-transparent"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div
          ref={scrollContainerRef}
          className={`flex ${height} transition-all duration-300 ease-linear bg-transparent overflow-x-hidden`}
          style={{ 
            cursor: isDragging ? 'grabbing' : 'grab',
            userSelect: 'none'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
        >
          {[...images, ...images, ...images].map((src, index) => (
            <div
              key={`${src}-${index}`}
              className="flex-shrink-0 w-72 px-2 pb-2"
              onClick={() => openModal(src, index % images.length)}
            >
              <img
                src={src}
                alt={`Gallery image ${index + 1}`}
                className="w-full h-full object-cover rounded-xl shadow-md hover:opacity-90 transition-opacity cursor-pointer"
                draggable={false}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Modal */}
      {modalImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center"
          onClick={closeModal}
        >
          <div 
            className="relative w-full h-full flex items-center justify-center p-4"
            onClick={e => e.stopPropagation()}
          >
            <button
              onClick={prevImage}
              className="fixed left-6 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-200 hover:scale-110"
              aria-label="Previous image"
            >
              <ChevronLeft className="w-8 h-8" />
            </button>

            <div className="max-w-7xl w-full h-full flex items-center justify-center">
              <img
                src={modalImage}
                alt={`Gallery image ${modalIndex + 1}`}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
              />
            </div>

            <button
              onClick={nextImage}
              className="fixed right-6 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-200 hover:scale-110"
              aria-label="Next image"
            >
              <ChevronRight className="w-8 h-8" />
            </button>

            <button
              onClick={closeModal}
              className="fixed top-6 right-6 w-12 h-12 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-200"
              aria-label="Close modal"
            >
              <X className="w-8 h-8" />
            </button>
          </div>
        </div>
      )}
    </>
  );
};

export default ImageGallery;
