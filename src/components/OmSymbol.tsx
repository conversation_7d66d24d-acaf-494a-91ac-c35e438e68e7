import React from 'react';
import { motion } from 'framer-motion';

const OmSymbol: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  const pathVariants = {
    hidden: { pathLength: 0, opacity: 0 },
    visible: { 
      pathLength: 1, 
      opacity: [0, 0.8, 0],
      transition: { 
        duration: 2,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pointer-events-none z-50"
      initial={false}
      animate={isVisible ? "visible" : "hidden"}
    >
      <motion.svg
        width="200"
        height="200"
        viewBox="0 0 100 100"
        className="text-amber-400 dark:text-indigo-400"
      >
        <motion.path
          d="M50 10c-22.1 0-40 17.9-40 40s17.9 40 40 40 40-17.9 40-40-17.9-40-40-40zm0 5c19.3 0 35 15.7 35 35s-15.7 35-35 35-35-15.7-35-35 15.7-35 35-35z"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          variants={pathVariants}
        />
        <motion.path
          d="M50 30c-11 0-20 9-20 20s9 20 20 20 20-9 20-20-9-20-20-20zm0 5c8.3 0 15 6.7 15 15s-6.7 15-15 15-15-6.7-15-15 6.7-15 15-15z"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          variants={pathVariants}
        />
        <motion.path
          d="M50 40c-5.5 0-10 4.5-10 10s4.5 10 10 10 10-4.5 10-10-4.5-10-10-10zm0 5c2.8 0 5 2.2 5 5s-2.2 5-5 5-5-2.2-5-5 2.2-5 5-5z"
          fill="none"
          stroke="currentColor"
          strokeWidth="1"
          variants={pathVariants}
        />
      </motion.svg>
    </motion.div>
  );
};

export default OmSymbol;