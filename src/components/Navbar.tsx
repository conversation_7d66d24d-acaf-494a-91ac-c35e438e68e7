import React, { useState, useEffect, useRef } from 'react';
import { Menu, X, Sun, Moon, ChevronDown, Phone, MessageSquare, Home } from 'lucide-react';
import { Link, useLocation, Location } from 'react-router-dom';
import Logo from './Logo';
import clsx from 'clsx'; // npm install clsx if not present


interface NavbarProps {
  darkMode: boolean;
  setDarkMode: (darkMode: boolean) => void;
  scrollPosition: number;
}

interface NavLink {
  name: string;
  href: string;
  children?: { name: string; href: string }[];
}

const SubMenuLink: React.FC<{ child: { name: string; href: string }; location: Location }> = ({ child, location }) => (
  <Link
    to={child.href}
    className={clsx(
      'block px-4 py-2 text-sm transition-colors duration-300',
      location.pathname === child.href
        ? 'text-amber-900 dark:text-amber-400 bg-amber-50 dark:bg-slate-700'
        : 'text-slate-700 dark:text-slate-200',
      'hover:bg-amber-50 dark:hover:bg-slate-700'
    )}
  >
    {child.name}
  </Link>
);

const MainMenuLink: React.FC<{
  link: NavLink;
  location: Location;
  handleMouseEnter: (menuName: string) => void;
  handleMouseLeave: (menuName: string) => void;
  openMenus: string[];
  dropdownRefs: React.MutableRefObject<{ [key: string]: HTMLDivElement | null }>;
}> = ({ link, location, handleMouseEnter, handleMouseLeave, openMenus, dropdownRefs }) => {
  const isMenuOpen = openMenus.includes(link.name);
  const isActive = link.children
    ? link.children.some(child => location.pathname === child.href)
    : location.pathname === link.href;

  if (link.children) {
    return (
      <div
        className="relative"
        ref={el => (dropdownRefs.current[link.name] = el)}
      >
        <button
          onMouseEnter={() => handleMouseEnter(link.name)}
          onMouseLeave={() => handleMouseLeave(link.name)}
          className={clsx(
            'flex items-center text-base font-medium transition-colors duration-300',
            isActive ? 'text-amber-700 dark:text-amber-400' : 'text-slate-900 dark:text-slate-200',
            'hover:text-amber-700 dark:hover:text-amber-400'
          )}
        >
          {link.name}
          <ChevronDown className={clsx('ml-1 w-4 h-4 transition-transform duration-200', isMenuOpen && 'rotate-180')} />
        </button>
        {isMenuOpen && (
          <div
            onMouseEnter={() => handleMouseEnter(link.name)}
            onMouseLeave={() => handleMouseLeave(link.name)}
            className="absolute top-0 left-0 pt-6 w-48"
          >
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg py-2">
              {link.children.map(child => (
                <SubMenuLink key={child.name} child={child} location={location} />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <Link
      to={link.href}
      className={clsx(
        'relative text-base font-medium transition-colors duration-300',
        location.pathname === link.href ? 'text-amber-700 dark:text-amber-400' : 'text-slate-900 dark:text-slate-200',
        'hover:text-amber-700 dark:hover:text-amber-400'
      )}
    >
      {link.name}
      {location.pathname === link.href && (
        <div className="absolute -bottom-1 left-0 w-full h-0.5 bg-amber-400 dark:bg-amber-400"></div>
      )}
    </Link>
  );
};

const Navbar: React.FC<NavbarProps> = ({ darkMode, setDarkMode, scrollPosition }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [openMenus, setOpenMenus] = useState<string[]>([]);
  const [menuStack, setMenuStack] = useState<number[]>([0]); // For mobile menu navigation
  const location = useLocation();
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Removed unused toggleMenu

  const isScrolled = scrollPosition > 50;

  const handleMouseEnter = (menuName: string) => {
    setOpenMenus((prev) => [...prev, menuName]);
  };

  const handleMouseLeave = (menuName: string) => {
    setOpenMenus((prev) => prev.filter((name) => name !== menuName));
  };

  const navLinks: NavLink[] = [
    { name: 'Home', href: '/' },
    {
      name: 'About',
      href: '#',
      children: [
        { name: 'About Us', href: '/about' },
        { name: 'Teachers', href: '/teachers' },
        { name: 'Impact', href: '/impact' },
        { name: 'Gallery', href: '/gallery' },
      ],
    },
    { name: 'New To YOGA', href: '/begin-yoga' },
    { name: 'Digital Ashram', href: '/digitalashram' },
    {
      name: 'Services',
      href: '#',
      children: [
        { name: 'Calendar', href: '/calendar' },
        { name: 'Courses', href: '/courses' },
        { name: 'Retreats', href: '/retreats' },
        { name: 'Wellness', href: '/wellness' },
        { name: 'Online Services', href: '/online-services' },
        { name: 'Resources', href: '/resources' },
        { name: 'Test Components', href: '/test' },
      ],
    },
    {
      name: 'Contact',
      href: '#',
      children: [
        { name: 'Contact Us', href: '/contact' },
        { name: 'Career Center', href: '/careers' },
        { name: 'Help Center', href: '/faq' },
      ],
    },
  ];

  useEffect(() => {
    // Close mobile menu when route changes
    setIsOpen(false);
    setOpenMenus([]);
  }, [location]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openMenus.length > 0) {
        const isClickInside = Object.values(dropdownRefs.current).some(
          (ref) => ref && ref.contains(event.target as Node)
        );
        if (!isClickInside) {
          setOpenMenus([]);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [openMenus]);

  // renderLink is now replaced by MainMenuLink and SubMenuLink components above.

  return (
    <>
      {/* Navigation Bar */}
      <nav
        className={`fixed w-full z-40 transition-all duration-500 ${
          isScrolled
            ? 'py-2 bg-white/70 dark:bg-slate-900/80 backdrop-blur-md shadow-md'  // Scrolled state for all screens
            : 'py-4 bg-white/90 dark:bg-slate-900/90 lg:bg-transparent lg:dark:bg-transparent'  // Not scrolled: no bg on large screens
        }`}
        style={{ '--navbar-height': isScrolled ? '64px' : '80px' } as React.CSSProperties}
      >
        <div className="container mx-auto px-4 md:px-6 flex justify-center items-center lg:justify-between">
          <div className="flex lg:flex-none">
            <Logo darkMode={darkMode} />
          </div>
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <MainMenuLink
                key={link.name}
                link={link}
                location={location}
                handleMouseEnter={handleMouseEnter}
                handleMouseLeave={handleMouseLeave}
                openMenus={openMenus}
                dropdownRefs={dropdownRefs}
              />
            ))}
            <button
              onClick={() => setDarkMode(!darkMode)}
              className="p-2 rounded-lg bg-white/10 hover:bg-white/20 dark:bg-slate-800/50 dark:hover:bg-slate-800 transition-colors duration-300"
              aria-label="Toggle dark mode"
            >
              {darkMode ? (
                <Sun className="w-5 h-5 text-amber-400" />
              ) : (
                <Moon className="w-5 h-5 text-slate-700" />
              )}
            </button>
            <Link
              to="/apply"
              className="px-6 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300"
            >
              Apply Now
            </Link>
          </div>
        </div>
      </nav>


      {/* Mobile Menu System (NEW) */}
      {/* Only rendered on mobile screens */}
      {isOpen && (
        <>
          {/* Overlay */}
          <div
            className="lg:hidden fixed inset-0 z-40 bg-black/40 backdrop-blur-sm transition-opacity duration-300"
            onClick={() => {
              setIsOpen(false);
              setMenuStack([0]);
            }}
            aria-label="Close menu overlay"
          ></div>
          {/* Side Menu Panel */}
          <div
            className={`lg:hidden fixed bottom-16 right-2 z-50 w-72 max-w-full max-h-[80vh] bg-white dark:bg-slate-900 rounded-2xl shadow-2xl flex flex-col transition-transform duration-300 ${
              isOpen ? 'translate-y-0' : 'translate-y-full'
            }`}
            tabIndex={-1}
            role="dialog"
            aria-modal="true"
          >
            {/* Header */}
            <div className="flex items-center justify-between px-4 py-2 border-b border-slate-200 dark:border-slate-800">
              {menuStack.length > 1 ? (
                <button
                  onClick={() => setMenuStack((stack) => stack.slice(0, -1))}
                  className="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800"
                  aria-label="Back"
                >
                  <ChevronDown className="w-6 h-6 rotate-90 text-slate-700 dark:text-slate-200" />
                </button>
              ) : (
                <span />
              )}
              <h3 className="text-lg font-medium text-slate-800 dark:text-slate-100 text-center flex-1">
                {menuStack.length === 1
                  ? 'Menu'
                  : navLinks[menuStack[menuStack.length - 1]].name}
              </h3>
              <button
                onClick={() => {
                  setIsOpen(false);
                  setMenuStack([0]);
                }}
                className="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-800"
                aria-label="Close menu"
              >
                <X className="w-6 h-6 text-slate-700 dark:text-slate-200" />
              </button>
            </div>
            {/* Menu Content */}
            <div className="flex-1 overflow-y-auto px-2 py-4">
              {(() => {
                // Determine which menu to show based on stack
                const currentIndex = menuStack[menuStack.length - 1];
                const currentMenu =
                  menuStack.length === 1
                    ? navLinks
                    : navLinks[currentIndex].children || [];
                return currentMenu.map((item) => {
                  const hasChildren = 'children' in item && item.children && Array.isArray(item.children) && item.children.length > 0;
                  return (
                    <div key={item.name} className="mb-2">
                      {hasChildren ? (
                        <button
                          className={`w-full flex justify-between items-center px-4 py-1 rounded-lg text-base font-medium transition
                            ${Array.isArray(item.children) && item.children.some((child: { href: string }) => location.pathname === child.href)
                              ? 'text-amber-500 dark:text-amber-400 bg-amber-50 dark:bg-slate-700'
                              : 'text-slate-800 dark:text-slate-100 bg-slate-50 dark:bg-slate-800 hover:bg-amber-50 dark:hover:bg-slate-700'
                            }`}
                          onClick={() => {
                            if (menuStack.length === 1) {
                              setMenuStack([...menuStack, navLinks.findIndex(l => l.name === item.name)]);
                            } else {
                              // For deeper submenus, not expected in current data
                            }
                          }}
                        >
                          <span>{item.name}</span>
                          <ChevronDown className="w-5 h-5 rotate-0 text-slate-400 dark:text-slate-500" />
                        </button>
                      ) : (
                        <Link
                          to={item.href}
                          className={`block w-full text-left px-4 py-1 rounded-lg text-base font-medium transition
                            ${location.pathname === item.href
                              ? 'text-amber-500 dark:text-amber-400 bg-amber-50 dark:bg-slate-700'
                              : 'text-slate-800 dark:text-slate-100 bg-slate-50 dark:bg-slate-800 hover:bg-amber-50 dark:hover:bg-slate-700'
                            }`}
                          onClick={() => {
                            setIsOpen(false);
                            setMenuStack([0]);
                          }}
                        >
                          {item.name}
                        </Link>
                      )}
                    </div>
                  );
                });
              })()}
            </div>
            {/* Apply Now Button */}
            <div className="p-4 border-t border-slate-200 dark:border-slate-800">
              <Link
                to="/apply"
                className="block w-full text-center px-6 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300"
                onClick={() => {
                  setIsOpen(false);
                  setMenuStack([0]);
                }}
              >
                Apply Now
              </Link>
            </div>
          </div>
        </>
      )}

      {/* Mobile Bottom Bar */}
      <div
        className={`lg:hidden fixed bottom-0 left-0 right-0 backdrop-blur-sm z-50 border-t ${
          darkMode ? 'bg-slate-900/90 border-slate-800' : 'bg-white/90 border-slate-200'
        }`}
      >
        <div
          className={`grid grid-cols-5 divide-x ${
            darkMode ? 'divide-slate-800' : 'divide-slate-200'
          }`}
        >
          {/* Home Button */}
          <Link
            to="/"
            className={`p-3 flex items-center justify-center transition-colors ${
              darkMode ? 'hover:bg-slate-800' : 'hover:bg-slate-100'
            }`}
            aria-label="Home"
          >
            <Home className={`w-6 h-6 ${darkMode ? 'text-slate-200' : 'text-slate-700'}`} />
          </Link>

          {/* Call Button */}
          <a
            href="tel:+918762101031"
            className={`p-3 flex items-center justify-center transition-colors ${
              darkMode ? 'hover:bg-slate-800' : 'hover:bg-slate-100'
            }`}
          >
            <Phone className={`w-6 h-6 ${darkMode ? 'text-slate-200' : 'text-slate-700'}`} />
          </a>

          {/* WhatsApp */}
          <a
            href="https://wa.me/918762101031"
            target="_blank"
            rel="noopener noreferrer"
            className={`p-3 flex items-center justify-center transition-colors ${
              darkMode ? 'hover:bg-slate-800' : 'hover:bg-slate-100'
            }`}
          >
            <MessageSquare className={`w-6 h-6 ${darkMode ? 'text-slate-200' : 'text-slate-700'}`} />
          </a>

          {/* Dark Mode Toggle */}
          <button
            onClick={() => setDarkMode(!darkMode)}
            className={`p-3 flex items-center justify-center transition-colors ${
              darkMode ? 'hover:bg-slate-800' : 'hover:bg-slate-100'
            }`}
            aria-label="Toggle dark mode"
          >
            {darkMode ? (
              <Sun className={`w-6 h-6 ${darkMode ? 'text-amber-400' : 'text-amber-500'}`} />
            ) : (
              <Moon className={`w-6 h-6 ${darkMode ? 'text-slate-200' : 'text-slate-700'}`} />
            )}
          </button>

          {/* Menu Toggle */}
          <button
            onClick={() => setIsOpen((open) => !open)}
            className={`p-3 flex items-center justify-center transition-colors ${
              darkMode ? 'hover:bg-slate-800' : 'hover:bg-slate-100'
            }`}
            aria-label={isOpen ? "Close menu" : "Open menu"}
          >
            {isOpen ? (
              <X className={`w-6 h-6 ${darkMode ? 'text-slate-200' : 'text-slate-700'}`} />
            ) : (
              <Menu className={`w-6 h-6 ${darkMode ? 'text-slate-200' : 'text-slate-700'}`} />
            )}
          </button>
        </div>
      </div>
    </>
  );
};

export default Navbar;
