import React, { useState } from 'react';
import { Instagram, Facebook, Twitter, Mail, Phone, MapPin, Youtube, Clock, Calendar, BookOpen, Award, Heart, Users, Loader2 } from 'lucide-react';
import Logo from './Logo';
import { Link } from 'react-router-dom';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);
    try {
      const response = await fetch('https://hub.rksh.in/mailer/mailer.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'newsletter',
          email
        })
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to subscribe');
      }

      setSubmitStatus({
        type: 'success',
        message: 'Thank you for subscribing to our newsletter!'
      });
      
      setEmail('');
    } catch (error) {
      setSubmitStatus({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to subscribe. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus(null), 5000);
    }
  };
  
  return (
    <footer className="bg-slate-900 text-white">
      {/* Newsletter Section */}
      <div className="bg-amber-400 dark:bg-indigo-700 py-12">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8">
            <div className="md:w-1/2">
              <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">Join Our Yoga Community</h3>
              <p className="text-slate-800 dark:text-slate-100">
                Subscribe to our newsletter for exclusive yoga tips, retreat updates, and special offers.
              </p>
              {submitStatus && (
                <p className={`mt-2 ${
                  submitStatus.type === 'success' 
                    ? 'text-green-800 dark:text-green-300' 
                    : 'text-red-800 dark:text-red-300'
                }`}>
                  {submitStatus.message}
                </p>
              )}
            </div>
            <div className="md:w-1/2">
              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-3 w-full">
                <input 
                  type="email" 
                  id="newsletter-email"
                  name="newsletter-email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Your email address" 
                  className="flex-grow px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-900 dark:focus:ring-white"
                  required
                />
                <button 
                  type="submit"
                  disabled={isSubmitting}
                  className={`px-6 py-3 bg-slate-900 text-white dark:bg-white dark:text-slate-900 rounded-lg font-medium transition-colors duration-300 flex items-center justify-center ${
                    isSubmitting ? 'opacity-75 cursor-not-allowed' : 'hover:bg-slate-800 dark:hover:bg-slate-100'
                  }`}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Subscribing...
                    </>
                  ) : (
                    'Subscribe'
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
      
      {/* Main Footer Content */}
      <div className="pt-16 pb-12">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
            <div>
              <Logo darkMode={true} inFooter={true} />
              <p className="mt-4 text-slate-400 leading-relaxed">
                Discover authentic yoga practices in the spiritual heart of Rishikesh. 
                Our school offers transformative experiences for practitioners of all levels.
              </p>
              <div className="flex space-x-4 mt-6">
                <a 
                  href="#" 
                  className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:bg-amber-400 hover:text-white transition-colors duration-300"
                >
                  <Instagram size={20} />
                </a>
                <a 
                  href="#" 
                  className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:bg-amber-400 hover:text-white transition-colors duration-300"
                >
                  <Facebook size={20} />
                </a>
                <a 
                  href="#" 
                  className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:bg-amber-400 hover:text-white transition-colors duration-300"
                >
                  <Twitter size={20} />
                </a>
                <a 
                  href="#" 
                  className="w-10 h-10 bg-slate-800 rounded-full flex items-center justify-center text-slate-400 hover:bg-amber-400 hover:text-white transition-colors duration-300"
                >
                  <Youtube size={20} />
                </a>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Quick Links</h3>
              <ul className="space-y-4">
                <li>
                  <Link to="/" className="text-slate-400 hover:text-amber-400 transition-colors duration-300 flex items-center">
                    <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-2"></span>
                    Home
                  </Link>
                </li>
                <li>
                  <Link to="/about" className="text-slate-400 hover:text-amber-400 transition-colors duration-300 flex items-center">
                    <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-2"></span>
                    About Us
                  </Link>
                </li>
                <li>
                  <Link to="/courses" className="text-slate-400 hover:text-amber-400 transition-colors duration-300 flex items-center">
                    <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-2"></span>
                    Courses
                  </Link>
                </li>
                <li>
                  <Link to="/retreats" className="text-slate-400 hover:text-amber-400 transition-colors duration-300 flex items-center">
                    <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-2"></span>
                    Retreats
                  </Link>
                </li>
                <li>
                  <Link to="/wellness" className="text-slate-400 hover:text-amber-400 transition-colors duration-300 flex items-center">
                    <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-2"></span>
                    Wellness Services
                  </Link>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Our Programs</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <BookOpen className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                  <div>
                    <span className="text-white font-medium block">200-Hour TTC</span>
                    <span className="text-slate-400 text-sm">Yoga Alliance Certified</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <BookOpen className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                  <div>
                    <span className="text-white font-medium block">300-Hour TTC</span>
                    <span className="text-slate-400 text-sm">Advanced Training</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <Heart className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                  <div>
                    <span className="text-white font-medium block">Meditation Retreats</span>
                    <span className="text-slate-400 text-sm">7-14 Day Programs</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <Users className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                  <div>
                    <span className="text-white font-medium block">Group Retreats</span>
                    <span className="text-slate-400 text-sm">Customized Experiences</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <Award className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                  <div>
                    <span className="text-white font-medium block">Ayurveda Workshops</span>
                    <span className="text-slate-400 text-sm">Holistic Wellness</span>
                  </div>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-bold mb-6 text-white">Contact Us</h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <MapPin className="w-5 h-5 text-amber-400 mr-3 mt-0.5" />
                  <span className="text-slate-400">
                    123 Yoga Street, Laxman Jhula<br />
                    Rishikesh, Uttarakhand 249302<br />
                    India
                  </span>
                </li>
                <li className="flex items-center">
                  <Phone className="w-5 h-5 text-amber-400 mr-3" />
                  <span className="text-slate-400">+91 98765 43210</span>
                </li>
                <li className="flex items-center">
                  <Mail className="w-5 h-5 text-amber-400 mr-3" />
                  <span className="text-slate-400"><EMAIL></span>
                </li>
                <li className="flex items-center">
                  <Clock className="w-5 h-5 text-amber-400 mr-3" />
                  <div className="text-slate-400">
                    <span className="block">Monday - Saturday</span>
                    <span className="block">6:00 AM - 7:00 PM</span>
                  </div>
                </li>
              </ul>
              
              <div className="mt-6 p-4 bg-slate-800 rounded-lg">
                <h4 className="text-white font-medium mb-2">Emergency Contact</h4>
                <p className="text-slate-400 text-sm">For urgent inquiries:</p>
                <p className="text-amber-400 font-medium">+91 98765 43211</p>
              </div>
            </div>
          </div>
          
          {/* Additional Footer Sections */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 py-8 border-t border-slate-800">
            <div>
              <h4 className="text-white font-medium mb-4">Certifications</h4>
              <div className="flex flex-wrap gap-4">
                <div className="bg-slate-800 p-3 rounded-lg">
                  <span className="text-amber-400 text-xs block">CERTIFIED</span>
                  <span className="text-white text-sm">Yoga Alliance</span>
                </div>
                <div className="bg-slate-800 p-3 rounded-lg">
                  <span className="text-amber-400 text-xs block">MEMBER</span>
                  <span className="text-white text-sm">Int'l Yoga Federation</span>
                </div>
                <div className="bg-slate-800 p-3 rounded-lg">
                  <span className="text-amber-400 text-xs block">VERIFIED</span>
                  <span className="text-white text-sm">Ministry of AYUSH</span>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">Upcoming Events</h4>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <Calendar className="w-4 h-4 text-amber-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-white text-sm block">Summer Solstice Retreat</span>
                    <span className="text-slate-400 text-xs">June 21, 2025</span>
                  </div>
                </li>
                <li className="flex items-start">
                  <Calendar className="w-4 h-4 text-amber-400 mr-2 mt-0.5" />
                  <div>
                    <span className="text-white text-sm block">200-Hour TTC Batch</span>
                    <span className="text-slate-400 text-xs">Starts July 15, 2025</span>
                  </div>
                </li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-white font-medium mb-4">We Accept</h4>
              <div className="flex flex-wrap gap-2">
                <div className="bg-slate-800 px-3 py-2 rounded text-white text-sm">Visa</div>
                <div className="bg-slate-800 px-3 py-2 rounded text-white text-sm">Mastercard</div>
                <div className="bg-slate-800 px-3 py-2 rounded text-white text-sm">PayPal</div>
                <div className="bg-slate-800 px-3 py-2 rounded text-white text-sm">Bank Transfer</div>
              </div>
            </div>
          </div>
          
          <div className="border-t border-slate-800 pt-8 mt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-slate-500 text-sm mb-4 md:mb-0">
                &copy; {currentYear} Shanti Yog Peeth. All rights reserved.
              </p>
              <div className="flex space-x-6">
                <Link to="/privacy-policy" className="text-slate-500 hover:text-amber-400 text-sm transition-colors duration-300">
                  Privacy Policy
                </Link>
                <Link to="/terms-of-service" className="text-slate-500 hover:text-amber-400 text-sm transition-colors duration-300">
                  Terms of Service
                </Link>
                <a href="#" className="text-slate-500 hover:text-amber-400 text-sm transition-colors duration-300">
                  Sitemap
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
