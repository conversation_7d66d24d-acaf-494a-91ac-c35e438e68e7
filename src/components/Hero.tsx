import React from 'react';

const Hero: React.FC = () => {

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-cover bg-center"
      style={{
        backgroundImage: 'url("/images/hero.webp")'
      }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-amber-900/40 via-amber-800/30 to-slate-900/70 dark:from-slate-900/70 dark:via-indigo-900/50 dark:to-slate-900/80"></div>

      {/* Animated circles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -left-20 w-96 h-96 bg-amber-400/20 dark:bg-indigo-600/20 rounded-full blur-3xl animate-blob"></div>
        <div className="absolute top-1/3 -right-20 w-80 h-80 bg-amber-500/20 dark:bg-indigo-500/20 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-20 left-1/3 w-72 h-72 bg-amber-300/20 dark:bg-indigo-700/20 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
      </div>

      <div
        className="container mx-auto px-4 md:px-6 relative z-10 text-center"
      >
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight leading-tight">
          Discover Inner Peace in <br />
          <span className="text-amber-400 dark:text-amber-300">Rishikesh</span>
        </h1>
        <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-10">
          Immerse yourself in authentic yoga practices at the foothills of the Himalayas,
          where ancient wisdom meets modern wellness.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <a
            href="#yoga-styles"
            className="px-8 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-full font-medium transition-all duration-300 transform hover:scale-105"
          >
            Explore Yoga Styles
          </a>
          <a
            href="#about"
            className="px-8 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white border border-white/30 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
          >
            Learn More
          </a>
        </div>
      </div>
    </section>
  );
};

export default Hero;