import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { ChevronDown, ChevronUp, X, Filter, DollarSign, Clock } from 'lucide-react';

interface FilterBarProps {
  eventTypes: string[];
  services: {
    courses: Array<{ id: number; title: string }>;
    retreats: Array<{ id: number; title: string }>;
  };
  onEventTypeChange: (type: string) => void;
  onServiceChange: (service: string) => void;
  onDurationChange?: (duration: string) => void;
  onPriceRangeChange?: (range: [number, number]) => void;
  selectedDuration?: string;
  selectedPriceRange?: [number, number];
}

interface FilterSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  isOpen: boolean;
}

interface ControlledFilterBarProps extends FilterBarProps {
  selectedEventType: string;
  selectedService: string;
}

const FilterBar: React.FC<ControlledFilterBarProps> = ({
  eventTypes,
  services,
  onEventTypeChange,
  onService<PERSON>hange,
  onDurationChange,
  onPriceRangeChange,
  selectedEventType = 'all',
  selectedService = 'all',
  selectedDuration = 'all',
  selectedPriceRange = [0, 5000]
}) => {
  // Fully controlled: selectedEventType and selectedService now come from props
  const [activeFilters, setActiveFilters] = useState<{[key: string]: string}>({});

  // Filter sections state (expanded/collapsed)
  const [sections, setSections] = useState<FilterSection[]>([
    { id: 'type', title: 'Program Type', icon: <Filter className="w-4 h-4" />, isOpen: true },
  ]);

  interface FilterOption {
    id: string;
    label: string;
  }

  // Duration options - wrapped in useMemo to avoid recreation on every render
  const durationOptions = useMemo<FilterOption[]>(() => [
    { id: 'all', label: 'Any' },
    { id: 'short', label: '1-7' },
    { id: 'medium', label: '7-21' },
    { id: 'long', label: '21+' }
  ], []);

  // Location options removed as requested

  // Function to update active filters - wrapped in useCallback
  const updateActiveFilters = useCallback((key: string, value: string) => {
    setActiveFilters(prev => {
      if (value === 'all' || value === 'Any Duration' || value === 'Any Location') {
        const newFilters = {...prev};
        delete newFilters[key];
        return newFilters;
      }
      return {...prev, [key]: value};
    });
  }, []);

  // Sync activeFilters with all controlled props
  useEffect(() => {
    // Type
    const displayValue = eventTypes.find(t => t.toLowerCase().replace(' ', '-') === selectedEventType) || 'All';
    updateActiveFilters('type', displayValue);
    // Service
    if (selectedService && selectedService !== 'all') {
      const serviceList = selectedEventType === 'courses' ? services.courses : services.retreats;
      const serviceTitle = serviceList.find(s => s.id.toString() === selectedService)?.title || '';
      updateActiveFilters('service', serviceTitle);
    } else {
      updateActiveFilters('service', 'all');
    }
    // Duration
    if (selectedDuration !== 'all') {
      const durationLabel = durationOptions.find(d => d.id === selectedDuration)?.label;
      if (durationLabel) updateActiveFilters('duration', durationLabel);
    } else {
      updateActiveFilters('duration', 'Any');
    }
    // Price
    if (selectedPriceRange[0] > 0 || selectedPriceRange[1] < 5000) {
      updateActiveFilters('price', `$${selectedPriceRange[0]} - $${selectedPriceRange[1]}`);
    } else {
      updateActiveFilters('price', 'all');
    }
  }, [selectedEventType, selectedService, selectedDuration, selectedPriceRange, eventTypes, services, durationOptions, updateActiveFilters]);


  const handleEventTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onEventTypeChange(value);
    onServiceChange('all'); // Reset service filter

    // Update active filters
    const displayValue = eventTypes.find(t => t.toLowerCase().replace(' ', '-') === value) || 'All';
    updateActiveFilters('type', displayValue);

    // Remove service from active filters when changing event type
    setActiveFilters(prev => {
      const newFilters = {...prev};
      delete newFilters['service'];
      return newFilters;
    });
  };

  const handleServiceChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    onServiceChange(value);

    // Update active filters
    if (value === 'all') {
      updateActiveFilters('service', 'all');
    } else {
      const serviceList = selectedEventType === 'courses' ? services.courses : services.retreats;
      const serviceTitle = serviceList.find(s => s.id.toString() === value)?.title || '';
      updateActiveFilters('service', serviceTitle);
    }
  };

  const handleDurationChange = (duration: string) => {
    if (onDurationChange) {
      onDurationChange(duration);

      // Update active filters
      const durationLabel = durationOptions.find(d => d.id === duration)?.label || 'Any Duration';
      updateActiveFilters('duration', durationLabel);


    }
  };

  const handlePriceRangeChange = (min: number, max: number) => {
    if (onPriceRangeChange) {
      const range: [number, number] = [min, max];
      onPriceRangeChange(range);

      // Update active filters
      if (min > 0 || max < 5000) {
        updateActiveFilters('price', `$${min} - $${max}`);
      } else {
        updateActiveFilters('price', 'all');
      }


    }
  };

  // Location handling removed as requested

  const getServiceOptions = () => {
    switch (selectedEventType) {
      case 'courses':
        return services.courses;
      case 'retreats':
        return services.retreats;
      default:
        return [];
    }
  };

  const toggleSection = (sectionId: string) => {
    setSections(sections.map(section =>
      section.id === sectionId
        ? { ...section, isOpen: !section.isOpen }
        : section
    ));
  };

  const removeFilter = (key: string) => {
    switch (key) {
      case 'type':
        onEventTypeChange('all');
        onServiceChange('all');
        break;
      case 'service':
        onServiceChange('all');

        break;
      case 'duration':
        if (onDurationChange) {
          onDurationChange('all');

        }
        break;
      case 'price':
        if (onPriceRangeChange) {
          onPriceRangeChange([0, 5000]);

        }
        break;
    }

    // Remove from active filters
    setActiveFilters(prev => {
      const newFilters = {...prev};
      delete newFilters[key];
      return newFilters;
    });
  };

  const clearAllFilters = () => {
    onEventTypeChange('all');
    onServiceChange('all');
    if (onDurationChange) onDurationChange('all');
    if (onPriceRangeChange) onPriceRangeChange([0, 5000]);
    setActiveFilters({});
  };

  return (
    <div className="space-y-4">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-base font-bold text-slate-900 dark:text-white">Filters</h3>
        {Object.keys(activeFilters).length > 0 && (
          <button
            onClick={clearAllFilters}
            className="text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      {/* Active Filters */}
      {Object.keys(activeFilters).length > 0 && (
        <div className="flex flex-wrap gap-1 pb-3 border-b border-slate-200 dark:border-slate-700">
          {Object.entries(activeFilters).map(([key, value]) => (
            <div
              key={key}
              className="flex items-center gap-1 px-2 py-0.5 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-xs font-medium rounded-full"
            >
              <span>{value}</span>
              <button
                onClick={() => removeFilter(key)}
                className="ml-1 text-amber-800 dark:text-amber-300 hover:text-amber-900 dark:hover:text-amber-200"
              >
                <X className="w-2.5 h-2.5" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Program Type Filter Section */}
      <div className="border-b border-slate-200 dark:border-slate-700 pb-3">
        <button
          onClick={() => toggleSection('type')}
          className="flex items-center justify-between w-full text-left py-1.5 focus:outline-none"
        >
          <div className="flex items-center gap-1.5">
            <Filter className="w-4 h-4" />
            <span className="font-medium text-slate-900 dark:text-white">Program Type</span>
          </div>
          {sections[0].isOpen ? (
            <ChevronUp className="w-3.5 h-3.5 text-slate-500 dark:text-slate-400" />
          ) : (
            <ChevronDown className="w-3.5 h-3.5 text-slate-500 dark:text-slate-400" />
          )}
        </button>

        {sections[0].isOpen && (
          <div className="mt-2 pl-5">
            <div className="space-y-3">
              {/* Primary Dropdown - Event Type */}
              <div>
                <label htmlFor="eventType" className="block font-medium text-slate-700 dark:text-slate-300 mb-1">
                  Event Type
                </label>
                <select
                  id="eventType"
                  value={selectedEventType}
                  onChange={handleEventTypeChange}
                  className="w-full px-2 py-1.5 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-amber-500 dark:focus:ring-indigo-500"
                >
                  {eventTypes.map((type) => (
                    <option key={type} value={type.toLowerCase().replace(' ', '-')}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              {/* Secondary Dropdown - Service (conditionally shown) */}
              {['courses', 'retreats'].includes(selectedEventType) && (
                <div>
                  <label htmlFor="service" className="block font-medium text-slate-700 dark:text-slate-300 mb-1">
                    {selectedEventType === 'courses' ? 'Course' : 'Retreat'}
                  </label>
                  <select
                    id="service"
                    value={selectedService}
                    onChange={handleServiceChange}
                    className="w-full px-2 py-1.5 border border-slate-300 dark:border-slate-600 rounded-md bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-amber-500 dark:focus:ring-indigo-500"
                  >
                    <option value="all">All {selectedEventType}</option>
                    {getServiceOptions().map((service) => (
                      <option key={service.id} value={service.id.toString()}>
                        {service.title}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Duration Filter - Compact Row */}
      <div className="border-b border-slate-200 dark:border-slate-700 pb-3">
        <div className="flex items-center">
          <Clock className="w-4 h-4 mr-3 text-slate-500 dark:text-slate-400" />
          <div className="grid grid-cols-4 gap-1 w-full">
            {durationOptions.map(option => (
              <div
                key={option.id}
                className={`
                  flex flex-col items-center justify-center py-1 px-0.5 rounded cursor-pointer
                  ${selectedDuration === option.id
                    ? 'bg-amber-100 dark:bg-slate-700 border border-amber-400 dark:border-indigo-500'
                    : 'hover:bg-slate-100 dark:hover:bg-slate-800 border border-transparent'}
                `}
                onClick={() => handleDurationChange(option.id)}
              >
                <span className="text-center text-slate-900 dark:text-white">
                  {option.label}
                </span>
                <input
                  type="radio"
                  id={`duration-${option.id}`}
                  name="duration"
                  value={option.id}
                  checked={selectedDuration === option.id}
                  onChange={() => handleDurationChange(option.id)}
                  className="w-3 h-3 text-amber-500 dark:text-indigo-600 focus:ring-amber-500 dark:focus:ring-indigo-600 border-slate-300 dark:border-slate-600"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Price Range Filter - Two Dots Slider */}
      <div>
        <div className="flex items-center">
          <DollarSign className="w-4 h-4 mr-3 text-slate-500 dark:text-slate-400" />
          <div className="w-full">
            <div className="flex justify-between text-slate-700 dark:text-slate-300 text-sm">
              <span>${selectedPriceRange[0]}</span>
              <span>${selectedPriceRange[1]}</span>
            </div>
            <div className="relative py-2">
  <div className="absolute inset-y-0 left-0 right-0 flex items-center pointer-events-none">
    <div className="h-1 w-full bg-slate-200 dark:bg-slate-700 rounded"></div>
  </div>
  <div className="flex items-center gap-2 relative z-10">
    <input
      type="range"
      min={0}
      max={selectedPriceRange[1]}
      step={100}
      value={selectedPriceRange[0]}
      onChange={e => {
        const min = Math.min(parseInt(e.target.value), selectedPriceRange[1]);
        handlePriceRangeChange(min, selectedPriceRange[1]);
      }}
      className="w-1/2 h-1 bg-transparent appearance-none cursor-pointer accent-amber-500 dark:accent-indigo-600"
      style={{ zIndex: 2 }}
    />
    <input
      type="range"
      min={selectedPriceRange[0]}
      max={5000}
      step={100}
      value={selectedPriceRange[1]}
      onChange={e => {
        const max = Math.max(parseInt(e.target.value), selectedPriceRange[0]);
        handlePriceRangeChange(selectedPriceRange[0], max);
      }}
      className="w-1/2 h-1 bg-transparent appearance-none cursor-pointer accent-amber-500 dark:accent-indigo-600"
      style={{ zIndex: 2 }}
    />
  </div>
</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterBar;
