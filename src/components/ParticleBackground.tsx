import React, { useEffect, useRef } from 'react';

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  opacity: number;
  type: 'circle' | 'om' | 'star';
}

// Function to draw a star shape
const drawStar = (
  ctx: CanvasRenderingContext2D,
  x: number,
  y: number,
  spikes: number,
  outerRadius: number,
  innerRadius: number
) => {
  let rotation = Math.PI / 2 * 3;
  const step = Math.PI / spikes;

  ctx.beginPath();
  ctx.moveTo(x, y - outerRadius);

  for (let i = 0; i < spikes; i++) {
    ctx.lineTo(
      x + Math.cos(rotation) * outerRadius,
      y + Math.sin(rotation) * outerRadius
    );
    rotation += step;

    ctx.lineTo(
      x + Math.cos(rotation) * innerRadius,
      y + Math.sin(rotation) * innerRadius
    );
    rotation += step;
  }

  ctx.lineTo(x, y - outerRadius);
  ctx.closePath();
};

const ParticleBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particles = useRef<Particle[]>([]);
  const animationFrameId = useRef<number>();

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticles = () => {
      particles.current = [];
      // Increase number of particles
      for (let i = 0; i < 150; i++) {
        // Randomly select particle type
        const types: ('circle' | 'om' | 'star')[] = ['circle', 'om', 'star'];
        const type = types[Math.floor(Math.random() * types.length)];

        // Adjust size based on particle type
        let size;
        if (type === 'circle') {
          size = Math.random() * 3 + 2; // 2 to 5 pixels
        } else if (type === 'om') {
          size = Math.random() * 2 + 3; // 3 to 5 pixels
        } else { // star
          size = Math.random() * 3 + 3; // 3 to 6 pixels
        }

        particles.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size,
          speedX: (Math.random() - 0.5) * 0.3,
          speedY: (Math.random() - 0.5) * 0.3,
          opacity: Math.random() * 0.5 + 0.5, // 0.5 to 1
          type
        });
      }
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.current.forEach(particle => {
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Vary colors based on particle type
        if (particle.type === 'circle') {
          ctx.fillStyle = `rgba(251, 191, 36, ${particle.opacity})`; // Amber
          // Draw circle
          ctx.beginPath();
          ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
          ctx.fill();
        } else if (particle.type === 'om') {
          ctx.fillStyle = `rgba(220, 38, 38, ${particle.opacity})`; // Red
          // Draw Om symbol
          ctx.font = `${particle.size * 5}px Arial`;
          ctx.fillText('ॐ', particle.x - particle.size * 2, particle.y + particle.size * 2);
        } else if (particle.type === 'star') {
          ctx.fillStyle = `rgba(59, 130, 246, ${particle.opacity})`; // Blue
          // Draw star
          drawStar(ctx, particle.x, particle.y, 5, particle.size * 2, particle.size);
          ctx.fill();
        }
      });

      animationFrameId.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    createParticles();
    animate();

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 pointer-events-none opacity-50 dark:opacity-40"
    />
  );
};

export default ParticleBackground;