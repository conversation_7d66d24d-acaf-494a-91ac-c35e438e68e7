import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Clock, Award, ChevronRight } from 'lucide-react';

interface Course {
  id: number;
  slug: string;
  title: string;
  image: string;
  duration: string;
  certification: string;
  level: string;
  description: string;
  highlights: string[];
  active?: string;
  price: {
    shared: string;
    private: string;
    sharedTwin: string;
  };
}

const FeaturedCourses: React.FC = () => {
  const courses: Course[] = [
    {
      id: 1,
      slug: "200-hour-yoga-teacher-training",
      title: "200-Hour Yoga Teacher Training",
      image: "/images/banner.jpeg",
      duration: "28 Days",
      certification: "Yoga Alliance Certified",
      level: "Beginner to Intermediate",
      description:
        "Our comprehensive 200-hour Yoga Teacher Training program is designed for aspiring teachers and dedicated practitioners who wish to deepen their understanding of yoga. This Yoga Alliance certified course covers asana, pranayama, meditation, anatomy, philosophy, teaching methodology, and more.",
      highlights: [
        "Yoga Alliance Certified",
        "Small batch sizes (max 20 students)",
        "Experienced international faculty",
        "Accommodation and meals included",
        "Excursions to sacred sites"
      ],
      active: "true",
      price: {
        shared: "$1800",
        private: "$2200",
        sharedTwin: "$3400"
      }
    },
    {
      id: 2,
      slug: "300-hour-advanced-teacher-training",
      title: "300-Hour Advanced Teacher Training",
      image: "https://placehold.co/800x600/e2e8f0/1e293b?text=300-Hour+TTC",
      duration: "42 Days",
      certification: "Yoga Alliance Certified",
      level: "Intermediate to Advanced",
      description:
        "Take your teaching and practice to the next level with our advanced 300-hour program. Building on the foundation of a 200-hour certification, this course delves deeper into advanced asanas, therapeutic applications, subtle body anatomy, advanced teaching skills, and specialized yoga styles.",
      highlights: [
        "Yoga Alliance Certified",
        "Limited to 15 students per batch",
        "Specialized modules with expert teachers",
        "Private accommodation options",
        "Personalized mentorship"
      ],
      active: "true",
      price: {
        shared: "$2900",
        private: "$3400",
        sharedTwin: "$5500"
      }
    },
    {
      id: 3,
      slug: "100-hour-meditation-teacher-training",
      title: "100-Hour Meditation Teacher Training",
      image: "https://placehold.co/800x600/e2e8f0/1e293b?text=Meditation+TTC",
      duration: "14 Days",
      certification: "Yoga Capital Certified",
      level: "All Levels",
      description:
        "Specialize in the art and science of meditation with our focused 100-hour Meditation Teacher Training. Learn various meditation techniques from different traditions, understand the science behind meditation, and develop the skills to guide others in their meditation journey.",
      highlights: [
        "Comprehensive meditation toolkit",
        "Neuroscience of meditation",
        "Daily silent meditation practice",
        "Teaching practicum",
        "Certification to teach meditation"
      ],
      active: "true",
      price: {
        shared: "$1200",
        private: "$1600",
        sharedTwin: "$2400"
      }
    }
  ];

  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % courses.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [courses.length]);

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute -top-16 -right-16 w-96 h-96 bg-amber-400/20 dark:bg-indigo-600/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-32 -left-20 w-80 h-80 bg-amber-500/20 dark:bg-indigo-500/20 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        <div className="text-center mb-16">
          <div className="inline-flex items-center mb-4">
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-8 mr-4"></div>
            <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Featured Programs</span>
            <div className="h-px bg-amber-400 dark:bg-indigo-500 w-8 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-5xl font-bold text-slate-900 dark:text-white mb-4">
            Transform Your Journey Through
          </h2>
          <h3 className="text-2xl md:text-4xl font-bold text-amber-500 dark:text-amber-400">
            Sacred Yoga Education
          </h3>
        </div>

        <div 
          ref={containerRef} 
          className="relative max-w-6xl mx-auto"
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {courses.map((course, index) => (
              <div
                key={course.id}
                className={`group relative bg-white dark:bg-slate-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 ${
                  index === activeIndex ? 'lg:scale-105 z-20' : 'lg:scale-95 hover:scale-100'
                }`}
              >
                <div className="relative h-64 overflow-hidden">
                  <img
                    src={course.image}
                    alt={course.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent">
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex items-center space-x-2 text-white mb-2">
                        <Clock className="w-4 h-4" />
                        <span className="text-sm">{course.duration}</span>
                        <span className="w-1 h-1 bg-amber-400 rounded-full"></span>
                        <Award className="w-4 h-4" />
                        <span className="text-sm">{course.certification}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3 line-clamp-2">
                    {course.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2">
                    {course.description}
                  </p>
                  <div className="flex flex-wrap gap-2 mb-6">
                    {course.highlights.slice(0, 2).map((highlight, idx) => (
                      <span
                        key={idx}
                        className="inline-block px-3 py-1 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-xs rounded-full"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                  <Link
                    to={`/courses/${course.slug}`}
                    className="inline-flex items-center text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300 text-sm font-medium"
                  >
                    Learn More
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </Link>
                </div>

                <div className="absolute top-3 right-3 px-3 py-1 bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white text-sm font-medium rounded-full">
                  {course.price.shared}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="text-center mt-12">
          <Link 
            to="/courses" 
            className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-full font-medium transition-colors duration-300 group"
          >
            Explore All Courses
            <ArrowRight className="w-5 h-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCourses;
