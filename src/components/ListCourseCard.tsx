import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Clock, Award } from 'lucide-react';

const Badge: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className = '' }) => (
  <span
    className={`px-3 py-1 rounded-full text-sm font-medium ${className}`}
  >
    {children}
  </span>
);

interface Course {
  id: number;
  slug: string;
  title: string;
  image: string;
  duration: number;
  capacity: number;
  certification: number[];
  level: string;
  description: string;
  highlights: string[];
  featured: boolean;
  active: boolean;
  price: {
    shared: number;
    private: number;
    sharedTwin: number;
  };
}

interface Certification {
  id: number;
  name: string;
  link: string;
}

interface ListCourseCardProps {
  course: Course;
  certifications: Certification[];
}

const ListCourseCard: React.FC<ListCourseCardProps> = ({ course, certifications }) => {
  const navigate = useNavigate();

  return (
    <div
      key={course.id}
      className={`group bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col md:flex-row cursor-pointer border-2 ${
        course.featured
          ? 'border-amber-400 dark:border-yellow-400'
          : 'border-slate-200 dark:border-slate-700'
      }`}
      onClick={() => navigate(`/courses/${course.slug}`)}
    >
      <div className="md:w-64 flex-shrink-0 aspect-square relative overflow-hidden">
        <img
          src={course.image}
          alt={`Image for ${course.title} course`}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
      </div>

      <div className="p-6 flex flex-col justify-between flex-1 relative">
        <div>
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-3xl font-bold text-slate-800 dark:text-white">{course.title}</h3>
            <Badge className="bg-amber-400 text-slate-900 dark:bg-yellow-400 dark:text-slate-900 font-bold text-lg px-4 py-2 shadow border border-amber-500 dark:border-yellow-500">
              ${course.price.shared}
            </Badge>
          </div>

          <div className="flex items-center space-x-4 mb-4">
            <span className="flex items-center text-amber-500 dark:text-amber-400">
              <Clock className="w-4 h-4 mr-1" />
              {course.duration} Days
            </span>
            <span className="h-4 border-l border-amber-300 dark:border-amber-500"></span>
            <span className="flex items-center text-amber-500 dark:text-amber-400">
              <Award className="w-4 h-4 mr-1" />
              {course.certification
                .map(certId => {
                  const cert = certifications.find(c => c.id === certId);
                  return cert ? cert.name : certId;
                })
                .join(', ')
              }
            </span>
          </div>

          <div className="flex items-center gap-2 mb-4">
            <Badge className="bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300">
              {course.level}
            </Badge>
            {/* Example conditional badge */}
            {course.level.toLowerCase() === 'beginner' && (
              <Badge className="bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-300">
                New
              </Badge>
            )}
          </div>

          <div className="relative mb-4 text-slate-600 dark:text-slate-300 text-sm max-h-20 overflow-hidden">
            <div className="absolute bottom-0 left-0 w-full h-6 bg-gradient-to-t from-white dark:from-slate-800 to-transparent"></div>
            <p>
              {course.description.length > 120 ? (
                <>
                  {course.description.slice(0, 120)}... <span className="text-amber-500 cursor-pointer">Read more</span>
                </>
              ) : (
                course.description
              )}
            </p>
          </div>

          <div className="flex flex-wrap gap-2 mb-6">
            {course.highlights.slice(0, 2).map((highlight, index) => (
              <Badge
                key={index}
                className="bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 text-xs py-1 px-2"
              >
                {highlight}
              </Badge>
            ))}
            {course.highlights.length > 2 && (
              <Badge className="border border-slate-300 dark:border-slate-500 text-slate-800 dark:text-slate-200 text-xs py-1 px-2">
                +{course.highlights.length - 2} more
              </Badge>
            )}
          </div>
        </div>

      </div>
    </div>
  );
};

export default ListCourseCard;
