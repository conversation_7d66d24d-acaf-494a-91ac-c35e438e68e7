import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { useState, useEffect, Suspense, lazy } from 'react';
import Navbar from './components/Navbar';
import Footer from './components/Footer';

// Lazy load all page components
const HomePage = lazy(() => import('./pages/HomePage'));
const AboutPage = lazy(() => import('./pages/AboutPage'));
const CoursesPage = lazy(() => import('./pages/CoursesPage'));
const SingleCoursePage = lazy(() => import('./pages/SingleCoursePage'));
const RetreatsPage = lazy(() => import('./pages/RetreatsPage'));
const ContactPage = lazy(() => import('./pages/ContactPage'));
const ApplyPage = lazy(() => import('./pages/ApplyPage'));
const SingleRetreatPage = lazy(() => import('./pages/SingleRetreatPage'));
const TeachersPage = lazy(() => import('./pages/TeachersPage'));
const YogaStylePage = lazy(() => import('./pages/YogaStylePage'));
const CalendarPage = lazy(() => import('./pages/CalendarPage'));
const ResourcesPage = lazy(() => import('./pages/ResourcesPage'));
const BeginnerYogaPage = lazy(() => import('./pages/BeginnerYogaPage'));
const NotFoundPage = lazy(() => import('./pages/NotFoundPage'));
const WellnessServicesPage = lazy(() => import('./pages/WellnessServicesPage'));
const OnlineServicesPage = lazy(() => import('./pages/OnlineServicesPage'));
const FAQPage = lazy(() => import('./pages/FAQPage'));
const CareerCenterPage = lazy(() => import('./pages/CareerCenterPage'));
const ImpactPage = lazy(() => import('./pages/ImpactPage'));
const GalleryPage = lazy(() => import('./pages/GalleryPage'));
const PrivacyPolicyPage = lazy(() => import('./pages/PrivacyPolicyPage'));
const TermsOfServicePage = lazy(() => import('./pages/TermsOfServicePage'));
const TestPage = lazy(() => import('./pages/TestPage'));

// Digital Ashram pages
const DigitalAshramPage = lazy(() => import('./pages/DigitalAshramPage'));
const SingleActivityPage = lazy(() => import('./digitalashram/SingleActivityPage'));

// Create a ScrollToTop component that uses useLocation
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

// Wrap the main app content
function AppContent() {
  const [darkMode, setDarkModeState] = useState(() => {
    const saved = localStorage.getItem('darkMode');
    if (saved !== null) {
      return JSON.parse(saved);
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });
  const [scrollPosition, setScrollPosition] = useState(0);

  const setDarkMode = (value: boolean) => {
    setDarkModeState(value);
    localStorage.setItem('darkMode', JSON.stringify(value));
  };

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  // Preload all other pages except the current one
  const location = useLocation();
  useEffect(() => {
    // Map of route paths to import functions
    const pageImports: { [key: string]: () => Promise<unknown> } = {
      '/': () => import('./pages/HomePage'),
      '/about': () => import('./pages/AboutPage'),
      '/courses': () => import('./pages/CoursesPage'),
      '/retreats': () => import('./pages/RetreatsPage'),
      '/contact': () => import('./pages/ContactPage'),
      '/apply': () => import('./pages/ApplyPage'),
      '/teachers': () => import('./pages/TeachersPage'),
      '/calendar': () => import('./pages/CalendarPage'),
      '/resources': () => import('./pages/ResourcesPage'),
      '/begin-yoga': () => import('./pages/BeginnerYogaPage'),
      '/wellness': () => import('./pages/WellnessServicesPage'),
      '/online-services': () => import('./pages/OnlineServicesPage'),
      '/faq': () => import('./pages/FAQPage'),
      '/careers': () => import('./pages/CareerCenterPage'),
      '/impact': () => import('./pages/ImpactPage'),
      '/gallery': () => import('./pages/GalleryPage'),
      '/privacy-policy': () => import('./pages/PrivacyPolicyPage'),
      '/terms-of-service': () => import('./pages/TermsOfServicePage'),
      '/test': () => import('./pages/TestPage'),
      '/digitalashram': () => import('./pages/DigitalAshramPage'),
      '/digitalashram/:activitySlug': () => import('./digitalashram/SingleActivityPage'),
      // Dynamic routes: preloading the base component is sufficient
      '/courses/:courseSlug': () => import('./pages/SingleCoursePage'),
      '/retreats/:retreatId': () => import('./pages/SingleRetreatPage'),
      '/yoga-styles/:slug': () => import('./pages/YogaStylePage'),
      '*': () => import('./pages/NotFoundPage'),
    };

    // Get current path, ignoring dynamic params for preloading
    const currentPath = Object.keys(pageImports).find((path) => {
      if (path === '*') return false;
      if (path.includes(':')) {
        // Match dynamic routes
        const base = path.split('/:')[0];
        return location.pathname.startsWith(base);
      }
      return location.pathname === path;
    });

    Object.entries(pageImports).forEach(([path, importer]) => {
      if (path !== currentPath) {
        importer();
      }
    });
  }, [location.pathname]);

  return (
    <div className={`min-h-screen transition-colors duration-300 ${darkMode ? 'dark bg-slate-900 text-white' : 'bg-amber-50 text-slate-900'}`}>
      <ScrollToTop />
      <Navbar darkMode={darkMode} setDarkMode={setDarkMode} scrollPosition={scrollPosition} />
      <Suspense fallback={<div className="flex justify-center items-center min-h-[40vh]">Loading...</div>}>
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/about" element={<AboutPage />} />
          <Route path="/courses" element={<CoursesPage />} />
          <Route path="/courses/:courseSlug" element={<SingleCoursePage />} />
          <Route path="/retreats" element={<RetreatsPage />} />
          <Route path="/retreats/:retreatId" element={<SingleRetreatPage />} />
          <Route path="/calendar" element={<CalendarPage />} />
          <Route path="/teachers" element={<TeachersPage />} />
          <Route path="/contact" element={<ContactPage />} />
          <Route path="/apply" element={<ApplyPage />} />
          <Route path="/yoga-styles/:slug" element={<YogaStylePage />} />
          <Route path="/resources" element={<ResourcesPage />} />
          <Route path="/begin-yoga" element={<BeginnerYogaPage />} />
          <Route path="/wellness" element={<WellnessServicesPage />} />
          <Route path="/online-services" element={<OnlineServicesPage />} />
          <Route path="/faq" element={<FAQPage />} />
          <Route path="/careers" element={<CareerCenterPage />} />
          <Route path="/impact" element={<ImpactPage />} />
          <Route path="/gallery" element={<GalleryPage />} />
          <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
          <Route path="/terms-of-service" element={<TermsOfServicePage />} />
          <Route path="/test" element={<TestPage />} />
          {/* Digital Ashram Routes */}
          <Route path="/digitalashram" element={<DigitalAshramPage />} />
          <Route path="/digitalashram/:activitySlug" element={<SingleActivityPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
      <Footer />
    </div>
  );
}

// Main App component
function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;
