import React, { useState, useEffect, useRef, useMemo } from 'react';

interface SoundOption {
  id: string;
  name: string;
  src: string;
}

interface TimerOption {
  id: string;
  minutes: number;
  label: string;
}

const MeditationTimer: React.FC = () => {
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [selectedDuration, setSelectedDuration] = useState<string>('duration-10');
  const [selectedSound, setSelectedSound] = useState<string>('nature');
  const [volume, setVolume] = useState<number>(70);
  const [showCompletionMessage, setShowCompletionMessage] = useState<boolean>(false);

  const timerRef = useRef<number | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const circleRef = useRef<SVGCircleElement>(null);

  const timerOptions = useMemo<TimerOption[]>(() => [
    { id: 'duration-5', minutes: 5, label: '5 min' },
    { id: 'duration-10', minutes: 10, label: '10 min' },
    { id: 'duration-15', minutes: 15, label: '15 min' },
    { id: 'duration-20', minutes: 20, label: '20 min' },
    { id: 'duration-30', minutes: 30, label: '30 min' },
  ], []);

  const soundOptions = useMemo<SoundOption[]>(() => [
    { id: 'nature', name: 'Nature Sounds', src: 'https://example.com/nature.mp3' },
    { id: 'rain', name: 'Gentle Rain', src: 'https://example.com/rain.mp3' },
    { id: 'bowls', name: 'Singing Bowls', src: 'https://example.com/bowls.mp3' },
    { id: 'om', name: 'Om Chanting', src: 'https://example.com/om.mp3' },
    { id: 'silence', name: 'Silence', src: '' },
  ], []);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Set up audio
  useEffect(() => {
    if (selectedSound !== 'silence') {
      const selectedSoundOption = soundOptions.find(option => option.id === selectedSound);
      if (selectedSoundOption && audioRef.current) {
        audioRef.current.src = selectedSoundOption.src;
        audioRef.current.loop = true;
        audioRef.current.volume = volume / 100;
      }
    }
  }, [selectedSound, volume, soundOptions]);

  // Timer logic
  useEffect(() => {
    const selectedOption = timerOptions.find(option => option.id === selectedDuration);
    if (selectedOption) {
      setTimeLeft(selectedOption.minutes * 60);
    }
  }, [selectedDuration, timerOptions]);

  useEffect(() => {
    if (isRunning && !isPaused) {
      // Start or resume the timer
      timerRef.current = window.setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            // Timer completed
            clearInterval(timerRef.current as number);
            setIsRunning(false);
            setShowCompletionMessage(true);
            if (audioRef.current) {
              audioRef.current.pause();
            }
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);

      // Play the selected sound
      if (selectedSound !== 'silence' && audioRef.current) {
        audioRef.current.play().catch(error => {
          console.error('Audio playback failed:', error);
        });
      }
    } else {
      // Pause or stop the timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // Pause the audio
      if (audioRef.current) {
        audioRef.current.pause();
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRunning, isPaused, selectedSound]);

  // Update circle progress
  useEffect(() => {
    if (circleRef.current) {
      const selectedOption = timerOptions.find(option => option.id === selectedDuration);
      if (selectedOption) {
        const totalSeconds = selectedOption.minutes * 60;
        const percentage = (timeLeft / totalSeconds) * 100;
        const circumference = 2 * Math.PI * 120; // Circle radius is 120
        const offset = circumference - (percentage / 100) * circumference;
        circleRef.current.style.strokeDashoffset = offset.toString();
      }
    }
  }, [timeLeft, selectedDuration, timerOptions]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleStartTimer = () => {
    if (!isRunning) {
      setIsRunning(true);
      setIsPaused(false);
      setShowCompletionMessage(false);
    } else if (isPaused) {
      setIsPaused(false);
    } else {
      setIsPaused(true);
    }
  };

  const handleResetTimer = () => {
    setIsRunning(false);
    setIsPaused(false);
    setShowCompletionMessage(false);
    const selectedOption = timerOptions.find(option => option.id === selectedDuration);
    if (selectedOption) {
      setTimeLeft(selectedOption.minutes * 60);
    }
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
  };

  const handleDurationChange = (durationId: string) => {
    if (!isRunning) {
      setSelectedDuration(durationId);
    }
  };

  const handleSoundChange = (soundId: string) => {
    setSelectedSound(soundId);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseInt(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume / 100;
    }
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Meditation <span className="text-amber-500 dark:text-amber-400">Timer</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Create a peaceful meditation practice with customizable duration and ambient sounds.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left column: Timer settings */}
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Timer Settings</h3>

                {/* Duration selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Duration
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {timerOptions.map(option => (
                      <button
                        key={option.id}
                        onClick={() => handleDurationChange(option.id)}
                        disabled={isRunning}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                          selectedDuration === option.id
                            ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                            : 'bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-500'
                        } ${isRunning ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        {option.label}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Sound selection */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                    Ambient Sound
                  </label>
                  <select
                    value={selectedSound}
                    onChange={(e) => handleSoundChange(e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                  >
                    {soundOptions.map(option => (
                      <option key={option.id} value={option.id}>
                        {option.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Volume control */}
                {selectedSound !== 'silence' && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
                      Volume: {volume}%
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={volume}
                      onChange={handleVolumeChange}
                      className="w-full h-2 bg-slate-200 dark:bg-slate-600 rounded-lg appearance-none cursor-pointer accent-amber-500"
                    />
                  </div>
                )}
              </div>

              <div className="bg-amber-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Meditation Tips</h3>
                <ul className="space-y-3 text-slate-700 dark:text-slate-300">
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Find a quiet, comfortable place to sit
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Keep your spine straight but relaxed
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Focus on your breath as an anchor
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    When your mind wanders, gently bring it back
                  </li>
                  <li className="flex items-start">
                    <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Be kind to yourself, meditation is a practice
                  </li>
                </ul>
              </div>
            </div>

            {/* Right column: Timer display */}
            <div className="lg:col-span-2">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md flex flex-col items-center justify-center h-full">
                {/* Timer circle */}
                <div className="relative w-64 h-64 md:w-80 md:h-80 mb-8">
                  <svg className="w-full h-full" viewBox="0 0 256 256">
                    {/* Background circle */}
                    <circle
                      cx="128"
                      cy="128"
                      r="120"
                      fill="none"
                      stroke="#e2e8f0"
                      strokeWidth="8"
                      className="dark:stroke-slate-600"
                    />
                    {/* Progress circle */}
                    <circle
                      ref={circleRef}
                      cx="128"
                      cy="128"
                      r="120"
                      fill="none"
                      stroke="#f59e0b"
                      strokeWidth="8"
                      strokeLinecap="round"
                      strokeDasharray={2 * Math.PI * 120}
                      strokeDashoffset="0"
                      transform="rotate(-90 128 128)"
                      className="dark:stroke-amber-400 transition-all duration-1000 ease-linear"
                    />
                  </svg>

                  {/* Timer display */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <div className="text-5xl md:text-6xl font-bold text-slate-900 dark:text-white mb-2">
                      {formatTime(timeLeft)}
                    </div>
                    <div className="text-amber-500 dark:text-amber-400 font-medium">
                      {isRunning && !isPaused ? 'Meditating...' : 'Ready'}
                    </div>
                  </div>
                </div>

                {/* Completion message */}
                {showCompletionMessage && (
                  <div className="bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 p-4 rounded-lg mb-6 text-center animate-fade-in">
                    <p className="font-medium">Meditation complete. Take a moment to notice how you feel.</p>
                  </div>
                )}

                {/* Control buttons */}
                <div className="flex space-x-4">
                  <button
                    onClick={handleStartTimer}
                    className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 flex items-center ${
                      isRunning && !isPaused
                        ? 'bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300'
                        : 'bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white'
                    }`}
                  >
                    {isRunning ? (
                      isPaused ? (
                        <>
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          Resume
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          Pause
                        </>
                      )
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Start Meditation
                      </>
                    )}
                  </button>

                  <button
                    onClick={handleResetTimer}
                    className="px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300 flex items-center"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Hidden audio element */}
      <audio ref={audioRef} />
    </div>
  );
};

export default MeditationTimer;