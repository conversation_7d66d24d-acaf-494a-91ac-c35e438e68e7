import React, { useState, useRef, useEffect } from 'react';

interface PricingFeature {
  name: string;
  basic: boolean | string;
  standard: boolean | string;
  premium: boolean | string;
}

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: string;
  accommodation: string;
  popular?: boolean;
  features: string[];
  color: string;
}

const PricingTable: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'course'>('course');
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const pricingPlans: PricingPlan[] = [
    {
      id: 'basic',
      name: 'Basic',
      description: 'Perfect for beginners looking to start their yoga journey',
      price: 999,
      duration: '200-Hour YTT',
      accommodation: 'Shared Room (3-4 People)',
      features: [
        'Daily Yoga Classes',
        'Yoga Philosophy Sessions',
        'Course Materials',
        'Vegetarian Meals',
        'Shared Accommodation',
        'Weekend Excursions',
        'Certification',
        'Airport Pickup'
      ],
      color: 'amber'
    },
    {
      id: 'standard',
      name: 'Standard',
      description: 'Our most popular program for dedicated practitioners',
      price: 1299,
      duration: '200-Hour YTT',
      accommodation: 'Shared Room (2 People)',
      popular: true,
      features: [
        'Daily Yoga Classes',
        'Yoga Philosophy Sessions',
        'Course Materials',
        'Vegetarian Meals',
        'Semi-Private Accommodation',
        'Weekend Excursions',
        'Certification',
        'Airport Pickup',
        'Private Consultation',
        'Ayurvedic Massage'
      ],
      color: 'amber'
    },
    {
      id: 'premium',
      name: 'Premium',
      description: 'Luxury experience with personalized attention',
      price: 1799,
      duration: '200-Hour YTT',
      accommodation: 'Private Room',
      features: [
        'Daily Yoga Classes',
        'Yoga Philosophy Sessions',
        'Course Materials',
        'Vegetarian Meals',
        'Private Accommodation',
        'Weekend Excursions',
        'Certification',
        'Airport Pickup',
        'Private Consultation',
        'Ayurvedic Massage',
        'Personal Yoga Kit',
        'Post-Course Support'
      ],
      color: 'amber'
    }
  ];

  // Monthly prices (calculated as course price / 4 weeks)
  const getMonthlyPrice = (price: number) => Math.round(price / 4);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Transparent <span className="text-amber-500 dark:text-amber-400">Pricing</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Choose the plan that fits your needs. All plans include certification, accommodation, and meals.
            </p>
            
            {/* Billing cycle toggle */}
            <div className="flex justify-center mt-6">
              <div className="bg-slate-100 dark:bg-slate-700 p-1 rounded-full inline-flex">
                <button
                  onClick={() => setBillingCycle('course')}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    billingCycle === 'course'
                      ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow'
                      : 'text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white'
                  }`}
                >
                  Full Course
                </button>
                <button
                  onClick={() => setBillingCycle('monthly')}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    billingCycle === 'monthly'
                      ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow'
                      : 'text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white'
                  }`}
                >
                  Monthly
                </button>
              </div>
            </div>
          </div>

          {/* Pricing cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {pricingPlans.map((plan) => (
              <div
                key={plan.id}
                className={`relative rounded-xl overflow-hidden transition-all duration-300 hover:shadow-2xl ${
                  plan.popular
                    ? 'border-2 border-amber-400 dark:border-amber-500 shadow-xl transform md:-translate-y-4'
                    : 'border border-slate-200 dark:border-slate-700 shadow-lg'
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 bg-amber-400 dark:bg-amber-500 text-slate-900 dark:text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                    MOST POPULAR
                  </div>
                )}
                
                <div className="p-6 md:p-8">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">{plan.name}</h3>
                  <p className="text-slate-600 dark:text-slate-400 text-sm mb-6 h-12">{plan.description}</p>
                  
                  <div className="mb-6">
                    <div className="flex items-end">
                      <span className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white">
                        ${billingCycle === 'course' ? plan.price : getMonthlyPrice(plan.price)}
                      </span>
                      <span className="text-slate-500 dark:text-slate-400 ml-2 pb-1">
                        /{billingCycle === 'course' ? 'course' : 'month'}
                      </span>
                    </div>
                    <div className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                      {plan.duration} • {plan.accommodation}
                    </div>
                  </div>
                  
                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span className="text-slate-700 dark:text-slate-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  
                  <button
                    className={`w-full py-3 rounded-lg font-medium transition-all duration-300 ${
                      plan.popular
                        ? 'bg-amber-400 hover:bg-amber-500 dark:bg-amber-500 dark:hover:bg-amber-600 text-slate-900'
                        : 'bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-900 dark:text-white'
                    }`}
                  >
                    Apply Now
                  </button>
                </div>
              </div>
            ))}
          </div>
          
          {/* Additional information */}
          <div className="mt-12 text-center">
            <p className="text-slate-600 dark:text-slate-400 text-sm">
              All prices are in USD. Need a custom plan? <a href="#" className="text-amber-500 dark:text-amber-400 font-medium hover:underline">Contact us</a> for personalized options.
            </p>
          </div>
        </div>
        
        {/* FAQ section */}
        <div className="bg-slate-50 dark:bg-slate-700/30 p-6 md:p-10">
          <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-6 text-center">Frequently Asked Questions</h3>
          
          <div className="max-w-3xl mx-auto space-y-4">
            <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
              <h4 className="font-medium text-slate-900 dark:text-white mb-2">What's included in the accommodation?</h4>
              <p className="text-slate-600 dark:text-slate-300 text-sm">All accommodations include bedding, towels, daily cleaning service, Wi-Fi, and access to common areas. Premium rooms include additional amenities like air conditioning and private bathrooms.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
              <h4 className="font-medium text-slate-900 dark:text-white mb-2">Can I upgrade my accommodation during my stay?</h4>
              <p className="text-slate-600 dark:text-slate-300 text-sm">Yes, you can upgrade your accommodation based on availability. Please contact our staff at least one week before your desired change date.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
              <h4 className="font-medium text-slate-900 dark:text-white mb-2">Are there any additional costs I should be aware of?</h4>
              <p className="text-slate-600 dark:text-slate-300 text-sm">The prices listed include all core program elements. Additional costs may include optional activities, personal purchases, and travel insurance which we strongly recommend.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingTable;
