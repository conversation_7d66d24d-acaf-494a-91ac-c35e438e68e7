import React, { useState, useRef, useEffect, useCallback } from 'react';

interface Teacher {
  id: number;
  name: string;
  title: string;
  image: string;
  quote: string;
  bio: string;
  specialties: string[];
  featuredClass: {
    title: string;
    schedule: string;
    description: string;
  };
  achievements: string[];
  socialMedia: {
    instagram?: string;
    facebook?: string;
    youtube?: string;
    website?: string;
  };
}

const TeacherSpotlight: React.FC = () => {
  const [activeTeacherIndex, setActiveTeacherIndex] = useState<number>(0);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  const [direction, setDirection] = useState<'next' | 'prev'>('next');

  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const spotlightRef = useRef<HTMLDivElement>(null);

  // Sample featured teachers data
  const featuredTeachers: Teacher[] = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Meditation Master & Philosophy Teacher",
      image: "https://placehold.co/600x800/e2e8f0/1e293b?text=<PERSON>+<PERSON>",
      quote: "Meditation is not an escape from life, but a deeper encounter with it.",
      bio: "Swami Ananda has dedicated over 30 years to the practice and teaching of meditation and yoga philosophy. After spending 12 years in the Himalayas studying with various masters, he returned to share the profound wisdom of yoga with students worldwide. His approach combines traditional yogic practices with practical insights for modern life, making ancient wisdom accessible and relevant. Swami <PERSON>a is known for his gentle humor, profound insights, and ability to explain complex philosophical concepts in simple, relatable terms.",
      specialties: ["Meditation", "Yoga Philosophy", "Pranayama", "Mantra Chanting", "Yoga Nidra"],
      featuredClass: {
        title: "Inner Silence: Meditation for Modern Life",
        schedule: "Tuesdays & Thursdays, 6:00 AM - 7:30 AM",
        description: "A transformative meditation class that combines traditional techniques with practical applications for daily life. Suitable for all levels, from beginners to advanced practitioners."
      },
      achievements: [
        "Author of 'The Heart of Meditation' and 'Living Yoga Philosophy'",
        "Founded three meditation centers across India",
        "Trained over 500 meditation teachers",
        "Featured speaker at International Yoga Conference (2018-2022)"
      ],
      socialMedia: {
        instagram: "https://instagram.com/swamiananda",
        facebook: "https://facebook.com/swamiananda",
        youtube: "https://youtube.com/swamiananda",
        website: "https://swamiananda.com"
      }
    },
    {
      id: 2,
      name: "Maya Patel",
      title: "Advanced Asana & Alignment Specialist",
      image: "https://placehold.co/600x800/e2e8f0/1e293b?text=Maya+Patel",
      quote: "The body is the bow, asana is the arrow, and the soul is the target.",
      bio: "Maya Patel began her yoga journey at age 16 and has since become one of the most respected asana teachers in the country. With certifications in Iyengar, Ashtanga, and therapeutic yoga, she brings a wealth of knowledge to her precise, alignment-focused classes. After recovering from a serious back injury through yoga, Maya developed a special interest in therapeutic applications of asana practice. Her teaching style is characterized by meticulous attention to detail, creative sequencing, and an emphasis on the subtle energetic effects of each pose.",
      specialties: ["Iyengar Yoga", "Alignment Therapy", "Backbends", "Inversions", "Yoga for Injuries"],
      featuredClass: {
        title: "Precision in Practice: Advanced Asana Masterclass",
        schedule: "Mondays & Fridays, 9:00 AM - 11:00 AM",
        description: "An in-depth exploration of advanced asanas with precise alignment cues and modifications. Suitable for intermediate to advanced practitioners with at least 2 years of consistent practice."
      },
      achievements: [
        "E-RYT 500 with over 10,000 teaching hours",
        "Studied directly with the Iyengar family in Pune for 5 years",
        "Developed 'Align & Heal' methodology for injury recovery",
        "Featured in Yoga Journal and Yoga International"
      ],
      socialMedia: {
        instagram: "https://instagram.com/mayapatelyoga",
        youtube: "https://youtube.com/mayapatelyoga",
        website: "https://mayapatelyoga.com"
      }
    },
    {
      id: 3,
      name: "Dr. Raj Kumar",
      title: "Ayurvedic Practitioner & Holistic Wellness Expert",
      image: "https://placehold.co/600x800/e2e8f0/1e293b?text=Dr+Raj+Kumar",
      quote: "Yoga and Ayurveda are two wings of the same bird of consciousness.",
      bio: "Dr. Raj Kumar bridges the ancient wisdom of Ayurveda with modern yoga practice. With a background in conventional medicine and extensive training in traditional Ayurveda, he offers a unique integrated approach to wellness. Dr. Raj completed his formal Ayurvedic studies at Gujarat Ayurved University and has spent over 15 years researching the complementary relationship between yoga and Ayurveda. His classes and consultations focus on personalized practices based on individual constitution (dosha) and current imbalances, helping students optimize their yoga practice for maximum health benefits.",
      specialties: ["Ayurvedic Yoga", "Dosha-Specific Practices", "Seasonal Routines", "Therapeutic Herbs", "Detoxification"],
      featuredClass: {
        title: "Ayurvedic Yoga: Balancing Your Doshas",
        schedule: "Wednesdays, 4:00 PM - 6:00 PM & Saturdays, 8:00 AM - 10:00 AM",
        description: "Learn how to adapt your yoga practice according to your Ayurvedic constitution and seasonal changes. Includes personalized recommendations for diet, lifestyle, and specific yoga sequences."
      },
      achievements: [
        "Ph.D. in Ayurvedic Medicine with specialization in Yoga Therapy",
        "Author of 'The Ayurvedic Path to Yoga' and 'Seasonal Yoga Practices'",
        "Developed Ayurvedic protocols for major yoga therapy centers",
        "Consultant to international wellness retreats and hospitals"
      ],
      socialMedia: {
        instagram: "https://instagram.com/drkumarayurveda",
        facebook: "https://facebook.com/drkumarayurveda",
        website: "https://ayurvedicyoga.com"
      }
    }
  ];

  const handlePrev = useCallback(() => {
    if (isAnimating) return;

    setIsAnimating(true);
    setDirection('prev');

    setTimeout(() => {
      setActiveTeacherIndex((prev) => (prev === 0 ? featuredTeachers.length - 1 : prev - 1));
      setIsAnimating(false);
    }, 500);
  }, [isAnimating, featuredTeachers.length]);

  const handleNext = useCallback(() => {
    if (isAnimating) return;

    setIsAnimating(true);
    setDirection('next');

    setTimeout(() => {
      setActiveTeacherIndex((prev) => (prev === featuredTeachers.length - 1 ? 0 : prev + 1));
      setIsAnimating(false);
    }, 500);
  }, [isAnimating, featuredTeachers.length]);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Auto-rotate teachers every 10 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isAnimating) {
        handleNext();
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [activeTeacherIndex, isAnimating, handleNext]);

  const activeTeacher = featuredTeachers[activeTeacherIndex];

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Teacher <span className="text-amber-500 dark:text-amber-400">Spotlight</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Meet our exceptional teachers who bring wisdom, experience, and passion to our yoga community.
            </p>
          </div>

          <div
            ref={spotlightRef}
            className={`transition-all duration-500 ${isAnimating ? 'opacity-0' : 'opacity-100'} ${
              direction === 'next' ? 'translate-x-4' : '-translate-x-4'
            }`}
          >
            <div className="relative mb-8">
              {/* Navigation buttons */}
              <div className="absolute -top-4 right-0 flex space-x-2 z-10">
                <button
                  onClick={handlePrev}
                  className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 flex items-center justify-center hover:bg-amber-200 dark:hover:bg-amber-800/30 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <button
                  onClick={handleNext}
                  className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 flex items-center justify-center hover:bg-amber-200 dark:hover:bg-amber-800/30 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>

              {/* Teacher indicator dots */}
              <div className="absolute -top-4 left-0 flex space-x-2">
                {featuredTeachers.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      if (isAnimating) return;
                      setDirection(index > activeTeacherIndex ? 'next' : 'prev');
                      setIsAnimating(true);
                      setTimeout(() => {
                        setActiveTeacherIndex(index);
                        setIsAnimating(false);
                      }, 500);
                    }}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === activeTeacherIndex
                        ? 'bg-amber-500 dark:bg-amber-400 w-6'
                        : 'bg-slate-300 dark:bg-slate-600'
                    }`}
                    aria-label={`View ${featuredTeachers[index].name}`}
                  ></button>
                ))}
              </div>

              {/* Main content */}
              <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-slate-700 dark:to-slate-800 rounded-xl overflow-hidden shadow-lg">
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-0">
                  {/* Teacher image and quote - 5 columns on large screens */}
                  <div className="lg:col-span-5 relative">
                    <div className="h-64 lg:h-full relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent z-10"></div>
                      <img
                        src={activeTeacher.image}
                        alt={activeTeacher.name}
                        className="w-full h-full object-cover object-center"
                      />

                      {/* Quote overlay */}
                      <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
                        <div className="relative">
                          <svg className="w-10 h-10 text-amber-400/80 dark:text-amber-300/80 absolute -top-6 -left-2" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                          </svg>
                          <blockquote className="text-white text-lg italic pl-8 pr-4">
                            {activeTeacher.quote}
                          </blockquote>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Teacher info - 7 columns on large screens */}
                  <div className="lg:col-span-7 p-6 lg:p-8">
                    <div className="mb-6">
                      <h3 className="text-2xl lg:text-3xl font-bold text-slate-900 dark:text-white">{activeTeacher.name}</h3>
                      <p className="text-amber-500 dark:text-amber-400 text-lg">{activeTeacher.title}</p>
                    </div>

                    <div className="mb-6">
                      <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                        {activeTeacher.bio}
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Specialties</h4>
                        <div className="flex flex-wrap gap-2">
                          {activeTeacher.specialties.map((specialty, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 rounded-full text-sm"
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Achievements</h4>
                        <ul className="space-y-1 text-slate-700 dark:text-slate-300 text-sm">
                          {activeTeacher.achievements.map((achievement, index) => (
                            <li key={index} className="flex items-start">
                              <svg className="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                              </svg>
                              <span>{achievement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Featured class */}
                    <div className="bg-white/50 dark:bg-slate-700/50 rounded-lg p-4 mb-6">
                      <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">Featured Class</h4>
                      <h5 className="text-amber-600 dark:text-amber-400 font-medium mb-1">{activeTeacher.featuredClass.title}</h5>
                      <p className="text-slate-600 dark:text-slate-400 text-sm mb-2">{activeTeacher.featuredClass.schedule}</p>
                      <p className="text-slate-700 dark:text-slate-300 text-sm">{activeTeacher.featuredClass.description}</p>
                    </div>

                    {/* Social media and CTA */}
                    <div className="flex flex-wrap items-center justify-between">
                      <div className="flex space-x-3 mb-4 md:mb-0">
                        {activeTeacher.socialMedia.instagram && (
                          <a
                            href={activeTeacher.socialMedia.instagram}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-10 h-10 rounded-full bg-slate-200 dark:bg-slate-600 hover:bg-amber-200 dark:hover:bg-amber-800/30 flex items-center justify-center transition-colors"
                            aria-label={`${activeTeacher.name}'s Instagram`}
                          >
                            <svg className="w-5 h-5 text-slate-700 dark:text-slate-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                          </a>
                        )}

                        {activeTeacher.socialMedia.facebook && (
                          <a
                            href={activeTeacher.socialMedia.facebook}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-10 h-10 rounded-full bg-slate-200 dark:bg-slate-600 hover:bg-amber-200 dark:hover:bg-amber-800/30 flex items-center justify-center transition-colors"
                            aria-label={`${activeTeacher.name}'s Facebook`}
                          >
                            <svg className="w-5 h-5 text-slate-700 dark:text-slate-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                            </svg>
                          </a>
                        )}

                        {activeTeacher.socialMedia.youtube && (
                          <a
                            href={activeTeacher.socialMedia.youtube}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-10 h-10 rounded-full bg-slate-200 dark:bg-slate-600 hover:bg-amber-200 dark:hover:bg-amber-800/30 flex items-center justify-center transition-colors"
                            aria-label={`${activeTeacher.name}'s YouTube`}
                          >
                            <svg className="w-5 h-5 text-slate-700 dark:text-slate-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                            </svg>
                          </a>
                        )}

                        {activeTeacher.socialMedia.website && (
                          <a
                            href={activeTeacher.socialMedia.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-10 h-10 rounded-full bg-slate-200 dark:bg-slate-600 hover:bg-amber-200 dark:hover:bg-amber-800/30 flex items-center justify-center transition-colors"
                            aria-label={`${activeTeacher.name}'s Website`}
                          >
                            <svg className="w-5 h-5 text-slate-700 dark:text-slate-300" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path d="M21.41 8.64v-.05a10 10 0 1 0-18.82 0v.05a9.86 9.86 0 0 0 0 6.72v.05a10 10 0 0 0 18.82 0v-.05a9.86 9.86 0 0 0 0-6.72ZM4.26 14a7.82 7.82 0 0 1 0-4h1.86a16.73 16.73 0 0 0 0 4Zm.82 2h1.4a12.15 12.15 0 0 0 1 2.57A8 8 0 0 1 5.08 16Zm1.4-8h-1.4a8 8 0 0 1 2.37-2.57A12.15 12.15 0 0 0 6.48 8Zm4.46 10.74a6.91 6.91 0 0 1-2.41-2.74h2.41Zm0-4.74H6.8a14.67 14.67 0 0 1 0-4h4.14Zm0-6h-2.41a6.91 6.91 0 0 1 2.41-2.74Zm5.52 10.57A12.15 12.15 0 0 0 17.52 16h1.4a8 8 0 0 1-2.46 2.57ZM12 20.74a6.91 6.91 0 0 1-2.41-2.74h4.83A6.91 6.91 0 0 1 12 20.74Zm0-4.74h-4.14a14.67 14.67 0 0 1 0-4h8.29a14.67 14.67 0 0 1 0 4Zm0-10.74a6.91 6.91 0 0 1 2.41 2.74h-4.82A6.91 6.91 0 0 1 12 5.26Zm5.52-1.09A12.15 12.15 0 0 0 16.52 8h-1.4a8 8 0 0 1 2.4-3.83ZM17.52 14a16.73 16.73 0 0 0 0-4h1.86a7.82 7.82 0 0 1 0 4Z"/>
                            </svg>
                          </a>
                        )}
                      </div>

                      <a
                        href="#"
                        className="inline-flex items-center px-5 py-2.5 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300 transform hover:scale-105"
                      >
                        View Classes
                        <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional teacher info cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Teaching philosophy */}
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mr-3">
                    <svg className="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Teaching Philosophy</h3>
                </div>
                <p className="text-slate-700 dark:text-slate-300">
                  {activeTeacher.name}'s teaching approach emphasizes the integration of mind, body, and breath.
                  Students are encouraged to explore their practice with curiosity and compassion,
                  finding their own unique expression of each pose while honoring their body's wisdom.
                </p>
              </div>

              {/* Upcoming workshops */}
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mr-3">
                    <svg className="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Upcoming Workshops</h3>
                </div>
                <ul className="space-y-3">
                  <li className="pb-3 border-b border-slate-200 dark:border-slate-600">
                    <h4 className="font-medium text-slate-900 dark:text-white">Deepening Your Practice</h4>
                    <p className="text-amber-500 dark:text-amber-400 text-sm">June 15-16, 2023</p>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">A weekend immersion for dedicated practitioners</p>
                  </li>
                  <li>
                    <h4 className="font-medium text-slate-900 dark:text-white">The Art of Meditation</h4>
                    <p className="text-amber-500 dark:text-amber-400 text-sm">July 8, 2023</p>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Learn practical techniques for daily meditation</p>
                  </li>
                </ul>
              </div>

              {/* Student testimonial */}
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mr-3">
                    <svg className="w-6 h-6 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-slate-900 dark:text-white">Student Testimonial</h3>
                </div>
                <div className="relative">
                  <svg className="w-8 h-8 text-amber-300 dark:text-amber-700 absolute -top-4 -left-2 opacity-50" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                  <blockquote className="text-slate-700 dark:text-slate-300 italic pl-6">
                    Studying with {activeTeacher.name} has transformed not just my yoga practice, but my entire approach to life.
                    Their wisdom, patience, and authentic teaching style create a space where true growth can happen.
                  </blockquote>
                  <div className="mt-4 flex items-center">
                    <div className="w-10 h-10 rounded-full bg-slate-200 dark:bg-slate-600 overflow-hidden mr-3">
                      <img src="https://placehold.co/100x100/e2e8f0/1e293b?text=S" alt="Student" className="w-full h-full object-cover" />
                    </div>
                    <div>
                      <p className="font-medium text-slate-900 dark:text-white">Sarah Johnson</p>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">Practicing for 3 years</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherSpotlight;