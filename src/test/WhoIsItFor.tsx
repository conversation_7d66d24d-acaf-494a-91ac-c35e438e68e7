import React, { useState } from 'react';

interface TargetAudienceItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  benefits: string[];
  cta: string;
  link: string;
}

const WhoIsItFor: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('practice');

  const targetAudience: TargetAudienceItem[] = [
    {
      id: 'practice',
      title: 'Improve Your Yoga Practice',
      description: 'For those looking to deepen their personal yoga journey, refine their technique, and experience authentic yoga in its birthplace.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
      ),
      benefits: [
        'Personalized guidance to improve your asanas',
        'Learn proper alignment and breathing techniques',
        'Deepen your understanding of yoga philosophy',
        'Practice in the spiritual environment of Rishikesh',
        'Connect with like-minded practitioners from around the world'
      ],
      cta: 'Explore Our Classes',
      link: '/classes'
    },
    {
      id: 'teacher',
      title: 'Become a Yoga Teacher',
      description: 'For aspiring teachers seeking comprehensive training and internationally recognized certification to share yoga with others.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
      ),
      benefits: [
        'Yoga Alliance certified 200-hour teacher training',
        'Learn to teach multiple yoga styles (Hatha, Vinyasa, Iyengar)',
        'Develop confidence in sequencing and adjustments',
        'Study anatomy, philosophy, and teaching methodology',
        'Graduate ready to teach internationally'
      ],
      cta: 'Discover Teacher Training',
      link: '/courses'
    },
    {
      id: 'wellness',
      title: 'Overcome Depression & Anxiety',
      description: 'For those seeking holistic approaches to mental health, stress reduction, and emotional balance through yogic practices.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
        </svg>
      ),
      benefits: [
        'Learn specific yoga and breathing techniques for anxiety',
        'Practice meditation for mental clarity and peace',
        'Understand the mind-body connection through yoga',
        'Develop daily routines for emotional balance',
        'Connect with supportive community during your healing journey'
      ],
      cta: 'Explore Wellness Programs',
      link: '/retreats'
    },
    {
      id: 'culture',
      title: 'Understand Indian Culture',
      description: 'For cultural enthusiasts wanting to immerse themselves in authentic Indian traditions, philosophy, and spiritual practices.',
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      ),
      benefits: [
        'Experience traditional Indian ceremonies and rituals',
        'Study ancient texts like the Bhagavad Gita and Yoga Sutras',
        'Learn about Ayurveda and holistic Indian wellness',
        'Visit sacred sites and temples in Rishikesh',
        'Participate in traditional music and chanting sessions'
      ],
      cta: 'Discover Cultural Programs',
      link: '/retreats'
    }
  ];

  const activeItem = targetAudience.find(item => item.id === activeTab) || targetAudience[0];

  return (
    <section id="who-is-it-for" className="py-20 md:py-32 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Who Is It For</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            Find Your <span className="text-amber-500">Path</span> With Us
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Our programs are designed for various needs and goals. Discover which path aligns with your journey.
          </p>
        </div>

        {/* Tabs for mobile */}
        <div className="md:hidden mb-8">
          <select 
            value={activeTab}
            onChange={(e) => setActiveTab(e.target.value)}
            className="w-full p-3 border border-slate-300 rounded-lg bg-white text-slate-900 focus:ring-2 focus:ring-amber-400 focus:border-transparent"
          >
            {targetAudience.map(item => (
              <option key={item.id} value={item.id}>{item.title}</option>
            ))}
          </select>
        </div>

        {/* Tabs for desktop */}
        <div className="hidden md:flex justify-center mb-12">
          <div className="inline-flex bg-amber-50 rounded-full p-1">
            {targetAudience.map(item => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeTab === item.id
                    ? 'bg-amber-400 text-slate-900 shadow-md'
                    : 'text-slate-700 hover:bg-amber-100'
                }`}
              >
                {item.title}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="flex flex-col lg:flex-row">
            {/* Left side - Content */}
            <div className="lg:w-2/3 p-8 md:p-12">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center text-amber-500 mr-4">
                  {activeItem.icon}
                </div>
                <h3 className="text-2xl font-bold text-slate-900">{activeItem.title}</h3>
              </div>
              
              <p className="text-slate-700 mb-8 text-lg">{activeItem.description}</p>
              
              <h4 className="text-lg font-semibold text-slate-900 mb-4">How You'll Benefit:</h4>
              <ul className="space-y-3 mb-8">
                {activeItem.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <svg className="w-5 h-5 text-amber-500 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <span className="text-slate-700">{benefit}</span>
                  </li>
                ))}
              </ul>
              
              <a
                href={activeItem.link}
                className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
              >
                {activeItem.cta}
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </a>
            </div>
            
            {/* Right side - Image */}
            <div className="lg:w-1/3 bg-amber-50 flex items-center justify-center p-8">
              <img
                src={`https://placehold.co/600x800/e2e8f0/1e293b?text=${activeItem.id}`}
                alt={activeItem.title}
                className="rounded-lg shadow-lg max-h-96 object-cover"
              />
            </div>
          </div>
        </div>

        {/* Testimonial */}
        <div className="mt-16 bg-amber-50 rounded-2xl p-8 md:p-12 shadow-lg">
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="md:w-1/4 flex justify-center">
              <div className="relative">
                <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-amber-100">
                  <img
                    src="https://placehold.co/400x400/e2e8f0/1e293b?text=Student"
                    alt="Student testimonial"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="absolute -bottom-2 -right-2 bg-amber-400 text-white p-2 rounded-full">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.983 3v7.391c0 5.704-3.731 9.57-8.983 10.609l-.995-2.151c2.432-.917 3.995-3.638 3.995-5.849h-4v-10h10zm14.017 0v7.391c0 5.704-3.748 9.571-9 10.609l-.996-2.151c2.433-.917 3.996-3.638 3.996-5.849h-3.983v-10h9.983z"/>
                  </svg>
                </div>
              </div>
            </div>
            <div className="md:w-3/4">
              <blockquote className="text-slate-700 text-lg italic leading-relaxed mb-6">
                "I came to Arya Yog Peeth feeling lost and struggling with anxiety. The combination of yoga, meditation, and the peaceful environment of Rishikesh transformed not just my practice, but my entire outlook on life. Yogi Arya's guidance was exactly what I needed."
              </blockquote>
              <div className="flex items-center">
                <div>
                  <p className="font-bold text-slate-900">Sarah Johnson</p>
                  <p className="text-sm text-slate-500">From London, United Kingdom</p>
                </div>
                <div className="ml-auto flex">
                  {[...Array(5)].map((_, i) => (
                    <svg
                      key={i}
                      className="w-5 h-5 text-amber-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoIsItFor;
