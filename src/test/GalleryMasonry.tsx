import React, { useState, useRef, useEffect } from 'react';

interface GalleryImage {
  id: number;
  src: string;
  alt: string;
  category: string;
  width: number;
  height: number;
}

const GalleryMasonry: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [modalImage, setModalImage] = useState<string | null>(null);
  const [modalIndex, setModalIndex] = useState<number>(0);
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'asana', name: 'Asana Practice' },
    { id: 'ashram', name: 'Ashram Life' },
    { id: 'teachers', name: 'Teachers' },
    { id: 'students', name: 'Students' },
    { id: 'rishikesh', name: 'Rishikes<PERSON>' }
  ];

  const galleryImages: GalleryImage[] = [
    {
      id: 1,
      src: "https://placehold.co/800x600/e2e8f0/1e293b?text=Yoga+Class",
      alt: "Morning yoga class by the Ganges",
      category: "asana",
      width: 800,
      height: 600
    },
    {
      id: 2,
      src: "https://placehold.co/600x800/e2e8f0/1e293b?text=Meditation",
      alt: "Students in meditation",
      category: "students",
      width: 600,
      height: 800
    },
    {
      id: 3,
      src: "https://placehold.co/800x800/e2e8f0/1e293b?text=Ashram+View",
      alt: "View of the ashram from above",
      category: "ashram",
      width: 800,
      height: 800
    },
    {
      id: 4,
      src: "https://placehold.co/900x600/e2e8f0/1e293b?text=Rishikesh",
      alt: "Rishikesh and the Ganges River",
      category: "rishikesh",
      width: 900,
      height: 600
    },
    {
      id: 5,
      src: "https://placehold.co/600x400/e2e8f0/1e293b?text=Yoga+Pose",
      alt: "Advanced yoga pose demonstration",
      category: "asana",
      width: 600,
      height: 400
    },
    {
      id: 6,
      src: "https://placehold.co/800x500/e2e8f0/1e293b?text=Teacher",
      alt: "Yoga teacher giving instructions",
      category: "teachers",
      width: 800,
      height: 500
    },
    {
      id: 7,
      src: "https://placehold.co/500x800/e2e8f0/1e293b?text=Graduation",
      alt: "Graduation ceremony",
      category: "students",
      width: 500,
      height: 800
    },
    {
      id: 8,
      src: "https://placehold.co/800x600/e2e8f0/1e293b?text=Laxman+Jhula",
      alt: "Laxman Jhula bridge",
      category: "rishikesh",
      width: 800,
      height: 600
    },
    {
      id: 9,
      src: "https://placehold.co/600x600/e2e8f0/1e293b?text=Dining+Hall",
      alt: "Ashram dining hall",
      category: "ashram",
      width: 600,
      height: 600
    },
    {
      id: 10,
      src: "https://placehold.co/900x700/e2e8f0/1e293b?text=Group+Class",
      alt: "Group yoga class",
      category: "asana",
      width: 900,
      height: 700
    },
    {
      id: 11,
      src: "https://placehold.co/700x900/e2e8f0/1e293b?text=Ganga+Aarti",
      alt: "Ganga Aarti ceremony",
      category: "rishikesh",
      width: 700,
      height: 900
    },
    {
      id: 12,
      src: "https://placehold.co/800x600/e2e8f0/1e293b?text=Anatomy+Class",
      alt: "Yoga anatomy class",
      category: "teachers",
      width: 800,
      height: 600
    }
  ];

  const filteredImages = galleryImages.filter(
    image => activeCategory === 'all' || image.category === activeCategory
  );

  const openModal = (index: number) => {
    setModalIndex(index);
    setModalImage(filteredImages[index].src);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setModalImage(null);
    document.body.style.overflow = 'auto';
  };

  const nextImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newIndex = (modalIndex + 1) % filteredImages.length;
    setModalIndex(newIndex);
    setModalImage(filteredImages[newIndex].src);
  };

  const prevImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    const newIndex = (modalIndex - 1 + filteredImages.length) % filteredImages.length;
    setModalIndex(newIndex);
    setModalImage(filteredImages[newIndex].src);
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Handle keyboard navigation in modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!modalImage) return;
      
      if (e.key === 'ArrowRight') {
        const newIndex = (modalIndex + 1) % filteredImages.length;
        setModalIndex(newIndex);
        setModalImage(filteredImages[newIndex].src);
      } else if (e.key === 'ArrowLeft') {
        const newIndex = (modalIndex - 1 + filteredImages.length) % filteredImages.length;
        setModalIndex(newIndex);
        setModalImage(filteredImages[newIndex].src);
      } else if (e.key === 'Escape') {
        closeModal();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [modalImage, modalIndex, filteredImages]);

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Our <span className="text-amber-500 dark:text-amber-400">Gallery</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore moments from our yoga school, ashram life, and the beautiful surroundings of Rishikesh.
            </p>
          </div>

          {/* Category filters */}
          <div className="flex flex-wrap justify-center gap-2 mb-10">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeCategory === category.id
                    ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                    : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Masonry gallery */}
          <div className="columns-1 sm:columns-2 lg:columns-3 gap-4 space-y-4">
            {filteredImages.map((image, index) => (
              <div 
                key={image.id}
                className="break-inside-avoid"
                onClick={() => openModal(index)}
              >
                <div className="relative overflow-hidden rounded-lg cursor-pointer group">
                  <div className="aspect-w-16 aspect-h-9 bg-slate-200 dark:bg-slate-700">
                    <img 
                      src={image.src} 
                      alt={image.alt}
                      className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                      style={{
                        aspectRatio: `${image.width}/${image.height}`,
                        objectFit: 'cover'
                      }}
                    />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                    <div className="p-4 w-full">
                      <p className="text-white text-sm">{image.alt}</p>
                      <span className="inline-block mt-2 px-2 py-1 bg-amber-400/80 text-slate-900 text-xs rounded-full">
                        {categories.find(cat => cat.id === image.category)?.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty state */}
          {filteredImages.length === 0 && (
            <div className="text-center py-20 bg-slate-50 dark:bg-slate-700 rounded-lg">
              <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <h3 className="text-xl font-medium text-slate-700 dark:text-slate-300 mb-2">No images found</h3>
              <p className="text-slate-500 dark:text-slate-400">
                There are no images in this category yet.
              </p>
            </div>
          )}

          {/* View more button */}
          {filteredImages.length > 0 && (
            <div className="text-center mt-10">
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors">
                View More Photos
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Lightbox Modal */}
      {modalImage && (
        <div 
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center"
          onClick={closeModal}
        >
          <div 
            className="relative w-full h-full flex items-center justify-center p-4"
            onClick={e => e.stopPropagation()}
          >
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-200 hover:scale-110"
              aria-label="Previous image"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <div className="max-w-5xl w-full h-full flex items-center justify-center">
              <img
                src={modalImage}
                alt={filteredImages[modalIndex].alt}
                className="max-w-full max-h-[90vh] object-contain rounded-lg"
              />
            </div>

            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-200 hover:scale-110"
              aria-label="Next image"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>

            <button
              onClick={closeModal}
              className="absolute top-4 right-4 w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center text-white transition-all duration-200 hover:scale-110"
              aria-label="Close modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>

            <div className="absolute bottom-4 left-0 right-0 text-center">
              <p className="text-white bg-black/50 inline-block px-4 py-2 rounded-lg">
                {modalIndex + 1} / {filteredImages.length}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GalleryMasonry;
