import React, { useState, useRef, useEffect } from 'react';

interface YogaLimb {
  id: string;
  name: string;
  sanskritName: string;
  description: string;
  icon: string;
}

const YogaPhilosophy: React.FC = () => {
  const [activeLimb, setActiveLimb] = useState<string>('yama');
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const yogaLimbs: YogaLimb[] = [
    {
      id: 'yama',
      name: 'Moral Disciplines',
      sanskritName: 'Yama',
      description: 'Ethical standards and sense of integrity, focusing on our behavior and how we conduct ourselves in life. The five yamas are: <PERSON><PERSON><PERSON> (non-violence), <PERSON><PERSON><PERSON> (truthfulness), <PERSON><PERSON><PERSON> (non-stealing), <PERSON><PERSON><PERSON><PERSON> (right use of energy), and <PERSON><PERSON><PERSON><PERSON><PERSON> (non-greed).',
      icon: '🕊️'
    },
    {
      id: 'niyama',
      name: 'Self-Discipline',
      sanskritName: 'Niyama',
      description: 'Personal observances for healthy living and spiritual enlightenment. The five niyamas are: Saucha (cleanliness), <PERSON><PERSON> (contentment), <PERSON><PERSON> (discipline), <PERSON><PERSON><PERSON><PERSON><PERSON> (self-study), and <PERSON><PERSON><PERSON><PERSON> (surrender).',
      icon: '🧘‍♀️'
    },
    {
      id: 'asana',
      name: 'Posture',
      sanskritName: 'Asana',
      description: 'The physical practice of yoga poses, which build strength, flexibility and confidence. The practice of asanas is the most commonly known aspect of yoga in the Western world.',
      icon: '🧘‍♂️'
    },
    {
      id: 'pranayama',
      name: 'Breath Control',
      sanskritName: 'Pranayama',
      description: 'Breathing techniques that control the flow of prana (life force energy) in the body. These techniques can energize, calm, or balance the practitioner.',
      icon: '💨'
    },
    {
      id: 'pratyahara',
      name: 'Withdrawal of Senses',
      sanskritName: 'Pratyahara',
      description: 'The practice of drawing awareness away from the external world and outside stimuli. It cultivates a detachment from the senses and helps us to look inward.',
      icon: '👁️'
    },
    {
      id: 'dharana',
      name: 'Concentration',
      sanskritName: 'Dharana',
      description: 'Focused concentration on a single point, preparing the mind for meditation. This involves training the mind to focus on one object or thought without distraction.',
      icon: '🔍'
    },
    {
      id: 'dhyana',
      name: 'Meditation',
      sanskritName: 'Dhyana',
      description: 'The state of being keenly aware without focus. In this state, the mind has been quieted, and in the stillness it produces few or no thoughts at all.',
      icon: '✨'
    },
    {
      id: 'samadhi',
      name: 'Enlightenment',
      sanskritName: 'Samadhi',
      description: 'The state of ecstasy and the experience of bliss. In this state, the meditator merges with their point of focus and transcends the Self altogether.',
      icon: '☀️'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <div ref={sectionRef} className="bg-gradient-to-br from-amber-50 to-amber-100/70 dark:from-slate-800 dark:to-slate-900 py-16 px-4 rounded-2xl overflow-hidden relative">
      {/* Background decorative elements */}
      <div className="absolute top-0 left-0 w-64 h-64 bg-amber-200 dark:bg-amber-900/20 rounded-full opacity-20 -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-amber-300 dark:bg-amber-800/20 rounded-full opacity-20 translate-x-1/3 translate-y-1/3"></div>
      
      <div ref={contentRef} className="max-w-6xl mx-auto opacity-0">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
            The Eight Limbs of <span className="text-amber-500 dark:text-amber-400">Yoga</span>
          </h2>
          <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Patanjali's Yoga Sutras describe the eight-fold path (Ashtanga) of yoga practice that leads to self-realization. Explore each limb to understand the complete yoga journey.
          </p>
        </div>

        {/* Limbs Navigation */}
        <div className="flex flex-wrap justify-center gap-2 mb-10">
          {yogaLimbs.map((limb) => (
            <button
              key={limb.id}
              onClick={() => setActiveLimb(limb.id)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 flex items-center ${
                activeLimb === limb.id
                  ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md scale-105'
                  : 'bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
              }`}
            >
              <span className="mr-2">{limb.icon}</span>
              {limb.sanskritName}
            </button>
          ))}
        </div>

        {/* Active Limb Content */}
        {yogaLimbs.map((limb) => (
          <div
            key={limb.id}
            className={`transition-all duration-500 ${
              activeLimb === limb.id ? 'opacity-100 scale-100' : 'opacity-0 scale-95 hidden'
            }`}
          >
            <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
              <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
                <div className="flex-shrink-0 w-24 h-24 flex items-center justify-center bg-amber-100 dark:bg-amber-900/30 text-amber-500 dark:text-amber-400 text-4xl rounded-full">
                  {limb.icon}
                </div>
                <div>
                  <div className="mb-4">
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                      {limb.name}
                    </h3>
                    <p className="text-amber-500 dark:text-amber-400 font-medium">
                      {limb.sanskritName}
                    </p>
                  </div>
                  <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                    {limb.description}
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Navigation Arrows */}
        <div className="flex justify-center mt-8 space-x-4">
          <button
            onClick={() => {
              const currentIndex = yogaLimbs.findIndex(limb => limb.id === activeLimb);
              const prevIndex = currentIndex > 0 ? currentIndex - 1 : yogaLimbs.length - 1;
              setActiveLimb(yogaLimbs[prevIndex].id);
            }}
            className="w-10 h-10 rounded-full bg-white dark:bg-slate-700 flex items-center justify-center shadow-md hover:bg-amber-100 dark:hover:bg-slate-600 transition-colors"
            aria-label="Previous limb"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-700 dark:text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            onClick={() => {
              const currentIndex = yogaLimbs.findIndex(limb => limb.id === activeLimb);
              const nextIndex = currentIndex < yogaLimbs.length - 1 ? currentIndex + 1 : 0;
              setActiveLimb(yogaLimbs[nextIndex].id);
            }}
            className="w-10 h-10 rounded-full bg-white dark:bg-slate-700 flex items-center justify-center shadow-md hover:bg-amber-100 dark:hover:bg-slate-600 transition-colors"
            aria-label="Next limb"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-700 dark:text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default YogaPhilosophy;
