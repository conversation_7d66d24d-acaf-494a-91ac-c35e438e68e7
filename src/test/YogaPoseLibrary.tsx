import React, { useState, useRef, useEffect } from 'react';

interface YogaPose {
  id: string;
  name: string;
  sanskritName: string;
  image: string;
  description: string;
  benefits: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  categories: string[];
  steps: string[];
  modifications: string[];
  contraindications?: string[];
}

const YogaPoseLibrary: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [activeDifficulty, setActiveDifficulty] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedPose, setSelectedPose] = useState<YogaPose | null>(null);
  const [showModal, setShowModal] = useState<boolean>(false);
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Categories for filtering
  const categories = [
    { id: 'all', name: 'All Poses' },
    { id: 'standing', name: 'Standing Poses' },
    { id: 'seated', name: 'Seated Poses' },
    { id: 'backbends', name: 'Backbends' },
    { id: 'twists', name: 'Twists' },
    { id: 'inversions', name: 'Inversions' },
    { id: 'arm-balances', name: 'Arm Balances' },
    { id: 'restorative', name: 'Restorative' },
  ];

  // Difficulty levels
  const difficultyLevels = [
    { id: 'all', name: 'All Levels' },
    { id: 'beginner', name: 'Beginner' },
    { id: 'intermediate', name: 'Intermediate' },
    { id: 'advanced', name: 'Advanced' },
  ];

  // Sample yoga poses data
  const yogaPoses: YogaPose[] = [
    {
      id: 'downward-dog',
      name: 'Downward-Facing Dog',
      sanskritName: 'Adho Mukha Svanasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Downward+Dog',
      description: 'A common pose in many yoga sequences that stretches and strengthens the entire body.',
      benefits: [
        'Strengthens the arms, shoulders, and legs',
        'Stretches the hamstrings, calves, and spine',
        'Improves circulation',
        'Calms the nervous system',
        'Energizes the body'
      ],
      difficulty: 'beginner',
      categories: ['standing', 'inversions'],
      steps: [
        'Start on your hands and knees with your wrists under your shoulders and knees under your hips.',
        'Spread your fingers wide and press firmly through your palms and knuckles.',
        'Tuck your toes and lift your knees off the floor.',
        'Straighten your legs as much as possible and lift your hips high.',
        'Draw your shoulder blades down your back and relax your neck.',
        'Keep your head between your arms, not hanging down.',
        'Hold for 5-10 breaths.'
      ],
      modifications: [
        'Bend your knees if your hamstrings are tight.',
        'Use blocks under your hands if your wrists are sensitive.',
        'Drop your head completely if your neck is tense.'
      ]
    },
    {
      id: 'warrior-2',
      name: 'Warrior II',
      sanskritName: 'Virabhadrasana II',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Warrior+II',
      description: 'A powerful standing pose that builds strength and stamina in the legs and opens the hips and chest.',
      benefits: [
        'Strengthens the legs, ankles, and feet',
        'Opens the hips, chest, and shoulders',
        'Improves focus and concentration',
        'Builds stamina and endurance',
        'Stimulates abdominal organs'
      ],
      difficulty: 'beginner',
      categories: ['standing'],
      steps: [
        'Stand with your feet wide apart, about 4-5 feet.',
        'Turn your right foot out 90 degrees and your left foot in slightly.',
        'Align your right heel with the arch of your left foot.',
        'Extend your arms parallel to the floor, reaching actively through the fingertips.',
        'Bend your right knee until it is directly over the ankle.',
        'Turn your head to gaze over your right fingertips.',
        'Hold for 5-10 breaths, then repeat on the other side.'
      ],
      modifications: [
        'Reduce the stance width if you feel unstable.',
        'Don\'t bend the knee as deeply if you have knee issues.',
        'Rest your hands on your hips if your shoulders are tight.'
      ]
    },
    {
      id: 'tree-pose',
      name: 'Tree Pose',
      sanskritName: 'Vrksasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Tree+Pose',
      description: 'A balancing pose that improves focus, concentration, and stability.',
      benefits: [
        'Improves balance and stability',
        'Strengthens the legs, ankles, and feet',
        'Opens the hips',
        'Improves focus and concentration',
        'Builds confidence'
      ],
      difficulty: 'beginner',
      categories: ['standing', 'balancing'],
      steps: [
        'Start standing with your feet together and weight evenly distributed.',
        'Shift your weight to your left foot and lift your right foot off the floor.',
        'Place the sole of your right foot on your inner left thigh, calf, or ankle (avoid the knee).',
        'Bring your palms together at your heart center or extend your arms overhead.',
        'Fix your gaze on a point in front of you to help with balance.',
        'Hold for 5-10 breaths, then repeat on the other side.'
      ],
      modifications: [
        'Place your foot on your ankle or use a wall for support if balance is challenging.',
        'Keep your hands on your hips if raising them affects your balance.',
        'Use a chair or wall for support if needed.'
      ]
    },
    {
      id: 'seated-forward-bend',
      name: 'Seated Forward Bend',
      sanskritName: 'Paschimottanasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Seated+Forward+Bend',
      description: 'A calming forward bend that stretches the entire back of the body.',
      benefits: [
        'Stretches the spine, hamstrings, and calves',
        'Calms the mind and reduces stress',
        'Stimulates the liver, kidneys, and digestive organs',
        'Relieves headaches and fatigue',
        'Reduces mild depression and anxiety'
      ],
      difficulty: 'beginner',
      categories: ['seated', 'forward-bends'],
      steps: [
        'Sit on the floor with your legs extended in front of you.',
        'Flex your feet and engage your leg muscles.',
        'Inhale and lengthen your spine, sitting up tall.',
        'Exhale and hinge at your hips to fold forward.',
        'Reach for your feet, ankles, or shins, wherever is comfortable.',
        'Relax your head and neck, focusing on lengthening your spine.',
        'Hold for 30 seconds to 3 minutes, breathing deeply.'
      ],
      modifications: [
        'Bend your knees if your hamstrings are tight.',
        'Use a strap around your feet if you can\'t reach them.',
        'Sit on a folded blanket to tilt your pelvis forward.'
      ],
      contraindications: [
        'Avoid with lower back injuries',
        'Practice with caution if you have sciatica'
      ]
    },
    {
      id: 'bridge-pose',
      name: 'Bridge Pose',
      sanskritName: 'Setu Bandha Sarvangasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Bridge+Pose',
      description: 'A gentle backbend that opens the chest and strengthens the back muscles.',
      benefits: [
        'Strengthens the back, glutes, and hamstrings',
        'Opens the chest and shoulders',
        'Stimulates the thyroid and improves metabolism',
        'Calms the mind and reduces anxiety',
        'Relieves back pain and improves posture'
      ],
      difficulty: 'beginner',
      categories: ['backbends'],
      steps: [
        'Lie on your back with your knees bent and feet flat on the floor, hip-width apart.',
        'Place your arms alongside your body with palms facing down.',
        'Press your feet and arms into the floor and lift your hips toward the ceiling.',
        'Roll your shoulders underneath you and clasp your hands below your pelvis.',
        'Keep your thighs parallel and engage your glutes.',
        'Hold for 30 seconds to 1 minute, breathing deeply.',
        'Release by slowly rolling your spine back down to the floor.'
      ],
      modifications: [
        'Place a block under your sacrum for a supported version.',
        'Keep your arms alongside your body if shoulder mobility is limited.',
        'Keep your knees wider apart if you have lower back issues.'
      ]
    },
    {
      id: 'crow-pose',
      name: 'Crow Pose',
      sanskritName: 'Bakasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Crow+Pose',
      description: 'An arm balance that builds strength and focus.',
      benefits: [
        'Strengthens the arms, wrists, and core',
        'Improves balance and focus',
        'Builds confidence',
        'Opens the upper back',
        'Prepares for more advanced arm balances'
      ],
      difficulty: 'intermediate',
      categories: ['arm-balances'],
      steps: [
        'Start in a squat position with your feet close together.',
        'Place your hands on the floor shoulder-width apart, fingers spread wide.',
        'Lift your hips and come onto the balls of your feet.',
        'Bend your elbows and place your knees on the backs of your upper arms.',
        'Shift your weight forward into your hands and lift one foot, then the other.',
        'Keep your gaze slightly forward and engage your core.',
        'Hold for 5-10 breaths.'
      ],
      modifications: [
        'Place a block or cushion under your forehead for support.',
        'Practice with your feet on a block to get used to the weight shift.',
        'Keep one foot on the floor until you build more strength.'
      ],
      contraindications: [
        'Avoid with wrist injuries',
        'Practice with caution if you have shoulder issues'
      ]
    },
    {
      id: 'headstand',
      name: 'Headstand',
      sanskritName: 'Sirsasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Headstand',
      description: 'An advanced inversion that builds strength, balance, and focus.',
      benefits: [
        'Strengthens the shoulders, arms, and core',
        'Improves balance and focus',
        'Stimulates the pituitary and pineal glands',
        'Increases blood flow to the brain',
        'Builds confidence and mental clarity'
      ],
      difficulty: 'advanced',
      categories: ['inversions'],
      steps: [
        'Kneel on the floor and place your forearms down with elbows shoulder-width apart.',
        'Interlace your fingers and create a cup with your hands.',
        'Place the crown of your head on the floor, cradled by your hands.',
        'Lift your knees and walk your feet toward your head.',
        'Engage your core and lift one leg up, then the other.',
        'Straighten your legs and align your body vertically.',
        'Hold for 10-60 seconds, breathing steadily.'
      ],
      modifications: [
        'Practice against a wall for support.',
        'Keep your knees bent as you build strength.',
        'Try dolphin pose first to build shoulder strength.'
      ],
      contraindications: [
        'Avoid with neck injuries',
        'Avoid with high blood pressure or heart conditions',
        'Avoid during menstruation',
        'Avoid with glaucoma or detached retina'
      ]
    },
    {
      id: 'childs-pose',
      name: 'Child\'s Pose',
      sanskritName: 'Balasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Childs+Pose',
      description: 'A restful pose that gently stretches the back and promotes relaxation.',
      benefits: [
        'Gently stretches the lower back, hips, and thighs',
        'Calms the mind and reduces stress',
        'Relieves tension in the back, shoulders, and chest',
        'Promotes relaxation and steady breathing',
        'Can help relieve back pain'
      ],
      difficulty: 'beginner',
      categories: ['restorative'],
      steps: [
        'Kneel on the floor with your big toes touching and knees wide apart.',
        'Sit back on your heels and fold forward, extending your arms in front of you.',
        'Rest your forehead on the floor and relax your shoulders.',
        'Breathe deeply, feeling the expansion of your back with each inhale.',
        'Hold for 1-5 minutes or as long as comfortable.'
      ],
      modifications: [
        'Place a cushion or folded blanket between your thighs and calves if there\'s discomfort.',
        'Rest your head on a block or cushion if it doesn\'t reach the floor.',
        'Keep your arms alongside your body if shoulder mobility is limited.'
      ]
    }
  ];

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setShowModal(false);
      }
    };

    if (showModal) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showModal]);

  // Filter poses based on category, difficulty, and search query
  const filteredPoses = yogaPoses.filter(pose => {
    const matchesCategory = activeCategory === 'all' || pose.categories.includes(activeCategory);
    const matchesDifficulty = activeDifficulty === 'all' || pose.difficulty === activeDifficulty;
    const matchesSearch = pose.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          pose.sanskritName.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesDifficulty && matchesSearch;
  });

  const handlePoseClick = (pose: YogaPose) => {
    setSelectedPose(pose);
    setShowModal(true);
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Yoga <span className="text-amber-500 dark:text-amber-400">Pose Library</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore our collection of yoga poses with detailed instructions, benefits, and modifications for all levels.
            </p>
          </div>

          {/* Search and filter section */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              {/* Search input */}
              <div className="md:col-span-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search poses..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-3 pl-10 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                  />
                  <svg className="w-5 h-5 text-slate-400 absolute left-3 top-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
              </div>

              {/* Difficulty filter */}
              <div className="md:col-span-1">
                <select
                  value={activeDifficulty}
                  onChange={(e) => setActiveDifficulty(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                >
                  {difficultyLevels.map(level => (
                    <option key={level.id} value={level.id}>{level.name}</option>
                  ))}
                </select>
              </div>

              {/* Reset filters button */}
              <div className="md:col-span-1 flex items-center">
                <button
                  onClick={() => {
                    setActiveCategory('all');
                    setActiveDifficulty('all');
                    setSearchQuery('');
                  }}
                  className="px-4 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300 flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Reset Filters
                </button>
              </div>
            </div>

            {/* Category tabs */}
            <div className="flex flex-wrap gap-2 mb-6">
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                      : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Poses grid */}
          {filteredPoses.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPoses.map(pose => (
                <div
                  key={pose.id}
                  onClick={() => handlePoseClick(pose)}
                  className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02]"
                >
                  <div className="relative h-48">
                    <img
                      src={pose.image}
                      alt={pose.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2">
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                        pose.difficulty === 'beginner'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : pose.difficulty === 'intermediate'
                            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      }`}>
                        {pose.difficulty.charAt(0).toUpperCase() + pose.difficulty.slice(1)}
                      </span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1">{pose.name}</h3>
                    <p className="text-amber-500 dark:text-amber-400 text-sm mb-2">{pose.sanskritName}</p>
                    <p className="text-slate-700 dark:text-slate-300 text-sm line-clamp-2">{pose.description}</p>
                    <div className="mt-3 flex flex-wrap gap-1">
                      {pose.categories.slice(0, 2).map((category, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-xs"
                        >
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </span>
                      ))}
                      {pose.categories.length > 2 && (
                        <span className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-xs">
                          +{pose.categories.length - 2}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-slate-50 dark:bg-slate-700 rounded-xl">
              <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">No poses found</h3>
              <p className="text-slate-700 dark:text-slate-300">Try adjusting your filters or search query.</p>
            </div>
          )}
        </div>
      </div>

      {/* Pose detail modal */}
      {showModal && selectedPose && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div
            ref={modalRef}
            className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="sticky top-0 bg-white dark:bg-slate-800 z-10 flex justify-between items-center p-4 border-b border-slate-200 dark:border-slate-700">
              <h3 className="text-xl font-bold text-slate-900 dark:text-white">{selectedPose.name}</h3>
              <button
                onClick={() => setShowModal(false)}
                className="p-2 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <img
                    src={selectedPose.image}
                    alt={selectedPose.name}
                    className="w-full h-auto rounded-lg"
                  />
                </div>
                <div>
                  <p className="text-amber-500 dark:text-amber-400 text-lg mb-4">{selectedPose.sanskritName}</p>
                  <p className="text-slate-700 dark:text-slate-300 mb-4">{selectedPose.description}</p>
                  
                  <div className="mb-4">
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">Difficulty</h4>
                    <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                      selectedPose.difficulty === 'beginner'
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        : selectedPose.difficulty === 'intermediate'
                          ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    }`}>
                      {selectedPose.difficulty.charAt(0).toUpperCase() + selectedPose.difficulty.slice(1)}
                    </span>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">Categories</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedPose.categories.map((category, index) => (
                        <span
                          key={index}
                          className="inline-block px-3 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-sm"
                        >
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Benefits</h4>
                  <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                    {selectedPose.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start">
                        <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>
                
                {selectedPose.contraindications && (
                  <div>
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Contraindications</h4>
                    <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                      {selectedPose.contraindications.map((contraindication, index) => (
                        <li key={index} className="flex items-start">
                          <svg className="w-5 h-5 text-red-500 dark:text-red-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                          </svg>
                          {contraindication}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">How to Practice</h4>
                <ol className="space-y-2 text-slate-700 dark:text-slate-300">
                  {selectedPose.steps.map((step, index) => (
                    <li key={index} className="flex items-start">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 flex items-center justify-center mr-2 mt-0.5 text-sm font-medium">
                        {index + 1}
                      </span>
                      <span>{step}</span>
                    </li>
                  ))}
                </ol>
              </div>
              
              <div>
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Modifications</h4>
                <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                  {selectedPose.modifications.map((modification, index) => (
                    <li key={index} className="flex items-start">
                      <svg className="w-5 h-5 text-blue-500 dark:text-blue-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      {modification}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default YogaPoseLibrary;