import React, { useState, useEffect, useRef } from 'react';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  image: string;
  quote: string;
  course: string;
  rating: number;
}

const TestimonialCarousel: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [autoplay, setAutoplay] = useState(true);
  const autoplayTimerRef = useRef<number | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: "<PERSON>",
      location: "London, UK",
      image: "https://placehold.co/300x300/e2e8f0/1e293b?text=Sarah+J",
      quote: "My experience at Shanti Yog Peeth transformed not just my yoga practice but my entire perspective on life. The teachers were incredibly knowledgeable and supportive throughout my journey.",
      course: "200-Hour YTT",
      rating: 5
    },
    {
      id: 2,
      name: "<PERSON>",
      location: "Toronto, Canada",
      image: "https://placehold.co/300x300/e2e8f0/1e293b?text=<PERSON>+C",
      quote: "The authentic approach to yoga at Shanti Yog Peeth was exactly what I was looking for. Located in the spiritual heart of Rishikesh, every day felt like a new discovery of yogic wisdom.",
      course: "300-Hour Advanced YTT",
      rating: 5
    },
    {
      id: 3,
      name: "Priya Sharma",
      location: "Melbourne, Australia",
      image: "https://placehold.co/300x300/e2e8f0/1e293b?text=Priya+S",
      quote: "As someone who was nervous about traveling to India alone, I found a welcoming family at Shanti Yog Peeth. The staff made me feel at home, and the training exceeded all my expectations.",
      course: "Meditation Retreat",
      rating: 4
    },
    {
      id: 4,
      name: "David Müller",
      location: "Berlin, Germany",
      image: "https://placehold.co/300x300/e2e8f0/1e293b?text=David+M",
      quote: "The balance between physical practice, philosophy, and spiritual teachings was perfect. I left with not only a certificate but a profound understanding of yoga as a way of life.",
      course: "100-Hour Yin Yoga",
      rating: 5
    },
    {
      id: 5,
      name: "Aiko Tanaka",
      location: "Tokyo, Japan",
      image: "https://placehold.co/300x300/e2e8f0/1e293b?text=Aiko+T",
      quote: "The location by the Ganges River created the perfect atmosphere for deepening my practice. The teachers' dedication to preserving authentic yoga traditions was inspiring.",
      course: "Ayurveda & Yoga Retreat",
      rating: 5
    }
  ];

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true);
      setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  const prevSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true);
      setActiveIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== activeIndex) {
      setIsAnimating(true);
      setActiveIndex(index);
      setTimeout(() => setIsAnimating(false), 500);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  useEffect(() => {
    if (autoplay) {
      autoplayTimerRef.current = window.setInterval(() => {
        nextSlide();
      }, 5000);
    }

    return () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current);
      }
    };
  }, [autoplay, activeIndex, isAnimating]);

  // Pause autoplay when user interacts with carousel
  const handleUserInteraction = () => {
    setAutoplay(false);
    // Resume autoplay after 10 seconds of inactivity
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
    }
    setTimeout(() => setAutoplay(true), 10000);
  };

  return (
    <div 
      ref={sectionRef}
      className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-slate-800 dark:to-slate-900 rounded-2xl shadow-xl overflow-hidden"
      onClick={handleUserInteraction}
      onMouseEnter={() => setAutoplay(false)}
      onMouseLeave={() => setAutoplay(true)}
    >
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              What Our <span className="text-amber-500 dark:text-amber-400">Students</span> Say
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Hear from our global community of yoga practitioners who have experienced the transformative journey at Shanti Yog Peeth.
            </p>
          </div>

          <div className="relative max-w-4xl mx-auto">
            {/* Testimonial Slides */}
            <div className="overflow-hidden">
              <div 
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${activeIndex * 100}%)` }}
              >
                {testimonials.map((testimonial) => (
                  <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                    <div className="bg-white dark:bg-slate-700 rounded-xl shadow-lg p-6 md:p-8">
                      <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
                        <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden flex-shrink-0 border-4 border-amber-200 dark:border-amber-700">
                          <img 
                            src={testimonial.image} 
                            alt={testimonial.name} 
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center mb-4">
                            {[...Array(5)].map((_, i) => (
                              <svg 
                                key={i} 
                                className={`w-5 h-5 ${i < testimonial.rating ? 'text-amber-400' : 'text-gray-300 dark:text-gray-600'}`}
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                          </div>
                          <blockquote className="text-slate-700 dark:text-slate-200 italic mb-4 text-lg">
                            "{testimonial.quote}"
                          </blockquote>
                          <div>
                            <p className="font-bold text-slate-900 dark:text-white">{testimonial.name}</p>
                            <div className="flex flex-wrap items-center gap-2 text-sm text-slate-500 dark:text-slate-400">
                              <span>{testimonial.location}</span>
                              <span className="w-1 h-1 rounded-full bg-amber-400"></span>
                              <span>{testimonial.course}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 md:translate-x-0 w-10 h-10 rounded-full bg-white dark:bg-slate-800 shadow-md flex items-center justify-center z-10 hover:bg-amber-50 dark:hover:bg-slate-700 transition-colors"
              aria-label="Previous testimonial"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-700 dark:text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 md:translate-x-0 w-10 h-10 rounded-full bg-white dark:bg-slate-800 shadow-md flex items-center justify-center z-10 hover:bg-amber-50 dark:hover:bg-slate-700 transition-colors"
              aria-label="Next testimonial"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-700 dark:text-slate-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center space-x-2 mt-6">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === activeIndex
                    ? 'bg-amber-500 dark:bg-amber-400 w-6'
                    : 'bg-amber-200 dark:bg-slate-600 hover:bg-amber-300 dark:hover:bg-slate-500'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialCarousel;
