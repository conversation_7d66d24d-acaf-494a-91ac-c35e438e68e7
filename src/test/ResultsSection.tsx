import React, { useRef, useEffect } from 'react';
import { ResultItem } from './types';

const ResultsSection: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const headingRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (headingRef.current) headingRef.current.classList.add('animate-fade-in-up');
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const results: ResultItem[] = [
    {
      title: "Physical Benefits",
      description: "Experience improved flexibility, strength, and posture. Many students report relief from chronic pain and better overall physical health.",
      icon: "🧘‍♀️",
      stats: {
        value: "87%",
        label: "report improved flexibility"
      }
    },
    {
      title: "Mental Benefits",
      description: "Develop mental clarity, reduced anxiety, and improved focus. Our practices help calm the mind and build emotional resilience.",
      icon: "🧠",
      stats: {
        value: "92%",
        label: "experience reduced stress"
      }
    },
    {
      title: "Spiritual Benefits",
      description: "Connect with your deeper self, develop mindfulness, and find purpose. Many students report a profound sense of inner peace.",
      icon: "✨",
      stats: {
        value: "78%",
        label: "report increased mindfulness"
      }
    },
    {
      title: "Career Benefits",
      description: "For teacher training students, gain the skills and certification to teach yoga professionally around the world.",
      icon: "🌎",
      stats: {
        value: "65%",
        label: "teach professionally after graduation"
      }
    }
  ];

  const testimonials = [
    {
      before: "I struggled with chronic back pain for years and had tried everything.",
      after: "After just one month of consistent practice at Arya Yog Peeth, my pain decreased by 70%. Now I can enjoy activities I thought I'd never do again.",
      name: "Michael S., USA",
      image: "https://placehold.co/100x100/e2e8f0/1e293b?text=MS"
    },
    {
      before: "My anxiety was so severe that it affected my daily life and relationships.",
      after: "The breathing techniques and meditation practices I learned have given me tools to manage my anxiety. I feel like I've gotten my life back.",
      name: "Priya K., India",
      image: "https://placehold.co/100x100/e2e8f0/1e293b?text=PK"
    }
  ];

  return (
    <section
      id="results-section"
      ref={sectionRef}
      className="py-20 md:py-32 bg-amber-50"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div 
          ref={headingRef}
          className="text-center mb-16 opacity-0"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Transformation</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            The <span className="text-amber-500">Results</span> You Can Expect
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Our students experience profound transformations across all dimensions of their being. Here's what you can expect from your journey with us.
          </p>
        </div>

        <div 
          ref={contentRef}
          className="opacity-0"
        >
          {/* Results Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {results.map((result, index) => (
              <div 
                key={index}
                className="bg-white rounded-xl shadow-md p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1"
              >
                <div className="text-4xl mb-4">{result.icon}</div>
                <h3 className="text-xl font-bold text-slate-900 mb-3">{result.title}</h3>
                <p className="text-slate-700 mb-6 text-sm">{result.description}</p>
                {result.stats && (
                  <div className="bg-amber-100/50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-amber-500">{result.stats.value}</div>
                    <div className="text-sm text-slate-600">{result.stats.label}</div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Before/After Testimonials */}
          <h3 className="text-2xl font-bold text-center text-slate-900 mb-8">Before & After Stories</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {testimonials.map((testimonial, index) => (
              <div 
                key={index}
                className="bg-white rounded-xl shadow-md overflow-hidden"
              >
                <div className="flex flex-col md:flex-row">
                  <div className="md:w-1/3 bg-amber-100 p-6 flex flex-col justify-center items-center">
                    <div className="text-center mb-4">
                      <img 
                        src={testimonial.image} 
                        alt={testimonial.name} 
                        className="w-16 h-16 rounded-full mx-auto mb-2"
                      />
                      <p className="font-medium text-slate-900">{testimonial.name}</p>
                    </div>
                    <div className="text-center">
                      <span className="px-3 py-1 bg-amber-200 rounded-full text-xs text-amber-800">
                        Verified Student
                      </span>
                    </div>
                  </div>
                  <div className="md:w-2/3 p-6">
                    <div className="mb-4">
                      <h4 className="font-semibold text-slate-900 mb-2">Before:</h4>
                      <p className="text-slate-700 italic">"{testimonial.before}"</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-slate-900 mb-2">After:</h4>
                      <p className="text-slate-700 italic">"{testimonial.after}"</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <a
              href="#faq"
              className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
            >
              Common Questions
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResultsSection;