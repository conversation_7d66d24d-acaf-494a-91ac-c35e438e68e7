import { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { faqItems } from '../data/faqData';

export default function FAQ() {
  const [activeIndex, setActiveIndex] = useState<number | null>(0);

  const toggleAccordion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <section id="faq" className="py-20 md:py-32 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">FAQ</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            Frequently Asked <span className="text-amber-500">Questions</span>
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Find answers to common questions about our programs, accommodations, and more.
            If you don't see your question here, feel free to reach out to us directly.
          </p>
        </div>

        {/* FAQ accordion */}
        <div className="max-w-3xl mx-auto">
          <div className="space-y-4">
            {faqItems.map((faq, index) => (
              <div
                key={index}
                className="bg-amber-50 rounded-lg overflow-hidden transition-all duration-300"
              >
                <button
                  onClick={() => toggleAccordion(index)}
                  className="flex justify-between items-center w-full p-5 text-left"
                >
                  <span className="font-semibold text-slate-900">{faq.question}</span>
                  <ChevronDown
                    className={`w-5 h-5 text-amber-500 transition-transform duration-300 ${
                      activeIndex === index ? 'transform rotate-180' : ''
                    }`}
                  />
                </button>
                <div
                  className={`px-5 overflow-hidden transition-all duration-300 ${
                    activeIndex === index ? 'max-h-96 pb-5' : 'max-h-0'
                  }`}
                >
                  <p className="text-slate-700">{faq.answer}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}