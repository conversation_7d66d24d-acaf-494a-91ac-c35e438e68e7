import React from 'react';

const StillHaveQuestions: React.FC = () => {
  return (
    <section id="still-have-questions" className="py-20 md:py-32 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-amber-50 to-amber-100/70"></div>
      <div className="absolute top-0 left-0 w-64 h-64 bg-amber-200 rounded-full opacity-20 -translate-x-1/2 -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-amber-300 rounded-full opacity-20 translate-x-1/3 translate-y-1/3"></div>
      <div className="absolute top-1/4 right-10 w-20 h-20 bg-amber-400 rounded-full opacity-10"></div>
      <div className="absolute bottom-1/4 left-10 w-32 h-32 bg-amber-400 rounded-full opacity-10"></div>

      {/* Decorative elements */}
      <div className="absolute top-10 left-10 text-6xl text-amber-200 opacity-30 font-serif">?</div>
      <div className="absolute bottom-10 right-10 text-8xl text-amber-200 opacity-30 font-serif">?</div>
      <div className="absolute top-1/3 right-1/4 text-5xl text-amber-200 opacity-20 font-serif">?</div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white/80 backdrop-blur-lg rounded-3xl shadow-xl p-8 md:p-12 border border-amber-100">
            <div className="text-center mb-8">
              <div className="inline-block mb-4 relative">
                <span className="relative z-10 text-amber-500 uppercase tracking-wider text-sm font-semibold bg-amber-50 px-4 py-1 rounded-full border border-amber-200">
                  Get in Touch
                </span>
                <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-24 h-2 bg-amber-200 rounded-full opacity-50 blur-sm"></div>
              </div>

              <h2 className="text-3xl md:text-5xl font-bold mb-6 text-slate-900 relative inline-block">
                Still Have <span className="text-amber-500">Questions?</span>
                <div className="absolute -bottom-2 left-0 right-0 h-3 bg-amber-200 opacity-30 -z-10 skew-x-3"></div>
              </h2>

              <p className="text-slate-700 text-lg max-w-2xl mx-auto mb-10">
                If you couldn't find the answer to your question, feel free to reach out to us directly.
                We're here to help you on your yoga journey.
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-6 rounded-2xl border border-purple-100 hover:shadow-lg transition-all duration-300 group">
                <div className="flex items-start">
                  <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white p-3 rounded-xl shadow-md group-hover:shadow-pink-200 transition-all duration-300 mr-4">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-2">Ask on Instagram</h3>
                    <p className="text-slate-600 mb-4">Follow us and send a direct message with your questions.</p>
                    <a
                      href="https://www.instagram.com/aryayogpeeth/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-pink-600 font-medium group-hover:text-pink-700 transition-colors"
                    >
                      @aryayogpeeth
                      <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-br from-amber-50 to-amber-100 p-6 rounded-2xl border border-amber-200 hover:shadow-lg transition-all duration-300 group">
                <div className="flex items-start">
                  <div className="bg-amber-400 text-slate-900 p-3 rounded-xl shadow-md group-hover:shadow-amber-200 transition-all duration-300 mr-4">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-2">Contact Us Directly</h3>
                    <p className="text-slate-600 mb-4">Fill out our contact form and we'll get back to you promptly.</p>
                    <a
                      href="#contact"
                      className="inline-flex items-center text-amber-600 font-medium group-hover:text-amber-700 transition-colors"
                    >
                      Go to contact form
                      <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-10 text-center">
              <p className="text-slate-500 text-sm">
                Typical response time: <span className="font-medium text-slate-700">Within 24 hours</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StillHaveQuestions;
