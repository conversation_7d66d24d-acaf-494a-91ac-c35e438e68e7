import React, { useState, useRef, useEffect } from 'react';
import { TargetAudienceItem } from './types';

const TargetAudience: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>("practice-improvers");
  const sectionRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const audienceData: TargetAudienceItem[] = [
    {
      id: "practice-improvers",
      title: "People Looking to Improve Their Yoga Practice",
      description: "Whether you're a beginner or have been practicing for years, our programs are designed to deepen your understanding and refine your technique.",
      benefits: [
        "Personalized guidance to improve alignment",
        "Progressive approach suitable for all levels",
        "Focus on building a sustainable practice",
        "Integration of philosophy with physical practice"
      ],
      programs: ["Drop-in Classes", "Workshops", "Short Retreats"],
      testimonial: {
        text: "I came to Arya Yog Peeth as a beginner and left with a solid foundation. The teachers took time to correct my alignment and explain the philosophy behind each pose.",
        author: "Lauren Kelly, USA",
        image: "https://placehold.co/120x120/e7a974/ffffff?text=LK"
      }
    },
    {
      id: "aspiring-teachers",
      title: "Aspiring Yoga Teachers",
      description: "Transform your passion for yoga into a fulfilling career with our comprehensive teacher training programs that blend traditional knowledge with modern teaching methodologies.",
      benefits: [
        "Internationally recognized certification",
        "Hands-on teaching experience",
        "Business of yoga modules",
        "Mentorship from experienced teachers"
      ],
      programs: ["200-Hour TTC", "300-Hour Advanced TTC", "Specialized Workshops"],
      testimonial: {
        text: "The teacher training at Arya Yog Peeth was transformative. Not only did I deepen my practice, but I gained the confidence and skills to teach effectively.",
        author: "Olya, Russia",
        image: "https://placehold.co/120x120/e7a974/ffffff?text=Olya"
      }
    },
    {
      id: "mental-health",
      title: "People Dealing with Depression/Anxiety",
      description: "Experience the therapeutic benefits of yoga and meditation in a supportive environment designed to promote mental wellbeing and emotional balance.",
      benefits: [
        "Stress reduction techniques",
        "Mindfulness and meditation practices",
        "Yoga therapy approaches",
        "Supportive community environment"
      ],
      programs: ["Therapeutic Yoga Classes", "Meditation Retreats", "One-on-One Sessions"],
      testimonial: {
        text: "The practices I learned at Arya Yog Peeth have become essential tools for managing my anxiety. The teachers created a safe space for healing.",
        author: "Devam Rajpoot, India",
        image: "https://placehold.co/120x120/e7a974/ffffff?text=DR"
      }
    },
    {
      id: "culture-enthusiasts",
      title: "Those Interested in Indian Culture",
      description: "Immerse yourself in the rich cultural heritage of India through yoga, philosophy, rituals, and traditional practices in the spiritual capital of Rishikesh.",
      benefits: [
        "Study of ancient texts and philosophy",
        "Sanskrit chanting and mantras",
        "Traditional ceremonies and rituals",
        "Cultural excursions and experiences"
      ],
      programs: ["Cultural Immersion Retreats", "Philosophy Workshops", "Spiritual Pilgrimages"],
      testimonial: {
        text: "My time at Arya Yog Peeth offered a genuine connection to Indian spiritual traditions. The cultural experiences were as enriching as the yoga practice.",
        author: "Elaine, UK",
        image: "https://placehold.co/120x120/e7a974/ffffff?text=Elaine"
      }
    }
  ];

  const activeAudience = audienceData.find(item => item.id === activeTab) || audienceData[0];

  return (
    <section
      id="target-audience"
      ref={sectionRef}
      className="py-20 md:py-32 bg-white"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Who Is It For</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            Is This <span className="text-amber-500">For You?</span>
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Our programs are designed for various needs and goals. Discover which path aligns with your journey.
          </p>
        </div>

        <div
          ref={contentRef}
          className="opacity-0"
        >
          {/* Tabs */}
          <div className="flex flex-wrap justify-center mb-8 gap-2">
            {audienceData.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeTab === item.id
                    ? 'bg-amber-400 text-slate-900'
                    : 'bg-amber-100/50 text-slate-700 hover:bg-amber-200/50'
                }`}
              >
                {item.title.split(' ').slice(0, 3).join(' ')}...
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="bg-amber-50 rounded-2xl p-6 md:p-8 shadow-md">
            <div className="flex flex-col lg:flex-row gap-8">
              <div className="lg:w-2/3">
                <h3 className="text-2xl font-bold text-slate-900 mb-4">{activeAudience.title}</h3>
                <p className="text-slate-700 mb-6">{activeAudience.description}</p>

                <div className="mb-6">
                  <h4 className="font-semibold text-slate-900 mb-3">Benefits:</h4>
                  <ul className="space-y-2">
                    {activeAudience.benefits.map((benefit, idx) => (
                      <li key={idx} className="flex items-start">
                        <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span className="text-slate-700">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-6">
                  <h4 className="font-semibold text-slate-900 mb-3">Recommended Programs:</h4>
                  <div className="flex flex-wrap gap-2">
                    {activeAudience.programs.map((program, idx) => (
                      <span key={idx} className="px-3 py-1 bg-white rounded-full text-sm text-slate-700 border border-amber-200">
                        {program}
                      </span>
                    ))}
                  </div>
                </div>

                <a
                  href="#results-section"
                  className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
                >
                  See the Results
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                  </svg>
                </a>
              </div>

              {activeAudience.testimonial && (
                <div className="lg:w-1/3">
                  <div className="bg-white p-6 rounded-xl shadow-md">
                    <div className="flex items-center mb-4">
                      <img
                        src={activeAudience.testimonial.image}
                        alt={activeAudience.testimonial.author}
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                      <div>
                        <h4 className="font-semibold text-slate-900">{activeAudience.testimonial.author}</h4>
                        <p className="text-xs text-slate-500">Student</p>
                      </div>
                    </div>
                    <p className="text-slate-700 italic mb-4">"{activeAudience.testimonial.text}"</p>
                    <div className="flex text-amber-400">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TargetAudience;