import React, { useRef, useEffect } from 'react';
import { Instagram } from 'lucide-react';

const InstagramContact: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Mock Instagram posts
  const instagramPosts = [
    {
      id: 1,
      image: "/images/c1.webp",
      likes: 124,
      comments: 18
    },
    {
      id: 2,
      image: "/images/c2.avif",
      likes: 98,
      comments: 12
    },
    {
      id: 3,
      image: "/images/c3.avif",
      likes: 156,
      comments: 24
    },
    {
      id: 4,
      image: "/images/c4.avif",
      likes: 87,
      comments: 9
    },
    {
      id: 5,
      image: "/images/card1.avif",
      likes: 112,
      comments: 15
    },
    {
      id: 6,
      image: "/images/card2.avif",
      likes: 143,
      comments: 21
    }
  ];

  return (
    <section
      id="instagram-contact"
      ref={sectionRef}
      className="py-20 md:py-32 bg-amber-50"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Connect With Us</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            Follow Us on <span className="text-amber-500">Instagram</span>
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Stay updated with our latest classes, events, and yoga inspiration.
            Connect with us on Instagram for the fastest response to your questions.
          </p>
        </div>

        <div
          ref={contentRef}
          className="opacity-0"
        >
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-12">
            {instagramPosts.map((post) => (
              <div
                key={post.id}
                className="relative group overflow-hidden rounded-lg aspect-square"
              >
                <img
                  src={post.image}
                  alt={`Instagram post ${post.id}`}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-3">
                  <div className="flex items-center justify-between text-white text-sm">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd"></path>
                      </svg>
                      {post.likes}
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd"></path>
                      </svg>
                      {post.comments}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="flex flex-col md:flex-row gap-8 items-stretch">
            <div className="md:w-1/2 flex">
              <div className="bg-white p-8 rounded-xl shadow-md flex flex-col w-full">
                <div className="flex-1">
                  {/* Instagram Card with Instagram theme */}
                  <div className="bg-gradient-to-br from-purple-600 via-pink-500 to-orange-400 p-6 rounded-xl shadow-lg mb-8">
                    <div className="flex items-center mb-4">
                      <Instagram className="w-8 h-8 text-white mr-3" />
                      <h3 className="text-2xl font-bold text-white">Connect on Instagram</h3>
                    </div>
                    <p className="text-white/90 mb-6">
                      Instagram is our primary platform for communication. Follow us for daily updates,
                      yoga tips, and to connect with our community of practitioners from around the world.
                    </p>
                    <a
                      href="https://instagram.com/aryayogpeeth"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-6 py-3 bg-white text-pink-600 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
                    >
                      <Instagram className="w-5 h-5 mr-2" />
                      Follow @aryayogpeeth
                    </a>
                  </div>

                  <div className="mt-4">
                    <h4 className="font-semibold text-slate-900 mb-4">Alternative Contact Methods:</h4>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span className="text-slate-700"><EMAIL></span>
                      </div>
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span className="text-slate-700">+91 98765 43210</span>
                      </div>
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span className="text-slate-700">Tapovan, Rishikesh, Uttarakhand, India</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="md:w-1/2 flex">
              <div className="bg-white p-8 rounded-xl shadow-md flex flex-col w-full">
                <h3 className="text-2xl font-bold text-slate-900 mb-6">Quick Contact</h3>
                <form className="space-y-4 flex-1 flex flex-col">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-1">Name</label>
                    <input
                      type="text"
                      id="name"
                      className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-amber-500 focus:border-amber-500"
                      placeholder="Your name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                    <input
                      type="email"
                      id="email"
                      className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-amber-500 focus:border-amber-500"
                      placeholder="Your email"
                    />
                  </div>
                  <div className="flex-1">
                    <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-1">Message</label>
                    <textarea
                      id="message"
                      rows={4}
                      className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-amber-500 focus:border-amber-500 h-full min-h-[120px]"
                      placeholder="Your message"
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    className="w-full px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-lg font-medium transition-all duration-300 mt-auto"
                  >
                    Send Message
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InstagramContact;