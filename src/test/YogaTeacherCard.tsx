import React, { useState, useRef, useEffect } from 'react';

interface Teacher {
  id: number;
  name: string;
  title: string;
  image: string;
  bio: string;
  specialties: string[];
  certifications: string[];
  experience: string;
  socialMedia: {
    instagram?: string;
    facebook?: string;
    youtube?: string;
  };
}

const YogaTeacherCard: React.FC = () => {
  const [activeTeacher, setActiveTeacher] = useState<number>(1);
  const [showFullBio, setShowFullBio] = useState<boolean>(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const teachers: Teacher[] = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      title: "Founder & Lead Instructor",
      image: "https://placehold.co/600x800/e2e8f0/1e293b?text=Yogi+Arya",
      bio: "<PERSON><PERSON> began his spiritual journey at the age of 12 under the guidance of his guru in the Himalayan foothills. With over 20 years of teaching experience, he has trained thousands of students from around the world in traditional Hatha, Ashtanga, and Kundalini yoga. His teaching philosophy emphasizes the integration of physical practice with spiritual growth, helping students discover their inner potential through authentic yogic techniques. <PERSON><PERSON>'s classes are known for their perfect balance of challenge and accessibility, making the ancient wisdom of yoga relevant to modern practitioners. Beyond his teaching, he continues his own sadhana (practice) daily, constantly deepening his understanding of yoga's transformative power.",
      specialties: ["Hatha Yoga", "Ashtanga Yoga", "Yoga Philosophy", "Meditation"],
      certifications: ["E-RYT 500", "Yoga Alliance Certified", "Traditional Gurukul Training"],
      experience: "20+ years",
      socialMedia: {
        instagram: "https://instagram.com/yogiarya",
        facebook: "https://facebook.com/yogiarya",
        youtube: "https://youtube.com/yogiarya"
      }
    },
    {
      id: 2,
      name: "Priya Sharma",
      title: "Senior Yoga Instructor",
      image: "https://placehold.co/600x800/e2e8f0/1e293b?text=Priya+Sharma",
      bio: "Priya discovered yoga during her college years as a way to manage stress and anxiety. What began as a physical practice soon transformed into a spiritual journey that led her to Rishikesh to deepen her knowledge. After completing her 500-hour teacher training, she joined Shanti Yog Peeth where she has been teaching for the past 8 years. Priya specializes in alignment-based Hatha yoga and restorative practices, with a particular focus on making yoga accessible to practitioners of all levels. Her gentle yet precise teaching style creates a safe space for students to explore their practice. She is also a certified Ayurvedic practitioner and often incorporates Ayurvedic principles into her yoga classes for a holistic approach to wellness.",
      specialties: ["Hatha Yoga", "Restorative Yoga", "Ayurveda", "Pranayama"],
      certifications: ["RYT 500", "Ayurvedic Practitioner", "Yin Yoga Certified"],
      experience: "12 years",
      socialMedia: {
        instagram: "https://instagram.com/priyasharma",
        facebook: "https://facebook.com/priyasharma"
      }
    },
    {
      id: 3,
      name: "Raj Patel",
      title: "Meditation & Philosophy Teacher",
      image: "https://placehold.co/600x800/e2e8f0/1e293b?text=Raj+Patel",
      bio: "Raj's journey into yoga began after a successful but stressful career in the corporate world. Seeking meaning beyond material success, he spent several years studying with various masters in ashrams across India. His background in both Eastern philosophy and Western psychology gives him a unique perspective on the mind-body connection. At Shanti Yog Peeth, Raj leads meditation sessions and philosophy classes, helping students understand the deeper aspects of yoga beyond the physical postures. His teachings draw from classical texts like the Yoga Sutras, Bhagavad Gita, and Upanishads, but are presented in a way that is relevant to contemporary life. Students appreciate his ability to bridge ancient wisdom with modern challenges, making complex philosophical concepts accessible and practical.",
      specialties: ["Meditation", "Yoga Philosophy", "Mantra Chanting", "Yoga Nidra"],
      certifications: ["MA in Eastern Philosophy", "Meditation Teacher Training", "Vedanta Studies"],
      experience: "15 years",
      socialMedia: {
        youtube: "https://youtube.com/rajpatel",
        facebook: "https://facebook.com/rajpatel"
      }
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const currentTeacher = teachers.find(teacher => teacher.id === activeTeacher) || teachers[0];
  
  const truncateBio = (bio: string) => {
    if (bio.length <= 250 || showFullBio) return bio;
    return bio.substring(0, 250) + '...';
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Meet Our <span className="text-amber-500 dark:text-amber-400">Teachers</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Our experienced instructors bring authentic yogic wisdom and personalized guidance to your practice.
            </p>
          </div>

          {/* Teacher selection tabs */}
          <div className="flex justify-center mb-8 space-x-2 overflow-x-auto pb-2">
            {teachers.map(teacher => (
              <button
                key={teacher.id}
                onClick={() => {
                  setActiveTeacher(teacher.id);
                  setShowFullBio(false);
                }}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 whitespace-nowrap ${
                  activeTeacher === teacher.id
                    ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                    : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
              >
                {teacher.name}
              </button>
            ))}
          </div>

          {/* Teacher profile card */}
          <div className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Teacher image */}
              <div className="md:col-span-1">
                <div className="h-full relative">
                  <div className="absolute inset-0 bg-gradient-to-t from-amber-900/70 to-transparent z-10"></div>
                  <img 
                    src={currentTeacher.image} 
                    alt={currentTeacher.name} 
                    className="w-full h-full object-cover object-center"
                  />
                  <div className="absolute bottom-0 left-0 right-0 p-4 z-20">
                    <div className="flex space-x-3">
                      {currentTeacher.socialMedia.instagram && (
                        <a 
                          href={currentTeacher.socialMedia.instagram} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="w-8 h-8 rounded-full bg-white/20 hover:bg-white/40 flex items-center justify-center transition-colors"
                        >
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                          </svg>
                        </a>
                      )}
                      {currentTeacher.socialMedia.facebook && (
                        <a 
                          href={currentTeacher.socialMedia.facebook} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="w-8 h-8 rounded-full bg-white/20 hover:bg-white/40 flex items-center justify-center transition-colors"
                        >
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                          </svg>
                        </a>
                      )}
                      {currentTeacher.socialMedia.youtube && (
                        <a 
                          href={currentTeacher.socialMedia.youtube} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="w-8 h-8 rounded-full bg-white/20 hover:bg-white/40 flex items-center justify-center transition-colors"
                        >
                          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                          </svg>
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Teacher info */}
              <div className="md:col-span-2 p-6">
                <div className="mb-4">
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white">{currentTeacher.name}</h3>
                  <p className="text-amber-500 dark:text-amber-400">{currentTeacher.title}</p>
                </div>
                
                <div className="mb-6">
                  <p className="text-slate-700 dark:text-slate-300 leading-relaxed">
                    {truncateBio(currentTeacher.bio)}
                    {currentTeacher.bio.length > 250 && (
                      <button 
                        onClick={() => setShowFullBio(!showFullBio)}
                        className="ml-2 text-amber-500 dark:text-amber-400 font-medium hover:underline"
                      >
                        {showFullBio ? 'Read less' : 'Read more'}
                      </button>
                    )}
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Specialties</h4>
                    <div className="flex flex-wrap gap-2">
                      {currentTeacher.specialties.map((specialty, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 rounded-full text-sm"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Certifications</h4>
                    <ul className="space-y-1 text-slate-700 dark:text-slate-300">
                      {currentTeacher.certifications.map((certification, index) => (
                        <li key={index} className="flex items-center">
                          <svg className="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          {certification}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <div className="mt-6 pt-6 border-t border-slate-200 dark:border-slate-600">
                  <div className="flex items-center">
                    <div className="flex items-center mr-6">
                      <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span className="text-slate-700 dark:text-slate-300">{currentTeacher.experience} Experience</span>
                    </div>
                    <a 
                      href="#" 
                      className="text-amber-500 dark:text-amber-400 hover:underline flex items-center"
                    >
                      <span>View Classes</span>
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YogaTeacherCard;
