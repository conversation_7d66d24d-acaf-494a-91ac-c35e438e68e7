import React, { useState, useEffect, useRef } from 'react';

interface CountdownTimerProps {
  targetDate?: string; // ISO date string format
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ 
  targetDate = '2025-06-03T00:00:00' // Default to New Year's Eve
}) => {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({ days: 0, hours: 0, minutes: 0, seconds: 0 });
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const timerRef = useRef<number | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Sample retreat data
  const retreatInfo = {
    title: "200-Hour Yoga Teacher Training",
    location: "<PERSON><PERSON>, Rishikesh",
    price: "$1,299",
    duration: "28 days",
    spots: 5
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = new Date(targetDate).getTime() - new Date().getTime();
      
      if (difference <= 0) {
        setIsExpired(true);
        if (timerRef.current) {
          window.clearInterval(timerRef.current);
          timerRef.current = null;
        }
        return { days: 0, hours: 0, minutes: 0, seconds: 0 };
      }
      
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60)
      };
    };

    // Initial calculation
    setTimeLeft(calculateTimeLeft());
    
    // Update every second
    timerRef.current = window.setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);
    
    // Cleanup interval on unmount
    return () => {
      if (timerRef.current) {
        window.clearInterval(timerRef.current);
      }
    };
  }, [targetDate]);

  // Format number to always have two digits
  const formatNumber = (num: number): string => {
    return num < 10 ? `0${num}` : num.toString();
  };

  return (
    <div ref={sectionRef} className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-slate-800 dark:to-slate-900 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left column: Retreat info */}
            <div>
              <div className="mb-6">
                <span className="inline-block px-3 py-1 bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white text-sm font-medium rounded-full mb-4">
                  Upcoming Retreat
                </span>
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
                  {retreatInfo.title}
                </h2>
                <p className="text-slate-700 dark:text-slate-300 text-lg">
                  Join us for a transformative journey into the heart of yoga at our serene ashram in Rishikesh, the yoga capital of the world.
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
                  <div className="text-amber-500 dark:text-amber-400 text-sm font-medium mb-1">Location</div>
                  <div className="text-slate-900 dark:text-white font-medium">{retreatInfo.location}</div>
                </div>
                <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
                  <div className="text-amber-500 dark:text-amber-400 text-sm font-medium mb-1">Price</div>
                  <div className="text-slate-900 dark:text-white font-medium">{retreatInfo.price}</div>
                </div>
                <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
                  <div className="text-amber-500 dark:text-amber-400 text-sm font-medium mb-1">Duration</div>
                  <div className="text-slate-900 dark:text-white font-medium">{retreatInfo.duration}</div>
                </div>
                <div className="bg-white dark:bg-slate-700 rounded-lg p-4 shadow-sm">
                  <div className="text-amber-500 dark:text-amber-400 text-sm font-medium mb-1">Spots Left</div>
                  <div className="text-slate-900 dark:text-white font-medium">Only {retreatInfo.spots} remaining</div>
                </div>
              </div>
              
              <a
                href="#"
                className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300 transform hover:scale-105"
              >
                Reserve Your Spot
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </a>
            </div>
            
            {/* Right column: Countdown timer */}
            <div className="bg-white dark:bg-slate-700 rounded-xl p-6 shadow-lg">
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
                  {isExpired ? 'Retreat Has Started!' : 'Registration Closes In:'}
                </h3>
                <p className="text-slate-600 dark:text-slate-400">
                  {isExpired 
                    ? 'Contact us to check for last-minute availability'
                    : 'Secure your spot before registration closes'}
                </p>
              </div>
              
              {/* Countdown display */}
              <div className="grid grid-cols-4 gap-4 mb-8">
                <div className="text-center">
                  <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-3 md:p-4 shadow-inner">
                    <div className="text-3xl md:text-4xl font-bold text-amber-500 dark:text-amber-400">
                      {formatNumber(timeLeft.days)}
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-400 text-sm mt-2">Days</div>
                </div>
                <div className="text-center">
                  <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-3 md:p-4 shadow-inner">
                    <div className="text-3xl md:text-4xl font-bold text-amber-500 dark:text-amber-400">
                      {formatNumber(timeLeft.hours)}
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-400 text-sm mt-2">Hours</div>
                </div>
                <div className="text-center">
                  <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-3 md:p-4 shadow-inner">
                    <div className="text-3xl md:text-4xl font-bold text-amber-500 dark:text-amber-400">
                      {formatNumber(timeLeft.minutes)}
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-400 text-sm mt-2">Minutes</div>
                </div>
                <div className="text-center">
                  <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-3 md:p-4 shadow-inner">
                    <div className="text-3xl md:text-4xl font-bold text-amber-500 dark:text-amber-400">
                      {formatNumber(timeLeft.seconds)}
                    </div>
                  </div>
                  <div className="text-slate-600 dark:text-slate-400 text-sm mt-2">Seconds</div>
                </div>
              </div>
              
              {/* Progress bar */}
              <div className="mb-6">
                <div className="flex justify-between text-sm text-slate-600 dark:text-slate-400 mb-2">
                  <span>Registration Progress</span>
                  <span>{Math.max(0, 20 - retreatInfo.spots)}/20 Spots Filled</span>
                </div>
                <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2.5">
                  <div 
                    className="bg-amber-400 dark:bg-amber-500 h-2.5 rounded-full"
                    style={{ width: `${((20 - retreatInfo.spots) / 20) * 100}%` }}
                  ></div>
                </div>
              </div>
              
              {/* Notification form */}
              <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-4">
                <div className="text-sm font-medium text-slate-900 dark:text-white mb-3">
                  Get notified about future retreats:
                </div>
                <div className="flex">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="flex-1 px-4 py-2 rounded-l-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                  />
                  <button className="px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-r-lg font-medium transition-colors">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CountdownTimer;
