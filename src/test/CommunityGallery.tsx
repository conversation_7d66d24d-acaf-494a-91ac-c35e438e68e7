import React, { useState, useRef, useEffect } from 'react';

interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  category: string;
  location?: string;
  date?: string;
  description?: string;
  featured?: boolean;
}

const CommunityGallery: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [showLightbox, setShowLightbox] = useState<boolean>(false);
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const lightboxRef = useRef<HTMLDivElement>(null);

  // Categories for filtering
  const categories = [
    { id: 'all', name: 'All Photos' },
    { id: 'classes', name: 'Classes' },
    { id: 'events', name: 'Events' },
    { id: 'retreats', name: 'Retreats' },
    { id: 'teacher-training', name: 'Teacher Training' },
    { id: 'community', name: 'Community' },
  ];

  // Sample gallery images
  const galleryImages: GalleryImage[] = [
    {
      id: '1',
      src: 'https://placehold.co/600x800/e2e8f0/1e293b?text=Yoga+Class',
      alt: 'Morning yoga class in the main studio',
      category: 'classes',
      location: 'Main Studio',
      date: 'April 2025',
      description: 'Students practicing sun salutations during our popular morning vinyasa flow class.',
      featured: true
    },
    {
      id: '2',
      src: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Meditation+Retreat',
      alt: 'Group meditation during Himalayan retreat',
      category: 'retreats',
      location: 'Rishikesh, India',
      date: 'March 2025',
      description: 'Our annual meditation retreat in the foothills of the Himalayas, a transformative experience for all participants.'
    },
    {
      id: '3',
      src: 'https://placehold.co/600x600/e2e8f0/1e293b?text=Teacher+Training',
      alt: 'Teacher training anatomy session',
      category: 'teacher-training',
      location: 'Yoga Anatomy Lab',
      date: 'February 2025',
      description: 'Students in our 200-hour teacher training program learning about functional anatomy and alignment principles.'
    },
    {
      id: '4',
      src: 'https://placehold.co/800x1000/e2e8f0/1e293b?text=Community+Event',
      alt: 'Community kirtan gathering',
      category: 'events',
      location: 'Community Hall',
      date: 'January 2025',
      description: 'Monthly kirtan and chanting circle bringing together our yoga community for an evening of music and connection.'
    },
    {
      id: '5',
      src: 'https://placehold.co/1000x800/e2e8f0/1e293b?text=Beach+Yoga',
      alt: 'Beach yoga during summer retreat',
      category: 'retreats',
      location: 'Goa, India',
      date: 'December 2024',
      description: 'Sunrise yoga practice on the beach during our winter escape retreat to Goa.',
      featured: true
    },
    {
      id: '6',
      src: 'https://placehold.co/600x600/e2e8f0/1e293b?text=Workshop',
      alt: 'Handstand workshop',
      category: 'events',
      location: 'Main Studio',
      date: 'November 2024',
      description: 'Special inversions workshop focusing on handstand progressions for all levels.'
    },
    {
      id: '7',
      src: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Graduation',
      alt: 'Teacher training graduation ceremony',
      category: 'teacher-training',
      location: 'Yoga Garden',
      date: 'October 2024',
      description: 'Graduation ceremony for our fall 2024 teacher training cohort. Congratulations to all our new teachers!'
    },
    {
      id: '8',
      src: 'https://placehold.co/600x800/e2e8f0/1e293b?text=Partner+Yoga',
      alt: 'Partner yoga class',
      category: 'classes',
      location: 'Studio 2',
      date: 'September 2024',
      description: 'Monthly partner yoga class exploring trust, communication, and connection through shared poses.'
    },
    {
      id: '9',
      src: 'https://placehold.co/800x800/e2e8f0/1e293b?text=Community+Potluck',
      alt: 'Community potluck dinner',
      category: 'community',
      location: 'Yoga Garden',
      date: 'August 2024',
      description: 'Summer potluck gathering bringing together students and teachers for delicious vegetarian food and conversation.'
    },
    {
      id: '10',
      src: 'https://placehold.co/1000x600/e2e8f0/1e293b?text=Outdoor+Class',
      alt: 'Outdoor yoga class in the park',
      category: 'classes',
      location: 'City Park',
      date: 'July 2024',
      description: 'Our popular summer series of outdoor yoga classes, connecting with nature while practicing.',
      featured: true
    },
    {
      id: '11',
      src: 'https://placehold.co/600x600/e2e8f0/1e293b?text=Ayurveda+Workshop',
      alt: 'Ayurveda cooking workshop',
      category: 'events',
      location: 'Community Kitchen',
      date: 'June 2024',
      description: 'Hands-on Ayurvedic cooking workshop teaching principles of food as medicine and seasonal eating.'
    },
    {
      id: '12',
      src: 'https://placehold.co/800x1000/e2e8f0/1e293b?text=Mountain+Retreat',
      alt: 'Mountain yoga retreat',
      category: 'retreats',
      location: 'Himalayan Foothills',
      date: 'May 2024',
      description: 'Spring retreat in the mountains, featuring daily yoga, meditation, and hiking in beautiful natural surroundings.'
    },
    {
      id: '13',
      src: 'https://placehold.co/600x800/e2e8f0/1e293b?text=Volunteer+Day',
      alt: 'Community volunteer day',
      category: 'community',
      location: 'Local Garden Project',
      date: 'April 2024',
      description: 'Our yoga community coming together for a day of service at the local community garden project.'
    },
    {
      id: '14',
      src: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Kids+Yoga',
      alt: 'Kids yoga class',
      category: 'classes',
      location: 'Children\'s Studio',
      date: 'March 2024',
      description: 'Weekly kids yoga class combining movement, storytelling, and mindfulness for ages 5-10.'
    },
    {
      id: '15',
      src: 'https://placehold.co/1000x800/e2e8f0/1e293b?text=Anatomy+Training',
      alt: 'Anatomy session during teacher training',
      category: 'teacher-training',
      location: 'Main Studio',
      date: 'February 2024',
      description: 'In-depth anatomy study during our 300-hour advanced teacher training program.'
    }
  ];

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Close lightbox when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (lightboxRef.current && !lightboxRef.current.contains(event.target as Node)) {
        setShowLightbox(false);
      }
    };

    if (showLightbox) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showLightbox]);

  // Handle escape key to close lightbox
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowLightbox(false);
      }
    };

    if (showLightbox) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [showLightbox]);

  // Filter images based on category
  const filteredImages = galleryImages.filter(image => {
    return activeCategory === 'all' || image.category === activeCategory;
  });

  // Get featured images
  const featuredImages = galleryImages.filter(image => image.featured);

  const openLightbox = (image: GalleryImage) => {
    setSelectedImage(image);
    setShowLightbox(true);
  };

  const closeLightbox = () => {
    setShowLightbox(false);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (!selectedImage) return;
    
    const currentIndex = filteredImages.findIndex(img => img.id === selectedImage.id);
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : filteredImages.length - 1;
    } else {
      newIndex = currentIndex < filteredImages.length - 1 ? currentIndex + 1 : 0;
    }
    
    setSelectedImage(filteredImages[newIndex]);
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Community <span className="text-amber-500 dark:text-amber-400">Gallery</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore moments from our classes, events, retreats, and community gatherings that capture the spirit of our yoga community.
            </p>
          </div>

          {/* Featured images carousel */}
          {featuredImages.length > 0 && (
            <div className="mb-12">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                Featured Moments
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {featuredImages.map((image) => (
                  <div 
                    key={image.id}
                    className="relative overflow-hidden rounded-xl shadow-md cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                    onClick={() => openLightbox(image)}
                  >
                    <img 
                      src={image.src} 
                      alt={image.alt}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-4">
                      <h4 className="text-white font-medium">{image.alt}</h4>
                      <p className="text-slate-200 text-sm">{image.location}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Category filters */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                      : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Masonry gallery */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-auto">
            {filteredImages.map((image) => {
              // Determine the span class based on image dimensions (simulated here)
              const imageId = parseInt(image.id);
              const spanClass = imageId % 3 === 0 
                ? 'sm:col-span-2 row-span-2' 
                : imageId % 5 === 0 
                  ? 'lg:col-span-2' 
                  : '';
              
              return (
                <div 
                  key={image.id}
                  className={`relative overflow-hidden rounded-xl shadow-md cursor-pointer transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg ${spanClass}`}
                  onClick={() => openLightbox(image)}
                >
                  <img 
                    src={image.src} 
                    alt={image.alt}
                    className="w-full h-full object-cover"
                    style={{ 
                      height: spanClass.includes('row-span-2') ? '500px' : '300px'
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 flex flex-col justify-end p-4 transition-opacity duration-300">
                    <h4 className="text-white font-medium">{image.alt}</h4>
                    {image.location && (
                      <p className="text-slate-200 text-sm">{image.location} • {image.date}</p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Empty state */}
          {filteredImages.length === 0 && (
            <div className="text-center py-12 bg-slate-50 dark:bg-slate-700 rounded-xl">
              <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">No images found</h3>
              <p className="text-slate-700 dark:text-slate-300">Try selecting a different category.</p>
            </div>
          )}

          {/* Share your photos CTA */}
          <div className="mt-12 bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8">
            <div className="max-w-3xl mx-auto text-center">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Share Your Yoga Journey
              </h3>
              <p className="text-slate-700 dark:text-slate-300 mb-6">
                Are you part of our community? We'd love to feature your photos in our gallery. Share your yoga moments with us using #ShantiYogCommunity or email them directly.
              </p>
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300">
                Submit Your Photos
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Lightbox */}
      {showLightbox && selectedImage && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div 
            ref={lightboxRef}
            className="max-w-5xl w-full bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-2xl"
          >
            <div className="relative">
              <img 
                src={selectedImage.src} 
                alt={selectedImage.alt}
                className="w-full max-h-[70vh] object-contain"
              />
              
              {/* Navigation buttons */}
              <button 
                onClick={() => navigateImage('prev')}
                className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 text-white flex items-center justify-center hover:bg-black/70 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>
              <button 
                onClick={() => navigateImage('next')}
                className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 rounded-full bg-black/50 text-white flex items-center justify-center hover:bg-black/70 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>
              
              {/* Close button */}
              <button 
                onClick={closeLightbox}
                className="absolute top-4 right-4 w-8 h-8 rounded-full bg-black/50 text-white flex items-center justify-center hover:bg-black/70 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <div className="p-6">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                {selectedImage.alt}
              </h3>
              <div className="flex items-center text-slate-600 dark:text-slate-400 text-sm mb-4">
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  {selectedImage.location}
                </span>
                <span className="mx-2">•</span>
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  {selectedImage.date}
                </span>
                <span className="mx-2">•</span>
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                  </svg>
                  {categories.find(cat => cat.id === selectedImage.category)?.name}
                </span>
              </div>
              <p className="text-slate-700 dark:text-slate-300">
                {selectedImage.description}
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommunityGallery;