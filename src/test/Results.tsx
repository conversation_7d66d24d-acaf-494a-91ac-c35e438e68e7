import React, { useRef, useEffect } from 'react';

interface Result {
  id: number;
  title: string;
  description: string;
  image: string;
  stats: {
    value: string;
    label: string;
  }[];
}

const Results: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (resultsRef.current) resultsRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Capture the current value of the ref
    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const results: Result[] = [
    {
      id: 1,
      title: "Physical Transformation",
      description: "Our students experience significant improvements in strength, flexibility, and overall physical health. Many report relief from chronic pain and improved sleep quality.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Physical",
      stats: [
        { value: "94%", label: "Report improved flexibility" },
        { value: "89%", label: "Experience better sleep" },
        { value: "78%", label: "Report reduced pain" }
      ]
    },
    {
      id: 2,
      title: "Mental Clarity",
      description: "Students develop enhanced focus, reduced anxiety, and greater emotional stability. Our meditation practices help cultivate a calm and clear mind.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Mental",
      stats: [
        { value: "92%", label: "Report reduced stress" },
        { value: "85%", label: "Experience improved focus" },
        { value: "81%", label: "Feel more emotionally balanced" }
      ]
    },
    {
      id: 3,
      title: "Spiritual Growth",
      description: "Many students describe a deeper connection to themselves and the world around them, with a greater sense of purpose and meaning in their lives.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Spiritual",
      stats: [
        { value: "87%", label: "Report deeper self-awareness" },
        { value: "79%", label: "Feel more connected to others" },
        { value: "83%", label: "Experience greater purpose" }
      ]
    },
    {
      id: 4,
      title: "Career Development",
      description: "Our teacher training graduates successfully establish yoga careers worldwide, with many opening their own studios or developing specialized teaching programs.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Career",
      stats: [
        { value: "76%", label: "Teaching within 3 months" },
        { value: "42%", label: "Open their own studio" },
        { value: "93%", label: "Feel confident teaching" }
      ]
    }
  ];

  return (
    <section 
      id="results" 
      ref={sectionRef}
      className="py-20 md:py-32 bg-amber-50/50"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Transformative Results</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            What <span className="text-amber-500">Results</span> Will You Get?
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Our students experience profound transformations that extend far beyond the yoga mat.
            Here's what you can expect from your journey with us.
          </p>
        </div>

        <div 
          ref={resultsRef}
          className="grid grid-cols-1 md:grid-cols-2 gap-8 opacity-0 transition-all duration-1000"
        >
          {results.map((result) => (
            <div key={result.id} className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col h-full">
              <div className="relative h-48 overflow-hidden">
                <img
                  src={result.image}
                  alt={result.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
                  <h3 className="text-white text-2xl font-bold p-6">{result.title}</h3>
                </div>
              </div>
              <div className="p-6 flex-1 flex flex-col">
                <p className="text-slate-700 mb-6">{result.description}</p>
                <div className="mt-auto">
                  <div className="grid grid-cols-3 gap-4">
                    {result.stats.map((stat, index) => (
                      <div key={index} className="text-center">
                        <div className="text-2xl font-bold text-amber-500">{stat.value}</div>
                        <div className="text-xs text-slate-600">{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <a
            href="#faq"
            className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
          >
            Have Questions?
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>

        {/* Testimonial Banner */}
        <div className="mt-20 bg-white rounded-xl p-8 md:p-12 shadow-lg relative overflow-hidden">
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-amber-100 rounded-full opacity-50 transform translate-x-1/3 -translate-y-1/3"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-amber-100 rounded-full opacity-50 transform -translate-x-1/3 translate-y-1/3"></div>
          
          <div className="relative z-10">
            <div className="flex flex-col md:flex-row items-center gap-8">
              <div className="md:w-1/3 flex justify-center">
                <div className="relative">
                  <div className="w-32 h-32 md:w-48 md:h-48 rounded-full overflow-hidden border-4 border-amber-100">
                    <img
                      src="https://placehold.co/400x400/e2e8f0/1e293b?text=Before/After"
                      alt="Before and after transformation"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute -bottom-2 -right-2 bg-amber-400 text-white p-2 rounded-full">
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9.983 3v7.391c0 5.704-3.731 9.57-8.983 10.609l-.995-2.151c2.432-.917 3.995-3.638 3.995-5.849h-4v-10h10zm14.017 0v7.391c0 5.704-3.748 9.571-9 10.609l-.996-2.151c2.433-.917 3.996-3.638 3.996-5.849h-3.983v-10h9.983z"/>
                    </svg>
                  </div>
                </div>
              </div>
              <div className="md:w-2/3">
                <h3 className="text-2xl font-bold text-slate-900 mb-4">Real Transformation Story</h3>
                <blockquote className="text-slate-700 text-lg italic leading-relaxed mb-6">
                  "When I arrived at Arya Yog Peeth, I was struggling with chronic back pain and anxiety that had plagued me for years. After completing the 200-hour teacher training, not only was I free from pain, but I had gained the confidence and skills to start my own yoga studio back home. The physical, mental, and professional transformation has been nothing short of miraculous."
                </blockquote>
                <div className="flex items-center">
                  <div>
                    <p className="font-bold text-slate-900">Michael Chen</p>
                    <p className="text-sm text-slate-500">From Toronto, Canada</p>
                  </div>
                  <div className="ml-auto flex">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className="w-5 h-5 text-amber-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Results;
