import React, { useState, useRef, useEffect } from 'react';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: {
    name: string;
    image: string;
    role: string;
  };
  date: string;
  readTime: number;
  image: string;
  categories: string[];
  featured: boolean;
}

const BlogPreview: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Blog categories
  const categories = [
    { id: 'all', name: 'All Posts' },
    { id: 'yoga-practice', name: 'Yoga Practice' },
    { id: 'meditation', name: 'Meditation' },
    { id: 'ayurveda', name: 'Ayurveda' },
    { id: 'philosophy', name: 'Philosophy' },
    { id: 'lifestyle', name: 'Lifestyle' },
  ];

  // Sample blog posts data
  const blogPosts: BlogPost[] = [
    {
      id: '1',
      title: 'The Science Behind Yoga: How Regular Practice Transforms Your Body and Mind',
      excerpt: "Discover the scientific evidence supporting yoga's numerous health benefits, from improved flexibility to reduced stress and anxiety.",
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Dr. Anjali Sharma',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=AS',
        role: 'Yoga Therapist'
      },
      date: 'May 15, 2025',
      readTime: 8,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Yoga+Science',
      categories: ['yoga-practice', 'philosophy'],
      featured: true
    },
    {
      id: '2',
      title: 'Ayurvedic Morning Rituals for Balance and Energy',
      excerpt: 'Learn how to incorporate simple Ayurvedic practices into your morning routine to start your day with clarity and vitality.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Vikram Patel',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=VP',
        role: 'Ayurvedic Practitioner'
      },
      date: 'May 10, 2025',
      readTime: 6,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Ayurvedic+Rituals',
      categories: ['ayurveda', 'lifestyle'],
      featured: true
    },
    {
      id: '3',
      title: 'Understanding the Eight Limbs of Yoga',
      excerpt: "Explore Patanjali's Yoga Sutras and the eight-fold path that forms the philosophical foundation of yoga practice.",
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Swami Yogananda',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=SY',
        role: 'Yoga Philosophy Teacher'
      },
      date: 'May 5, 2025',
      readTime: 10,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Eight+Limbs',
      categories: ['philosophy'],
      featured: false
    },
    {
      id: '4',
      title: 'Meditation Techniques for Beginners: Start Where You Are',
      excerpt: 'Simple, accessible meditation practices that anyone can incorporate into their daily life, regardless of experience level.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Sarah Johnson',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=SJ',
        role: 'Meditation Instructor'
      },
      date: 'April 28, 2025',
      readTime: 5,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Meditation+Beginners',
      categories: ['meditation'],
      featured: false
    },
    {
      id: '5',
      title: 'Yoga for Stress Relief: Poses to Calm Your Nervous System',
      excerpt: 'A sequence of restorative yoga poses specifically designed to activate the parasympathetic nervous system and reduce stress.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Maya Patel',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=MP',
        role: 'Yoga Instructor'
      },
      date: 'April 22, 2025',
      readTime: 7,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Stress+Relief+Yoga',
      categories: ['yoga-practice'],
      featured: true
    },
    {
      id: '6',
      title: 'The Role of Diet in Yoga Practice: Eating for Energy and Balance',
      excerpt: 'How your food choices can support or hinder your yoga practice, with guidance on mindful eating based on yogic principles.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Dr. Ravi Kumar',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=RK',
        role: 'Nutritionist & Yoga Teacher'
      },
      date: 'April 15, 2025',
      readTime: 9,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Yoga+Diet',
      categories: ['yoga-practice', 'lifestyle', 'ayurveda'],
      featured: false
    },
    {
      id: '7',
      title: 'Seasonal Living: Ayurvedic Wisdom for Spring',
      excerpt: "Align with nature's rhythms using Ayurvedic principles to adjust your diet, lifestyle, and practices for the spring season.",
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Lakshmi Nair',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=LN',
        role: 'Ayurvedic Consultant'
      },
      date: 'April 8, 2025',
      readTime: 6,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Seasonal+Ayurveda',
      categories: ['ayurveda', 'lifestyle'],
      featured: false
    },
    {
      id: '8',
      title: 'The Power of Mantra Meditation: Using Sound for Transformation',
      excerpt: 'Explore how mantras work, their benefits, and how to incorporate them into your meditation practice for deeper awareness.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
      author: {
        name: 'Arjun Desai',
        image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=AD',
        role: 'Sound Healing Practitioner'
      },
      date: 'April 1, 2025',
      readTime: 7,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Mantra+Meditation',
      categories: ['meditation', 'philosophy'],
      featured: false
    }
  ];

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Filter blog posts based on category and search query
  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = activeCategory === 'all' || post.categories.includes(activeCategory);
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         post.excerpt.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  // Get featured posts
  const featuredPosts = blogPosts.filter(post => post.featured);
  
  // Get recent posts (excluding featured ones)
  const recentPosts = blogPosts
    .filter(post => !post.featured)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 3);

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Yoga <span className="text-amber-500 dark:text-amber-400">Blog</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore our collection of articles on yoga, meditation, Ayurveda, and mindful living.
            </p>
          </div>

          {/* Featured posts section */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6 border-b border-slate-200 dark:border-slate-700 pb-2">
              Featured Articles
            </h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {featuredPosts.map((post, index) => (
                <div 
                  key={post.id}
                  className={`bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col ${
                    index === 0 ? 'lg:col-span-3' : ''
                  }`}
                >
                  <div className={`relative ${index === 0 ? 'h-64 lg:h-80' : 'h-48'}`}>
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-0 left-0 bg-amber-500 text-white px-3 py-1 text-sm font-medium">
                      Featured
                    </div>
                  </div>
                  
                  <div className="p-6 flex flex-col flex-grow">
                    <div className="mb-2 flex items-center">
                      {post.categories.map((categoryId, idx) => (
                        <span 
                          key={idx}
                          className="inline-block text-xs font-medium text-amber-600 dark:text-amber-400 mr-2"
                        >
                          {categories.find(cat => cat.id === categoryId)?.name}
                          {idx < post.categories.length - 1 && " • "}
                        </span>
                      ))}
                    </div>
                    
                    <h4 className={`font-bold text-slate-900 dark:text-white mb-3 ${
                      index === 0 ? 'text-2xl' : 'text-xl'
                    }`}>
                      {post.title}
                    </h4>
                    
                    <p className="text-slate-700 dark:text-slate-300 mb-4 flex-grow">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between mt-auto">
                      <div className="flex items-center">
                        <img 
                          src={post.author.image} 
                          alt={post.author.name} 
                          className="w-8 h-8 rounded-full mr-2"
                        />
                        <span className="text-sm text-slate-600 dark:text-slate-400">
                          {post.author.name}
                        </span>
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {post.readTime} min read
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent posts section */}
          <div className="mb-16">
            <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6 border-b border-slate-200 dark:border-slate-700 pb-2">
              Recent Articles
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {recentPosts.map((post) => (
                <div 
                  key={post.id}
                  className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col h-full transform hover:scale-[1.02]"
                >
                  <div className="relative h-48">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="p-6 flex flex-col flex-grow">
                    <div className="mb-2 flex items-center">
                      {post.categories.slice(0, 2).map((categoryId, idx) => (
                        <span 
                          key={idx}
                          className="inline-block text-xs font-medium text-amber-600 dark:text-amber-400 mr-2"
                        >
                          {categories.find(cat => cat.id === categoryId)?.name}
                          {idx < Math.min(post.categories.length, 2) - 1 && " • "}
                        </span>
                      ))}
                      {post.categories.length > 2 && (
                        <span className="inline-block text-xs font-medium text-slate-500 dark:text-slate-400">
                          +{post.categories.length - 2}
                        </span>
                      )}
                    </div>
                    
                    <h4 className="font-bold text-slate-900 dark:text-white text-lg mb-3">
                      {post.title}
                    </h4>
                    
                    <p className="text-slate-700 dark:text-slate-300 text-sm mb-4 flex-grow">
                      {post.excerpt}
                    </p>
                    
                    <div className="flex items-center justify-between mt-auto pt-4 border-t border-slate-200 dark:border-slate-600">
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        {post.date}
                      </div>
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {post.readTime} min read
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Browse all posts section */}
          <div>
            <div className="flex flex-wrap items-center justify-between mb-6">
              <h3 className="text-2xl font-semibold text-slate-900 dark:text-white pb-2">
                Browse All Articles
              </h3>
              
              <div className="relative w-full md:w-64 mt-4 md:mt-0">
                <input
                  type="text"
                  placeholder="Search articles..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                />
                <svg className="w-5 h-5 text-slate-400 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
            
            {/* Category tabs */}
            <div className="flex flex-wrap gap-2 mb-6">
              {categories.map(category => (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeCategory === category.id
                      ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                      : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
            
            {/* Blog posts grid */}
            {filteredPosts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredPosts.map((post) => (
                  <div 
                    key={post.id}
                    className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col h-full transform hover:scale-[1.02]"
                  >
                    <div className="relative h-48">
                      <img
                        src={post.image}
                        alt={post.title}
                        className="w-full h-full object-cover"
                      />
                      {post.featured && (
                        <div className="absolute top-0 left-0 bg-amber-500 text-white px-3 py-1 text-sm font-medium">
                          Featured
                        </div>
                      )}
                    </div>
                    
                    <div className="p-6 flex flex-col flex-grow">
                      <div className="mb-2 flex items-center">
                        {post.categories.slice(0, 2).map((categoryId, idx) => (
                          <span 
                            key={idx}
                            className="inline-block text-xs font-medium text-amber-600 dark:text-amber-400 mr-2"
                          >
                            {categories.find(cat => cat.id === categoryId)?.name}
                            {idx < Math.min(post.categories.length, 2) - 1 && " • "}
                          </span>
                        ))}
                        {post.categories.length > 2 && (
                          <span className="inline-block text-xs font-medium text-slate-500 dark:text-slate-400">
                            +{post.categories.length - 2}
                          </span>
                        )}
                      </div>
                      
                      <h4 className="font-bold text-slate-900 dark:text-white text-lg mb-3">
                        {post.title}
                      </h4>
                      
                      <p className="text-slate-700 dark:text-slate-300 text-sm mb-4 flex-grow">
                        {post.excerpt}
                      </p>
                      
                      <div className="flex items-center justify-between mt-auto pt-4 border-t border-slate-200 dark:border-slate-600">
                        <div className="flex items-center">
                          <img 
                            src={post.author.image} 
                            alt={post.author.name} 
                            className="w-6 h-6 rounded-full mr-2"
                          />
                          <span className="text-xs text-slate-600 dark:text-slate-400">
                            {post.author.name}
                          </span>
                        </div>
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          {post.readTime} min read
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-slate-50 dark:bg-slate-700 rounded-xl">
                <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">No articles found</h3>
                <p className="text-slate-700 dark:text-slate-300">Try adjusting your filters or search query.</p>
              </div>
            )}
            
            {/* Load more button */}
            {filteredPosts.length > 6 && (
              <div className="mt-10 text-center">
                <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300">
                  Load More Articles
                </button>
              </div>
            )}
          </div>
          
          {/* Newsletter signup */}
          <div className="mt-16 bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8">
            <div className="max-w-3xl mx-auto text-center">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Subscribe to Our Newsletter
              </h3>
              <p className="text-slate-700 dark:text-slate-300 mb-6">
                Get the latest articles, practice tips, and special offers delivered straight to your inbox.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Your email address"
                  className="flex-grow px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                />
                <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300 whitespace-nowrap">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPreview;