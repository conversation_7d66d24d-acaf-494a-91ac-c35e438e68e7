import React, { useState, useRef, useEffect } from 'react';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: string;
}

const FAQAccordion: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const faqItems: FAQItem[] = [
    {
      id: 1,
      question: "Do I need prior yoga experience to join your teacher training?",
      answer: "While some yoga experience is beneficial, our 200-hour YTT is designed to accommodate practitioners of various levels. We recommend having at least 6 months of consistent practice before joining. For those with limited experience, we offer preparatory courses that can help build a foundation before the full training begins.",
      category: "courses"
    },
    {
      id: 2,
      question: "What style of yoga do you teach in your training programs?",
      answer: "Our teacher training programs primarily focus on traditional Hatha Yoga and Ashtanga Vinyasa, with elements of Iyengar for alignment principles. We also introduce students to other styles like Yin, Restorative, and Kundalini yoga to provide a well-rounded education. Our philosophy is rooted in classical yogic traditions while acknowledging modern approaches.",
      category: "courses"
    },
    {
      id: 3,
      question: "Is your school registered with Yoga Alliance?",
      answer: "Yes, Shanti Yog Peeth is a Registered Yoga School (RYS) with Yoga Alliance at both the 200-hour and 300-hour levels. Our graduates are eligible to register with Yoga Alliance as Registered Yoga Teachers (RYT) upon successful completion of the training.",
      category: "certification"
    },
    {
      id: 4,
      question: "What is included in the course fee?",
      answer: "Our course fee includes tuition, accommodation, three vegetarian meals daily, course materials, weekend excursions to local spiritual sites, airport pickup from Dehradun, and a welcome kit with essential yoga props. Additional services like Ayurvedic treatments or private consultations are available at an extra cost.",
      category: "payment"
    },
    {
      id: 5,
      question: "What type of accommodation do you provide?",
      answer: "We offer three types of accommodation: shared rooms (3-4 people), twin-sharing rooms (2 people), and private rooms. All rooms have attached bathrooms, hot water, and basic amenities. Our ashram is located in a peaceful area near the Ganges River, providing a serene environment for your practice.",
      category: "accommodation"
    },
    {
      id: 6,
      question: "What is the daily schedule like during the training?",
      answer: "A typical day begins at 5:30 AM with morning bell, followed by cleansing practices and meditation. Morning asana practice runs from 6:30-8:30 AM, followed by breakfast. Theory classes run from 10 AM-1 PM, covering philosophy, anatomy, and teaching methodology. After lunch and rest, afternoon practice is from 4-6 PM, followed by meditation and dinner. Lights out is at 9:30 PM.",
      category: "courses"
    },
    {
      id: 7,
      question: "Do you offer job placement after graduation?",
      answer: "While we don't offer formal job placement, we do provide career guidance and support. Many of our graduates have found teaching opportunities through our extensive network of yoga studios and retreat centers worldwide. We also offer advanced training and mentorship programs for graduates looking to deepen their teaching skills.",
      category: "certification"
    },
    {
      id: 8,
      question: "Is there Wi-Fi available at the ashram?",
      answer: "Yes, we provide Wi-Fi in common areas, though we encourage limited use to maintain the retreat atmosphere. We suggest using technology mindfully during your stay to fully immerse in the yogic lifestyle and training experience.",
      category: "accommodation"
    },
    {
      id: 9,
      question: "What is your cancellation policy?",
      answer: "Deposits are non-refundable but transferable to another course date within one year. Cancellations made more than 60 days before the course start date receive a 70% refund of the remaining balance. Cancellations 30-60 days prior receive a 50% refund. Cancellations less than 30 days prior are not eligible for refunds. We recommend purchasing travel insurance to cover unexpected circumstances.",
      category: "payment"
    },
    {
      id: 10,
      question: "Do you cater to dietary restrictions?",
      answer: "Yes, we accommodate various dietary needs including vegetarian, vegan, gluten-free, and dairy-free options. Our kitchen uses fresh, local ingredients to prepare nutritious sattvic meals. Please inform us of your dietary requirements when registering so we can make appropriate arrangements.",
      category: "accommodation"
    },
    {
      id: 11,
      question: "How do I prepare for my trip to Rishikesh?",
      answer: "We recommend bringing comfortable yoga clothes, a yoga mat (though we provide mats if needed), a meditation cushion, weather-appropriate attire (check the season), modest clothing for temple visits, basic medications, and any personal items you need. India requires a tourist visa, which you should arrange well in advance. We provide detailed pre-arrival information after registration.",
      category: "travel"
    },
    {
      id: 12,
      question: "Is Rishikesh safe for solo female travelers?",
      answer: "Rishikesh is generally considered one of the safer destinations in India for solo female travelers. The city has a spiritual atmosphere and is alcohol-free. Our school arranges airport pickups and can assist with local transportation. We also connect students before arrival so they can coordinate travel plans if desired.",
      category: "travel"
    }
  ];

  const categories = [
    { id: 'all', name: 'All Questions' },
    { id: 'courses', name: 'Courses & Training' },
    { id: 'certification', name: 'Certification' },
    { id: 'payment', name: 'Fees & Payment' },
    { id: 'accommodation', name: 'Accommodation & Food' },
    { id: 'travel', name: 'Travel & Logistics' }
  ];

  const toggleAccordion = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const filteredFAQs = faqItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = searchQuery === '' || 
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.answer.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Frequently Asked <span className="text-amber-500 dark:text-amber-400">Questions</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Find answers to common questions about our yoga programs, accommodations, and more.
            </p>
          </div>

          {/* Search and filter */}
          <div className="max-w-4xl mx-auto mb-10">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search questions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-3 w-full rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                />
              </div>
              <div className="md:w-64">
                <select
                  value={activeCategory}
                  onChange={(e) => setActiveCategory(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* FAQ Accordion */}
          <div className="max-w-4xl mx-auto">
            {filteredFAQs.length > 0 ? (
              <div className="space-y-4">
                {filteredFAQs.map((faq, index) => (
                  <div 
                    key={faq.id}
                    className={`border rounded-lg overflow-hidden transition-all duration-300 ${
                      activeIndex === index 
                        ? 'border-amber-400 dark:border-amber-500 shadow-md' 
                        : 'border-slate-200 dark:border-slate-700'
                    }`}
                  >
                    <button
                      onClick={() => toggleAccordion(index)}
                      className="flex justify-between items-center w-full px-6 py-4 text-left focus:outline-none"
                    >
                      <span className="font-medium text-slate-900 dark:text-white pr-8">{faq.question}</span>
                      <span className={`transform transition-transform duration-300 ${activeIndex === index ? 'rotate-180' : ''}`}>
                        <svg className="w-5 h-5 text-amber-500 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </span>
                    </button>
                    <div 
                      className={`overflow-hidden transition-all duration-300 ${
                        activeIndex === index ? 'max-h-96' : 'max-h-0'
                      }`}
                    >
                      <div className="px-6 pb-4 text-slate-700 dark:text-slate-300">
                        {faq.answer}
                        <div className="mt-3 pt-3 border-t border-slate-100 dark:border-slate-700">
                          <span className="inline-block px-2 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 text-xs rounded-full">
                            {categories.find(cat => cat.id === faq.category)?.name || faq.category}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10 bg-slate-50 dark:bg-slate-700 rounded-lg">
                <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 className="text-xl font-medium text-slate-700 dark:text-slate-300 mb-2">No questions found</h3>
                <p className="text-slate-500 dark:text-slate-400">
                  Try adjusting your search or filter to find what you're looking for.
                </p>
              </div>
            )}
          </div>

          {/* Still have questions section */}
          <div className="max-w-4xl mx-auto mt-12 bg-amber-50 dark:bg-slate-700 rounded-xl p-6 md:p-8">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="md:w-1/4 flex justify-center">
                <div className="w-24 h-24 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400">
                  <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div className="md:w-3/4 text-center md:text-left">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                  Still have questions?
                </h3>
                <p className="text-slate-700 dark:text-slate-300 mb-4">
                  If you couldn't find the answer to your question, please don't hesitate to reach out to our team.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
                  <a
                    href="#"
                    className="inline-flex items-center justify-center px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Email Us
                  </a>
                  <a
                    href="#"
                    className="inline-flex items-center justify-center px-4 py-2 bg-white hover:bg-slate-100 dark:bg-slate-600 dark:hover:bg-slate-500 text-slate-900 dark:text-white border border-slate-300 dark:border-slate-500 rounded-lg font-medium transition-colors"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                    </svg>
                    Live Chat
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQAccordion;
