import React, { useRef, useEffect } from 'react';
import { SchoolOffering } from './types';

const SchoolOfferings: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const headingRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (headingRef.current) headingRef.current.classList.add('animate-fade-in-up');
            if (cardsRef.current) cardsRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const offerings: SchoolOffering[] = [
    {
      title: "Yoga Teacher Training",
      description: "Comprehensive 200-hour and 300-hour yoga teacher training programs certified by Yoga Alliance. Learn traditional yoga techniques with a modern approach.",
      image: "/images/c1.webp",
      benefits: [
        "Internationally recognized certification",
        "Small class sizes for personalized attention",
        "Traditional Hatha and Ashtanga yoga",
        "Anatomy and physiology modules"
      ]
    },
    {
      title: "Yoga Retreats",
      description: "Immersive yoga retreats in the serene environment of Rishikesh. Perfect for deepening your practice and connecting with like-minded practitioners.",
      image: "/images/c2.avif",
      benefits: [
        "Daily yoga and meditation sessions",
        "Accommodation and healthy meals included",
        "Excursions to sacred sites",
        "Personalized guidance from Yogi Arya"
      ]
    },
    {
      title: "Workshops & Intensives",
      description: "Specialized workshops focusing on specific aspects of yoga practice, from alignment to advanced pranayama techniques.",
      image: "/images/c3.avif",
      benefits: [
        "Focus on specific yoga techniques",
        "Suitable for all levels",
        "Therapeutic applications of yoga",
        "Integration of traditional wisdom"
      ]
    },
    {
      title: "Drop-in Classes",
      description: "Daily yoga classes open to all levels. Experience authentic yoga in a supportive community environment.",
      image: "/images/c4.avif",
      benefits: [
        "Multiple styles available",
        "Morning and evening sessions",
        "Beginner-friendly options",
        "Pay-as-you-go flexibility"
      ]
    }
  ];

  return (
    <section
      id="school-offerings"
      ref={sectionRef}
      className="py-20 md:py-32 bg-amber-50"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div 
          ref={headingRef}
          className="text-center mb-16 opacity-0"
        >
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Our Offerings</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            What <span className="text-amber-500">Arya Yog Peeth</span> Offers
          </h2>
          <p className="text-slate-700 max-w-3xl mx-auto">
            Our school combines traditional yogic wisdom with modern teaching methodologies to provide a transformative experience for all practitioners.
          </p>
        </div>

        <div 
          ref={cardsRef}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 opacity-0"
        >
          {offerings.map((offering, index) => (
            <div 
              key={index}
              className="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1"
            >
              <div className="h-48 overflow-hidden">
                <img 
                  src={offering.image} 
                  alt={offering.title} 
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 mb-3">{offering.title}</h3>
                <p className="text-slate-700 mb-4 text-sm">{offering.description}</p>
                <div className="space-y-2">
                  {offering.benefits.map((benefit, idx) => (
                    <div key={idx} className="flex items-start">
                      <svg className="w-4 h-4 text-amber-500 mr-2 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      <span className="text-sm text-slate-600">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <a
            href="#target-audience"
            className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
          >
            Is This For You?
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default SchoolOfferings;