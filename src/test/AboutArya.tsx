import React, { useRef, useEffect } from 'react';

const AboutArya: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (imageRef.current) imageRef.current.classList.add('animate-fade-in-left');
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-right');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Capture the current value of the ref
    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <section
      id="about-arya"
      ref={sectionRef}
      className="py-20 md:py-32 bg-white"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-20">
          <div
            ref={imageRef}
            className="lg:w-1/2 relative opacity-0"
          >
            <div className="relative z-10 ">
              <div className="relative rounded-xl border-l-8 border-amber-400">
                <img
                  src="https://placehold.co/600x800/e2e8f0/1e293b?text=Yogi+Arya"
                  alt="Yogi Arya in a yoga pose"
                  className="rounded-lg shadow-xl object-cover h-[500px] w-full"
                />
               
              </div>

              <div className="mt-[5px] p-4 bg-white rounded-xl shadow-xl z-20 backdrop-blur-sm bg-white/95 border-l-8 border-amber-400">
                <p className="text-sm md:text-base italic text-slate-700 text-center font-medium">
                  "Yoga is not about touching your toes, it's about what you learn on the way down."
                </p>
                <p className="text-right text-xs mt-2 text-slate-500 font-medium">— Yogi Arya</p>
                
              </div>
            </div>
            <div className="absolute bottom-1/4 -right-8 p-4 bg-amber-50 rounded-lg shadow-lg z-20 transform -rotate-2 hover:rotate-0 transition-transform duration-300">
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 bg-amber-100 rounded-full flex items-center justify-center">
                  <span className="text-amber-500 font-bold">15+</span>
                </div>
                <p className="text-sm font-medium text-slate-700">Years of Teaching Experience</p>
              </div>
            </div>
          </div>

          <div
            ref={contentRef}
            className="lg:w-1/2 opacity-0"
          >
            <div className="flex items-center mb-4">
              <div className="h-px bg-amber-400 w-16 mr-4"></div>
              <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Who Am I</span>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
              Meet <span className="text-amber-500">Yogi Arya</span>, Your Guide to Authentic Yoga
            </h2>

            <p className="text-slate-700 mb-6 leading-relaxed">
              Yogi Arya trained under his guruji, former founder of Patanjali Ayurveda, from age 12. His journey through yoga began as a personal quest for spiritual growth and has evolved into a lifelong mission to share authentic yogic wisdom with practitioners around the world.
            </p>

            <div className="mb-6 bg-amber-50 p-4 rounded-lg border-l-4 border-amber-400">
              <h3 className="font-bold text-slate-900 mb-2">Qualifications & Achievements</h3>
              <ul className="space-y-2 text-slate-700">
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Two master's degrees in Yogic Sciences
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Trained at Bihar School of Yoga and BKS Iyengar Institute at Pune
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Studied under masters of the Swami Satyananda Saraswati lineage
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Over 15 years teaching internationally in India, China, and Hong Kong
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 text-amber-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Trained over 5,000 students from around the world
                </li>
              </ul>
            </div>

            <p className="text-slate-700 mb-8 leading-relaxed">
              Arya has an extensive understanding of the interplay of human physiology and psychology, which he weaves into his classes. His approach is precise and scientific while also creating a fun, compassionate, and joyful community environment. His teaching combines traditional yogic wisdom with modern scientific understanding, making ancient practices accessible to contemporary practitioners.
            </p>

            <div className="grid grid-cols-2 gap-6 mb-8">
              <div className="flex flex-col items-center p-4 bg-amber-100/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 mb-1">15+</span>
                <span className="text-sm text-slate-600 text-center">Years of Experience</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-amber-100/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 mb-1">5000+</span>
                <span className="text-sm text-slate-600 text-center">Students Trained</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-amber-100/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 mb-1">3</span>
                <span className="text-sm text-slate-600 text-center">Countries Taught In</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-amber-100/50 rounded-lg">
                <span className="text-3xl font-bold text-amber-500 mb-1">4</span>
                <span className="text-sm text-slate-600 text-center">Yoga Styles Mastered</span>
              </div>
            </div>

            <a
              href="#about-school"
              className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
            >
              Discover My School
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutArya;
