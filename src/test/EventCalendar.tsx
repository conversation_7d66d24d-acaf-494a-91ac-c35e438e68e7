import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';

interface YogaEvent {
  id: string;
  title: string;
  instructor: string;
  date: string; // ISO date string
  startTime: string; // 24-hour format "HH:MM"
  endTime: string; // 24-hour format "HH:MM"
  type: 'class' | 'workshop' | 'retreat' | 'training';
  level: 'all-levels' | 'beginner' | 'intermediate' | 'advanced';
  location: string;
  description: string;
  price?: string;
  spotsAvailable?: number;
  image?: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  events: YogaEvent[];
}

// Helper function to check if two dates are the same day (defined outside component to avoid initialization issues)
const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

// Helper function to format date to YYYY-MM-DD (defined outside component to avoid initialization issues)
const formatDateToYYYYMMDD = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const EventCalendar: React.FC = () => {
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<YogaEvent | null>(null);
  const [showEventModal, setShowEventModal] = useState<boolean>(false);
  const [activeFilter, setActiveFilter] = useState<string>('all');

  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Sample yoga events data
  const yogaEvents = useMemo<YogaEvent[]>(() => [
    {
      id: 'event-1',
      title: 'Morning Vinyasa Flow',
      instructor: 'Priya Sharma',
      date: '2025-06-15',
      startTime: '07:00',
      endTime: '08:30',
      type: 'class',
      level: 'all-levels',
      location: 'Main Studio',
      description: 'Start your day with an energizing vinyasa flow that will awaken your body and mind. This class focuses on linking breath with movement to create a moving meditation that builds strength, flexibility, and balance.',
      price: '$15',
      spotsAvailable: 12,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Vinyasa+Flow'
    },
    {
      id: 'event-2',
      title: 'Gentle Hatha Yoga',
      instructor: 'Raj Patel',
      date: '2025-06-15',
      startTime: '10:00',
      endTime: '11:15',
      type: 'class',
      level: 'beginner',
      location: 'Garden Studio',
      description: 'A gentle approach to hatha yoga suitable for beginners and those looking for a more relaxed practice. Focus on fundamental poses, proper alignment, and breathing techniques to build a strong foundation for your yoga journey.',
      price: '$12',
      spotsAvailable: 8,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Hatha+Yoga'
    },
    {
      id: 'event-3',
      title: 'Yin Yoga & Meditation',
      instructor: 'Sarah Johnson',
      date: '2025-06-15',
      startTime: '18:30',
      endTime: '20:00',
      type: 'class',
      level: 'all-levels',
      location: 'Meditation Hall',
      description: 'A deeply relaxing practice combining yin yoga and meditation. Yin poses are held for longer periods to target the connective tissues and fascia, followed by a guided meditation to calm the mind and nervous system.',
      price: '$18',
      spotsAvailable: 15,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Yin+Yoga'
    },
    {
      id: 'event-4',
      title: 'Yoga for Back Pain Relief',
      instructor: 'Dr. Amit Gupta',
      date: '2025-06-17',
      startTime: '14:00',
      endTime: '16:00',
      type: 'workshop',
      level: 'all-levels',
      location: 'Therapy Room',
      description: 'Learn therapeutic yoga poses and techniques specifically designed to alleviate back pain and prevent future issues. This workshop combines yoga with physical therapy principles for a holistic approach to back health.',
      price: '$35',
      spotsAvailable: 10,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Back+Pain+Relief'
    },
    {
      id: 'event-5',
      title: 'Ashtanga Primary Series',
      instructor: 'Yogi Arya',
      date: '2025-06-18',
      startTime: '06:30',
      endTime: '08:30',
      type: 'class',
      level: 'intermediate',
      location: 'Main Studio',
      description: 'A traditional Ashtanga yoga class following the primary series sequence. This dynamic and challenging practice builds strength, flexibility, and internal heat through a set sequence of postures linked with breath.',
      price: '$18',
      spotsAvailable: 6,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Ashtanga+Yoga'
    },
    {
      id: 'event-6',
      title: 'Introduction to Meditation',
      instructor: 'Raj Patel',
      date: '2025-06-20',
      startTime: '19:00',
      endTime: '20:30',
      type: 'workshop',
      level: 'beginner',
      location: 'Meditation Hall',
      description: 'Learn the fundamentals of meditation practice in this beginner-friendly workshop. Explore different meditation techniques, proper posture, and how to establish a regular practice for stress reduction and mental clarity.',
      price: '$25',
      spotsAvailable: 20,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Meditation'
    },
    {
      id: 'event-7',
      title: 'Yoga Teacher Training Info Session',
      instructor: 'Yogi Arya',
      date: '2025-06-22',
      startTime: '18:00',
      endTime: '19:30',
      type: 'workshop',
      level: 'all-levels',
      location: 'Community Room',
      description: 'Learn about our upcoming 200-hour Yoga Teacher Training program. Meet the lead instructors, review the curriculum, and get all your questions answered about embarking on this transformative journey.',
      price: 'Free',
      spotsAvailable: 30,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Teacher+Training'
    },
    {
      id: 'event-8',
      title: 'Weekend Yoga Retreat',
      instructor: 'Multiple Teachers',
      date: '2025-06-24',
      startTime: '08:00',
      endTime: '20:00',
      type: 'retreat',
      level: 'all-levels',
      location: 'Riverside Retreat Center',
      description: 'Escape the city for a day of yoga, meditation, and nature. This one-day retreat includes multiple yoga classes, guided meditation, a healthy vegetarian lunch, and time to relax and reflect in beautiful natural surroundings.',
      price: '$120',
      spotsAvailable: 15,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Yoga+Retreat'
    },
    {
      id: 'event-9',
      title: 'Prenatal Yoga',
      instructor: 'Maya Rodriguez',
      date: '2025-06-25',
      startTime: '11:00',
      endTime: '12:30',
      type: 'class',
      level: 'all-levels',
      location: 'Garden Studio',
      description: 'A nurturing yoga practice designed specifically for expectant mothers. This class offers safe and effective poses to relieve common pregnancy discomforts, build strength for labor, and connect with your baby.',
      price: '$20',
      spotsAvailable: 8,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Prenatal+Yoga'
    },
    {
      id: 'event-10',
      title: '200-Hour Yoga Teacher Training',
      instructor: 'Yogi Arya & Team',
      date: '2025-07-01',
      startTime: '08:00',
      endTime: '18:00',
      type: 'training',
      level: 'intermediate',
      location: 'Shanti Yog Peeth',
      description: 'Our comprehensive 200-hour Yoga Teacher Training program begins today! This transformative journey will deepen your practice and prepare you to teach yoga with confidence and authenticity. The program covers asana, philosophy, anatomy, teaching methodology, and more.',
      price: '$2,500',
      spotsAvailable: 5,
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Teacher+Training'
    }
  ], []);

  // Helper function to get events for a specific date
  const getEventsForDate = useCallback((date: Date): YogaEvent[] => {
    const dateString = formatDateToYYYYMMDD(date);
    return yogaEvents.filter(event => event.date === dateString);
  }, [yogaEvents]);

  // Event type filters
  const filters = [
    { id: 'all', name: 'All Events' },
    { id: 'class', name: 'Classes' },
    { id: 'workshop', name: 'Workshops' },
    { id: 'retreat', name: 'Retreats' },
    { id: 'training', name: 'Trainings' }
  ];

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setShowEventModal(false);
      }
    };

    if (showEventModal) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEventModal]);

  // Generate calendar days for the current month
  useEffect(() => {
    const generateCalendarDays = () => {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();

      // First day of the month
      const firstDayOfMonth = new Date(year, month, 1);
      // Last day of the month
      const lastDayOfMonth = new Date(year, month + 1, 0);

      // Day of the week for the first day (0 = Sunday, 6 = Saturday)
      const firstDayOfWeek = firstDayOfMonth.getDay();

      // Total days in the month
      const daysInMonth = lastDayOfMonth.getDate();

      // Calculate days from previous month to show
      const daysFromPrevMonth = firstDayOfWeek;

      // Calculate days from next month to show (to complete the grid)
      const totalDaysToShow = 42; // 6 rows of 7 days
      const daysFromNextMonth = totalDaysToShow - daysInMonth - daysFromPrevMonth;

      const days: CalendarDay[] = [];

      // Add days from previous month
      const prevMonth = new Date(year, month - 1, 1);
      const daysInPrevMonth = new Date(year, month, 0).getDate();

      for (let i = daysInPrevMonth - daysFromPrevMonth + 1; i <= daysInPrevMonth; i++) {
        const date = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), i);
        days.push({
          date,
          isCurrentMonth: false,
          isToday: isSameDay(date, new Date()),
          events: getEventsForDate(date)
        });
      }

      // Add days from current month
      for (let i = 1; i <= daysInMonth; i++) {
        const date = new Date(year, month, i);
        days.push({
          date,
          isCurrentMonth: true,
          isToday: isSameDay(date, new Date()),
          events: getEventsForDate(date)
        });
      }

      // Add days from next month
      const nextMonth = new Date(year, month + 1, 1);

      for (let i = 1; i <= daysFromNextMonth; i++) {
        const date = new Date(nextMonth.getFullYear(), nextMonth.getMonth(), i);
        days.push({
          date,
          isCurrentMonth: false,
          isToday: isSameDay(date, new Date()),
          events: getEventsForDate(date)
        });
      }

      setCalendarDays(days);
    };

    generateCalendarDays();
  }, [currentDate, getEventsForDate]);



  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  // Navigate to today
  const goToToday = () => {
    setCurrentDate(new Date());
    setSelectedDate(new Date());
  };

  // Handle day click
  const handleDayClick = (day: CalendarDay) => {
    setSelectedDate(day.date);
  };

  // Handle event click
  const handleEventClick = (event: YogaEvent, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  // Filter events based on active filter
  const filteredEvents = selectedDate ?
    getEventsForDate(selectedDate).filter(event =>
      activeFilter === 'all' || event.type === activeFilter
    ) : [];

  // Format time from 24-hour to 12-hour format
  const formatTime = (time: string): string => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minutes} ${ampm}`;
  };

  // Format date to display
  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get month and year string
  const getMonthYearString = (): string => {
    return currentDate.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Yoga <span className="text-amber-500 dark:text-amber-400">Event Calendar</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Browse our schedule of yoga classes, workshops, retreats, and teacher trainings.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Calendar */}
            <div className="lg:col-span-2">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md">
                {/* Calendar header */}
                <div className="bg-amber-400 dark:bg-amber-600 p-4 flex items-center justify-between">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white">{getMonthYearString()}</h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={goToPreviousMonth}
                      className="p-2 rounded-full bg-white/20 hover:bg-white/30 text-slate-900 dark:text-white transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                      </svg>
                    </button>
                    <button
                      onClick={goToToday}
                      className="px-3 py-1 rounded-full bg-white/20 hover:bg-white/30 text-slate-900 dark:text-white text-sm font-medium transition-colors"
                    >
                      Today
                    </button>
                    <button
                      onClick={goToNextMonth}
                      className="p-2 rounded-full bg-white/20 hover:bg-white/30 text-slate-900 dark:text-white transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Weekday headers */}
                <div className="grid grid-cols-7 bg-amber-50 dark:bg-amber-900/20">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                    <div key={index} className="p-2 text-center text-slate-700 dark:text-slate-300 font-medium">
                      {day}
                    </div>
                  ))}
                </div>

                {/* Calendar grid */}
                <div className="grid grid-cols-7 auto-rows-fr">
                  {calendarDays.map((day, index) => (
                    <div
                      key={index}
                      onClick={() => handleDayClick(day)}
                      className={`min-h-[80px] p-1 border border-slate-200 dark:border-slate-600 transition-colors ${
                        day.isCurrentMonth
                          ? 'bg-white dark:bg-slate-700'
                          : 'bg-slate-100 dark:bg-slate-800 text-slate-400 dark:text-slate-500'
                      } ${
                        selectedDate && isSameDay(day.date, selectedDate)
                          ? 'ring-2 ring-amber-400 dark:ring-amber-500 z-10'
                          : ''
                      } ${
                        day.isToday
                          ? 'font-bold'
                          : ''
                      } hover:bg-amber-50 dark:hover:bg-slate-600 cursor-pointer`}
                    >
                      <div className="flex justify-between items-start">
                        <span className={`inline-block w-6 h-6 rounded-full text-center leading-6 text-sm ${
                          day.isToday
                            ? 'bg-amber-400 dark:bg-amber-500 text-white'
                            : ''
                        }`}>
                          {day.date.getDate()}
                        </span>
                        {day.events.length > 0 && (
                          <span className="inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                            {day.events.length}
                          </span>
                        )}
                      </div>
                      <div className="mt-1 space-y-1 overflow-hidden max-h-[50px]">
                        {day.events.slice(0, 2).map((event, eventIndex) => (
                          <div
                            key={eventIndex}
                            onClick={(e) => handleEventClick(event, e)}
                            className={`text-xs truncate px-1 py-0.5 rounded ${
                              event.type === 'class'
                                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                                : event.type === 'workshop'
                                  ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                                  : event.type === 'retreat'
                                    ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300'
                                    : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                            }`}
                          >
                            {event.title}
                          </div>
                        ))}
                        {day.events.length > 2 && (
                          <div className="text-xs text-slate-500 dark:text-slate-400 pl-1">
                            +{day.events.length - 2} more
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Event list for selected date */}
            <div className="lg:col-span-1">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md h-full">
                <div className="bg-amber-400 dark:bg-amber-600 p-4">
                  <h3 className="text-lg font-bold text-slate-900 dark:text-white">
                    {selectedDate ? formatDate(selectedDate) : 'Select a date'}
                  </h3>
                </div>

                {/* Event type filters */}
                <div className="p-4 border-b border-slate-200 dark:border-slate-600">
                  <div className="flex flex-wrap gap-2">
                    {filters.map(filter => (
                      <button
                        key={filter.id}
                        onClick={() => setActiveFilter(filter.id)}
                        className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
                          activeFilter === filter.id
                            ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-sm'
                            : 'bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-500'
                        }`}
                      >
                        {filter.name}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Events list */}
                <div className="p-4 overflow-y-auto" style={{ maxHeight: '500px' }}>
                  {selectedDate ? (
                    filteredEvents.length > 0 ? (
                      <div className="space-y-4">
                        {filteredEvents.map(event => (
                          <div
                            key={event.id}
                            onClick={() => {
                              setSelectedEvent(event);
                              setShowEventModal(true);
                            }}
                            className="bg-white dark:bg-slate-800 rounded-lg p-3 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                          >
                            <div className="flex items-start space-x-3">
                              <div className={`flex-shrink-0 w-3 h-full rounded-full ${
                                event.type === 'class'
                                  ? 'bg-green-500 dark:bg-green-400'
                                  : event.type === 'workshop'
                                    ? 'bg-blue-500 dark:bg-blue-400'
                                    : event.type === 'retreat'
                                      ? 'bg-purple-500 dark:bg-purple-400'
                                      : 'bg-red-500 dark:bg-red-400'
                              }`}></div>
                              <div className="flex-1">
                                <h4 className="text-slate-900 dark:text-white font-medium">{event.title}</h4>
                                <p className="text-amber-500 dark:text-amber-400 text-sm">{formatTime(event.startTime)} - {formatTime(event.endTime)}</p>
                                <p className="text-slate-700 dark:text-slate-300 text-sm mt-1">{event.instructor}</p>
                                <div className="flex items-center justify-between mt-2">
                                  <span className="text-slate-600 dark:text-slate-400 text-xs">{event.location}</span>
                                  <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${
                                    event.type === 'class'
                                      ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                                      : event.type === 'workshop'
                                        ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                                        : event.type === 'retreat'
                                          ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300'
                                          : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                                  }`}>
                                    {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <svg className="w-12 h-12 text-slate-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <p className="text-slate-700 dark:text-slate-300">No events scheduled for this day</p>
                        <p className="text-slate-500 dark:text-slate-400 text-sm mt-1">Try selecting a different date or filter</p>
                      </div>
                    )
                  ) : (
                    <div className="text-center py-8">
                      <svg className="w-12 h-12 text-slate-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      <p className="text-slate-700 dark:text-slate-300">Select a date to view events</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Event detail modal */}
      {showEventModal && selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div
            ref={modalRef}
            className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="relative">
              {selectedEvent.image && (
                <div className="h-48 md:h-64 overflow-hidden">
                  <img
                    src={selectedEvent.image}
                    alt={selectedEvent.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>
              )}
              <button
                onClick={() => setShowEventModal(false)}
                className="absolute top-4 right-4 p-2 rounded-full bg-black/20 hover:bg-black/30 text-white transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
              <div className="absolute bottom-4 left-4 right-4">
                <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                  selectedEvent.type === 'class'
                    ? 'bg-green-100 text-green-800'
                    : selectedEvent.type === 'workshop'
                      ? 'bg-blue-100 text-blue-800'
                      : selectedEvent.type === 'retreat'
                        ? 'bg-purple-100 text-purple-800'
                        : 'bg-red-100 text-red-800'
                }`}>
                  {selectedEvent.type.charAt(0).toUpperCase() + selectedEvent.type.slice(1)}
                </span>
                <h3 className="text-2xl font-bold text-white mt-2">{selectedEvent.title}</h3>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Date</p>
                  <p className="text-slate-900 dark:text-white font-medium">
                    {new Date(selectedEvent.date).toLocaleDateString('en-US', {
                      weekday: 'long',
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Time</p>
                  <p className="text-slate-900 dark:text-white font-medium">
                    {formatTime(selectedEvent.startTime)} - {formatTime(selectedEvent.endTime)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Instructor</p>
                  <p className="text-slate-900 dark:text-white font-medium">{selectedEvent.instructor}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Location</p>
                  <p className="text-slate-900 dark:text-white font-medium">{selectedEvent.location}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Level</p>
                  <p className="text-slate-900 dark:text-white font-medium">
                    {selectedEvent.level.split('-').map(word =>
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ')}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-slate-500 dark:text-slate-400">Price</p>
                  <p className="text-slate-900 dark:text-white font-medium">{selectedEvent.price}</p>
                </div>
              </div>

              <div className="mb-6">
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">Description</h4>
                <p className="text-slate-700 dark:text-slate-300">{selectedEvent.description}</p>
              </div>

              {selectedEvent.spotsAvailable !== undefined && (
                <div className="mb-6">
                  <div className="flex justify-between text-sm text-slate-600 dark:text-slate-400 mb-2">
                    <span>Availability</span>
                    <span>{selectedEvent.spotsAvailable} spots remaining</span>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2.5">
                    <div
                      className="bg-amber-400 dark:bg-amber-500 h-2.5 rounded-full"
                      style={{ width: `${Math.min(100, (selectedEvent.spotsAvailable / 20) * 100)}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <div className="flex justify-between">
                <button
                  onClick={() => setShowEventModal(false)}
                  className="px-4 py-2 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-colors"
                >
                  Close
                </button>
                <button
                  className="px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors"
                >
                  Register Now
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EventCalendar;