import React, { useRef, useEffect } from 'react';

const AboutSchool: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-left');
            if (imageRef.current) imageRef.current.classList.add('animate-fade-in-right');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Capture the current value of the ref
    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <section
      id="about-school"
      ref={sectionRef}
      className="py-20 md:py-32 bg-amber-50/50"
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col lg:flex-row-reverse items-center gap-12 lg:gap-20">
          <div
            ref={imageRef}
            className="lg:w-1/2 relative opacity-0"
          >
            <div className="relative z-10 grid grid-cols-2 gap-4">
              <div className="relative col-span-2">
                <img
                  src="https://placehold.co/800x800/e2e8f0/1e293b?text=Ashram"
                  alt="Arya Yog Peeth Ashram"
                  className="rounded-lg shadow-xl object-cover aspect-square w-full"
                />
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-white p-2 px-3 rounded-lg shadow-lg z-20 backdrop-blur-sm bg-white/90">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-slate-900 text-sm">Rishikesh, India</h4>
                      <p className="text-xs text-slate-500">The Yoga Capital of the World</p>
                    </div>
                  </div>
                </div>
              </div>
              <img
                src="https://placehold.co/400x400/e2e8f0/1e293b?text=Yoga+Class"
                alt="Yoga class in session"
                className="rounded-lg shadow-xl object-cover aspect-square w-full"
              />
              <img
                src="https://placehold.co/400x400/e2e8f0/1e293b?text=Meditation"
                alt="Meditation session"
                className="rounded-lg shadow-xl object-cover aspect-square w-full"
              />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-amber-400 rounded-lg -z-10"></div>
            </div>

          </div>

          <div
            ref={contentRef}
            className="lg:w-1/2 opacity-0"
          >
            <div className="flex items-center mb-4">
              <div className="h-px bg-amber-400 w-16 mr-4"></div>
              <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">What I Offer</span>
            </div>

            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
              <span className="text-amber-500">Arya Yog Peeth</span>: A Sanctuary for Authentic Yoga
            </h2>

            <p className="text-slate-700 mb-6 leading-relaxed">
              Arya Yog Peeth is a premier Yoga School based in Rishikesh, India with in-person and online trainings serving an international audience. Our school is dedicated to preserving and sharing authentic yogic traditions while making them accessible to modern practitioners.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Comprehensive Curriculum</h3>
                <p className="text-slate-600">
                  Our programs include Hatha, Vinyasa, and Iyengar Yoga, as well as special programs in Yogic Philosophy, Sound Healing, and deeper esoteric practices like Swara Yoga.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Beautiful Facilities</h3>
                <p className="text-slate-600">
                  Our ashram is located in the beautiful Himalayan foothills of Rishikesh, offering a peaceful environment with comfortable accommodations and delicious vegetarian meals.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Certified Programs</h3>
                <p className="text-slate-600">
                  Our 200 HR Yoga Teacher Training Courses are certified by Yoga Alliance, providing you with internationally recognized qualifications.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-6 h-6 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">Small Class Sizes</h3>
                <p className="text-slate-600">
                  We maintain small class sizes to ensure personalized attention and guidance for each student, creating an intimate and supportive learning environment.
                </p>
              </div>
            </div>

            <p className="text-slate-700 mb-8 leading-relaxed">
              Our courses run monthly, starting from the 3rd and finishing on the 30th. Whether you're looking to deepen your practice, become a certified teacher, or simply experience the transformative power of yoga in its birthplace, Arya Yog Peeth offers a genuine and comprehensive approach to yoga.
            </p>

            <a
              href="#who-is-it-for"
              className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
            >
              Is This For You?
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSchool;
