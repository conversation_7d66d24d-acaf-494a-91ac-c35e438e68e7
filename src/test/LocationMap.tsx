import React, { useRef, useEffect } from 'react';

interface Landmark {
  name: string;
  description: string;
  distance: string;
  icon: string;
}

const LocationMap: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const landmarks: Landmark[] = [
    {
      name: "<PERSON><PERSON>man J<PERSON>",
      description: "Famous suspension bridge across the Ganges River",
      distance: "10 min walk",
      icon: "🌉"
    },
    {
      name: "Parmarth Niketan",
      description: "Spiritual institution known for evening Ganga Aarti",
      distance: "15 min drive",
      icon: "🕯️"
    },
    {
      name: "Triveni Ghat",
      description: "Sacred bathing spot at the confluence of three rivers",
      distance: "20 min drive",
      icon: "🌊"
    },
    {
      name: "Beatles Ashram",
      description: "Historic ashram where The Beatles stayed in 1968",
      distance: "25 min drive",
      icon: "🎵"
    },
    {
      name: "Neelkanth Mahadev Temple",
      description: "Ancient temple dedicated to Lord Shiva",
      distance: "45 min drive",
      icon: "🛕"
    },
    {
      name: "Rajaji National Park",
      description: "Wildlife sanctuary with elephants and tigers",
      distance: "60 min drive",
      icon: "🐘"
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Our <span className="text-amber-500 dark:text-amber-400">Location</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Nestled in the spiritual heart of Rishikesh, our school offers the perfect environment for your yoga journey.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
            {/* Map and address column */}
            <div className="lg:col-span-3">
              <div className="rounded-xl overflow-hidden shadow-lg h-full flex flex-col">
                {/* Embedded map */}
                <div className="relative w-full h-0 pb-[75%] lg:pb-[60%]">
                  <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3472.0775782964197!2d78.32296491549701!3d30.12118152044761!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39091630a1d5137f%3A0x98f1b2d2a8704195!2sRishikesh%2C%20Uttarakhand%2C%20India!5e0!3m2!1sen!2sus!4v1651234567890!5m2!1sen!2sus" 
                    className="absolute top-0 left-0 w-full h-full border-0"
                    allowFullScreen
                    loading="lazy"
                    title="Shanti Yog Peeth location map"
                  ></iframe>
                </div>
                
                {/* Address and contact info */}
                <div className="p-6 bg-amber-50 dark:bg-slate-700 flex-grow">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400 mr-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">Shanti Yog Peeth</h3>
                      <p className="text-slate-700 dark:text-slate-300 mb-4">
                        123 Yoga Street, Near Laxman Jhula<br />
                        Rishikesh, Uttarakhand 249302<br />
                        India
                      </p>
                      <div className="flex items-center text-amber-500 dark:text-amber-400 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span className="text-slate-700 dark:text-slate-300">+91 98765 43210</span>
                      </div>
                      <div className="flex items-center text-amber-500 dark:text-amber-400">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <span className="text-slate-700 dark:text-slate-300"><EMAIL></span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Nearby landmarks column */}
            <div className="lg:col-span-2">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 h-full">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                  Nearby Landmarks
                </h3>
                <div className="space-y-4">
                  {landmarks.map((landmark, index) => (
                    <div 
                      key={index}
                      className="bg-white dark:bg-slate-600 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center text-2xl mr-3">
                          {landmark.icon}
                        </div>
                        <div>
                          <h4 className="font-medium text-slate-900 dark:text-white">{landmark.name}</h4>
                          <p className="text-slate-600 dark:text-slate-300 text-sm mb-1">{landmark.description}</p>
                          <div className="flex items-center text-amber-500 dark:text-amber-400 text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {landmark.distance}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Transportation section */}
          <div className="mt-10 bg-amber-50 dark:bg-slate-700 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
              Getting Here
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-slate-600 rounded-lg p-4 shadow-sm">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 dark:text-white">By Air</h4>
                </div>
                <p className="text-slate-600 dark:text-slate-300 text-sm">
                  The nearest airport is Dehradun (DED), about 35 km away. We offer airport pickup service for all our students.
                </p>
              </div>
              
              <div className="bg-white dark:bg-slate-600 rounded-lg p-4 shadow-sm">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 dark:text-white">By Train</h4>
                </div>
                <p className="text-slate-600 dark:text-slate-300 text-sm">
                  Rishikesh has its own railway station. Alternatively, Haridwar station (25 km away) has better connectivity to major cities.
                </p>
              </div>
              
              <div className="bg-white dark:bg-slate-600 rounded-lg p-4 shadow-sm">
                <div className="flex items-center mb-3">
                  <div className="w-10 h-10 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h4 className="font-medium text-slate-900 dark:text-white">By Bus</h4>
                </div>
                <p className="text-slate-600 dark:text-slate-300 text-sm">
                  Regular buses connect Rishikesh to Delhi, Haridwar, Dehradun and other major cities in North India.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationMap;
