import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';

interface BreathingPattern {
  id: string;
  name: string;
  description: string;
  inhaleTime: number; // in seconds
  holdInTime: number; // in seconds
  exhaleTime: number; // in seconds
  holdOutTime: number; // in seconds
  cycles: number;
  benefits: string[];
  instructions: string;
  level: 'beginner' | 'intermediate' | 'advanced';
}

const BreathingExercise: React.FC = () => {
  const [selectedPattern, setSelectedPattern] = useState<string>('box-breathing');
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [currentPhase, setCurrentPhase] = useState<'inhale' | 'hold-in' | 'exhale' | 'hold-out'>('inhale');
  const [currentCycle, setCurrentCycle] = useState<number>(1);
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [progress, setProgress] = useState<number>(0);

  const timerRef = useRef<number | null>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const circleRef = useRef<SVGCircleElement>(null);
  const animationRef = useRef<HTMLDivElement>(null);

  // Breathing patterns
  const breathingPatterns = useMemo<BreathingPattern[]>(() => [
    {
      id: 'box-breathing',
      name: 'Box Breathing',
      description: 'A simple yet powerful technique used by athletes, military personnel, and yogis to induce calm and focus.',
      inhaleTime: 4,
      holdInTime: 4,
      exhaleTime: 4,
      holdOutTime: 4,
      cycles: 8,
      benefits: [
        'Reduces stress and anxiety',
        'Improves concentration and focus',
        'Regulates the autonomic nervous system',
        'Can help manage pain',
        'Improves sleep quality'
      ],
      instructions: 'Sit comfortably with your back straight. Breathe in through your nose for 4 counts, hold for 4 counts, exhale through your mouth for 4 counts, and hold empty for 4 counts. Repeat for 8 cycles.',
      level: 'beginner'
    },
    {
      id: '4-7-8-breathing',
      name: '4-7-8 Breathing',
      description: 'A relaxing breath pattern developed by Dr. Andrew Weil, based on pranayama techniques.',
      inhaleTime: 4,
      holdInTime: 7,
      exhaleTime: 8,
      holdOutTime: 0,
      cycles: 5,
      benefits: [
        'Helps you fall asleep faster',
        'Reduces anxiety and stress',
        'Manages food cravings',
        'Helps control emotional responses',
        'Lowers heart rate and blood pressure'
      ],
      instructions: 'Sit with your back straight. Place the tip of your tongue against the ridge behind your upper front teeth. Exhale completely through your mouth. Close your mouth and inhale through your nose for 4 counts. Hold your breath for 7 counts. Exhale completely through your mouth for 8 counts. Repeat for 5 cycles.',
      level: 'beginner'
    },
    {
      id: 'alternate-nostril',
      name: 'Alternate Nostril Breathing',
      description: 'A traditional yogic breathing technique (Nadi Shodhana) that balances the left and right hemispheres of the brain.',
      inhaleTime: 4,
      holdInTime: 2,
      exhaleTime: 4,
      holdOutTime: 0,
      cycles: 10,
      benefits: [
        'Balances the left and right brain hemispheres',
        'Improves respiratory function',
        'Reduces stress and anxiety',
        'Enhances focus and concentration',
        'Purifies the subtle energy channels'
      ],
      instructions: 'Sit comfortably. Use your right thumb to close your right nostril. Inhale through your left nostril for 4 counts. Close your left nostril with your ring finger, hold for 2 counts. Release your thumb and exhale through your right nostril for 4 counts. Inhale through your right nostril, then close it, and exhale through your left. This is one cycle. Repeat for 10 cycles.',
      level: 'intermediate'
    },
    {
      id: 'ujjayi-breathing',
      name: 'Ujjayi Breathing',
      description: 'The "Victorious Breath" or "Ocean Breath" commonly used in yoga practice, especially during asana practice.',
      inhaleTime: 5,
      holdInTime: 0,
      exhaleTime: 5,
      holdOutTime: 0,
      cycles: 12,
      benefits: [
        'Increases oxygen intake',
        'Builds internal body heat',
        'Improves concentration',
        'Releases tension',
        'Strengthens the nervous system'
      ],
      instructions: 'Sit comfortably or practice during yoga poses. Inhale deeply through your nose for 5 counts. Slightly constrict the back of your throat to create a soft, audible sound like ocean waves. Exhale through your nose with the same throat constriction for 5 counts. Repeat for 12 cycles.',
      level: 'intermediate'
    },
    {
      id: 'kapalabhati',
      name: 'Kapalabhati',
      description: 'A cleansing breath technique also known as "Skull Shining Breath" that energizes the body and mind.',
      inhaleTime: 1,
      holdInTime: 0,
      exhaleTime: 1,
      holdOutTime: 0,
      cycles: 30,
      benefits: [
        'Cleanses the respiratory system',
        'Energizes the body and mind',
        'Strengthens the abdominal muscles',
        'Improves digestion',
        'Clears the nadis (energy channels)'
      ],
      instructions: 'Sit comfortably with a straight spine. Take a deep breath in, then exhale forcefully through the nose by contracting your abdominal muscles. Inhalation happens passively as the abdomen relaxes. Focus on quick, forceful exhalations with passive inhalations. Start with 30 breaths, then rest and observe your breath.',
      level: 'advanced'
    }
  ], []);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Get the selected breathing pattern
  const getSelectedPattern = useCallback((): BreathingPattern => {
    return breathingPatterns.find(pattern => pattern.id === selectedPattern) || breathingPatterns[0];
  }, [selectedPattern, breathingPatterns]);

  // Calculate total time for a single cycle
  const calculateCycleTime = (pattern: BreathingPattern): number => {
    return pattern.inhaleTime + pattern.holdInTime + pattern.exhaleTime + pattern.holdOutTime;
  };

  // Calculate total time for all cycles
  const calculateTotalTime = (pattern: BreathingPattern): number => {
    return calculateCycleTime(pattern) * pattern.cycles;
  };

  // Initialize the exercise
  const initializeExercise = () => {
    const pattern = getSelectedPattern();
    setCurrentPhase('inhale');
    setCurrentCycle(1);
    setTimeLeft(pattern.inhaleTime);
    // No need to set total time as we calculate it when needed
    setProgress(0);
  };

  // Start the breathing exercise
  const startExercise = () => {
    if (isRunning && !isPaused) return;

    if (!isRunning) {
      initializeExercise();
    }

    setIsRunning(true);
    setIsPaused(false);
  };

  // Pause the breathing exercise
  const pauseExercise = () => {
    setIsPaused(true);
  };

  // Stop the breathing exercise
  const stopExercise = () => {
    setIsRunning(false);
    setIsPaused(false);
    initializeExercise();
  };

  // Handle pattern change
  const handlePatternChange = (patternId: string) => {
    if (isRunning) {
      stopExercise();
    }
    setSelectedPattern(patternId);
  };

  // Update the breathing animation
  useEffect(() => {
    if (animationRef.current) {
      const animationElement = animationRef.current;

      if (isRunning && !isPaused) {
        animationElement.classList.remove('animate-pulse', 'scale-100', 'scale-125');

        if (currentPhase === 'inhale') {
          animationElement.classList.add('animate-breathe-in');
          animationElement.style.animationDuration = `${getSelectedPattern().inhaleTime}s`;
        } else if (currentPhase === 'exhale') {
          animationElement.classList.add('animate-breathe-out');
          animationElement.style.animationDuration = `${getSelectedPattern().exhaleTime}s`;
        } else {
          animationElement.classList.add('animate-pulse');
        }
      } else {
        animationElement.classList.remove('animate-breathe-in', 'animate-breathe-out');
        animationElement.classList.add('animate-pulse');
      }
    }
  }, [currentPhase, isRunning, isPaused, getSelectedPattern]);

  // Timer logic
  useEffect(() => {
    if (isRunning && !isPaused) {
      timerRef.current = window.setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            // Move to the next phase or cycle
            const pattern = getSelectedPattern();
            let nextPhase: 'inhale' | 'hold-in' | 'exhale' | 'hold-out' = 'inhale';
            let nextCycle = currentCycle;
            let nextTimeLeft = 0;

            if (currentPhase === 'inhale') {
              if (pattern.holdInTime > 0) {
                nextPhase = 'hold-in';
                nextTimeLeft = pattern.holdInTime;
              } else {
                nextPhase = 'exhale';
                nextTimeLeft = pattern.exhaleTime;
              }
            } else if (currentPhase === 'hold-in') {
              nextPhase = 'exhale';
              nextTimeLeft = pattern.exhaleTime;
            } else if (currentPhase === 'exhale') {
              if (pattern.holdOutTime > 0) {
                nextPhase = 'hold-out';
                nextTimeLeft = pattern.holdOutTime;
              } else {
                nextPhase = 'inhale';
                nextTimeLeft = pattern.inhaleTime;
                nextCycle = currentCycle + 1;
              }
            } else if (currentPhase === 'hold-out') {
              nextPhase = 'inhale';
              nextTimeLeft = pattern.inhaleTime;
              nextCycle = currentCycle + 1;
            }

            // Check if we've completed all cycles
            if (nextCycle > pattern.cycles) {
              clearInterval(timerRef.current as number);
              setIsRunning(false);
              setCurrentCycle(pattern.cycles);
              setProgress(100);
              return 0;
            }

            setCurrentPhase(nextPhase);
            setCurrentCycle(nextCycle);

            // Update progress
            const cycleTime = calculateCycleTime(pattern);
            const totalTime = cycleTime * pattern.cycles;
            const elapsedTime = (nextCycle - 1) * cycleTime +
                               (nextPhase === 'inhale' ? 0 :
                                nextPhase === 'hold-in' ? pattern.inhaleTime :
                                nextPhase === 'exhale' ? pattern.inhaleTime + pattern.holdInTime :
                                pattern.inhaleTime + pattern.holdInTime + pattern.exhaleTime);

            setProgress((elapsedTime / totalTime) * 100);

            return nextTimeLeft;
          }
          return prevTime - 1;
        });
      }, 1000);
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [isRunning, isPaused, currentPhase, currentCycle, getSelectedPattern]);

  // Update circle progress
  useEffect(() => {
    if (circleRef.current) {
      const circumference = 2 * Math.PI * 120; // Circle radius is 120
      const offset = circumference - (progress / 100) * circumference;
      circleRef.current.style.strokeDashoffset = offset.toString();
    }
  }, [progress]);

  // Get the current phase instruction
  const getPhaseInstruction = (): string => {
    switch (currentPhase) {
      case 'inhale':
        return 'Inhale';
      case 'hold-in':
        return 'Hold';
      case 'exhale':
        return 'Exhale';
      case 'hold-out':
        return 'Hold';
      default:
        return '';
    }
  };

  // Format time as MM:SS
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const selectedPatternData = getSelectedPattern();

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Breathing <span className="text-amber-500 dark:text-amber-400">Exercise</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Practice mindful breathing techniques to reduce stress, increase focus, and improve overall well-being.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left column: Breathing patterns */}
            <div className="lg:col-span-1 space-y-6">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Breathing Patterns</h3>

                <div className="space-y-3">
                  {breathingPatterns.map(pattern => (
                    <button
                      key={pattern.id}
                      onClick={() => handlePatternChange(pattern.id)}
                      className={`w-full p-3 rounded-lg text-left transition-all duration-300 ${
                        selectedPattern === pattern.id
                          ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 shadow-sm'
                          : 'bg-white dark:bg-slate-600 text-slate-700 dark:text-slate-300 hover:bg-amber-50 dark:hover:bg-slate-500'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{pattern.name}</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          pattern.level === 'beginner'
                            ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                            : pattern.level === 'intermediate'
                              ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300'
                              : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                        }`}>
                          {pattern.level.charAt(0).toUpperCase() + pattern.level.slice(1)}
                        </span>
                      </div>
                      <p className="text-sm text-slate-600 dark:text-slate-400 mt-1 line-clamp-2">{pattern.description}</p>
                    </button>
                  ))}
                </div>
              </div>

              <div className="bg-amber-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Benefits</h3>
                <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                  {selectedPatternData.benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Right column: Breathing exercise */}
            <div className="lg:col-span-2">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md h-full">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white">{selectedPatternData.name}</h3>
                  <p className="text-slate-700 dark:text-slate-300 mt-2">{selectedPatternData.instructions}</p>
                </div>

                <div className="flex flex-col items-center justify-center">
                  {/* Breathing animation and timer */}
                  <div className="relative w-64 h-64 md:w-80 md:h-80 mb-8">
                    {/* Progress circle */}
                    <svg className="w-full h-full" viewBox="0 0 256 256">
                      {/* Background circle */}
                      <circle
                        cx="128"
                        cy="128"
                        r="120"
                        fill="none"
                        stroke="#e2e8f0"
                        strokeWidth="8"
                        className="dark:stroke-slate-600"
                      />
                      {/* Progress circle */}
                      <circle
                        ref={circleRef}
                        cx="128"
                        cy="128"
                        r="120"
                        fill="none"
                        stroke="#f59e0b"
                        strokeWidth="8"
                        strokeLinecap="round"
                        strokeDasharray={2 * Math.PI * 120}
                        strokeDashoffset="0"
                        transform="rotate(-90 128 128)"
                        className="dark:stroke-amber-400 transition-all duration-1000 ease-linear"
                      />
                    </svg>

                    {/* Breathing animation */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div
                        ref={animationRef}
                        className={`w-32 h-32 rounded-full bg-amber-400/20 dark:bg-amber-400/10 animate-pulse transition-all duration-1000`}
                      ></div>
                    </div>

                    {/* Timer display */}
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <div className="text-5xl md:text-6xl font-bold text-slate-900 dark:text-white mb-2">
                        {timeLeft}
                      </div>
                      <div className="text-amber-500 dark:text-amber-400 font-medium text-xl">
                        {isRunning ? getPhaseInstruction() : 'Ready'}
                      </div>
                      {isRunning && (
                        <div className="text-slate-600 dark:text-slate-400 text-sm mt-1">
                          Cycle {currentCycle} of {selectedPatternData.cycles}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Pattern timing info */}
                  <div className="grid grid-cols-4 gap-4 mb-8 w-full max-w-md">
                    <div className="text-center">
                      <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-2 shadow-inner">
                        <div className="text-xl font-bold text-amber-500 dark:text-amber-400">
                          {selectedPatternData.inhaleTime}s
                        </div>
                      </div>
                      <div className="text-slate-600 dark:text-slate-400 text-sm mt-1">Inhale</div>
                    </div>
                    <div className="text-center">
                      <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-2 shadow-inner">
                        <div className="text-xl font-bold text-amber-500 dark:text-amber-400">
                          {selectedPatternData.holdInTime}s
                        </div>
                      </div>
                      <div className="text-slate-600 dark:text-slate-400 text-sm mt-1">Hold In</div>
                    </div>
                    <div className="text-center">
                      <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-2 shadow-inner">
                        <div className="text-xl font-bold text-amber-500 dark:text-amber-400">
                          {selectedPatternData.exhaleTime}s
                        </div>
                      </div>
                      <div className="text-slate-600 dark:text-slate-400 text-sm mt-1">Exhale</div>
                    </div>
                    <div className="text-center">
                      <div className="bg-amber-50 dark:bg-slate-800 rounded-lg p-2 shadow-inner">
                        <div className="text-xl font-bold text-amber-500 dark:text-amber-400">
                          {selectedPatternData.holdOutTime}s
                        </div>
                      </div>
                      <div className="text-slate-600 dark:text-slate-400 text-sm mt-1">Hold Out</div>
                    </div>
                  </div>

                  {/* Total time */}
                  <div className="mb-8 text-center">
                    <div className="text-slate-700 dark:text-slate-300">
                      Total Time: <span className="font-medium">{formatTime(calculateTotalTime(selectedPatternData))}</span>
                    </div>
                  </div>

                  {/* Control buttons */}
                  <div className="flex space-x-4">
                    {!isRunning ? (
                      <button
                        onClick={startExercise}
                        className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300 flex items-center"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Start Exercise
                      </button>
                    ) : isPaused ? (
                      <button
                        onClick={startExercise}
                        className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300 flex items-center"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Resume
                      </button>
                    ) : (
                      <button
                        onClick={pauseExercise}
                        className="px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300 flex items-center"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Pause
                      </button>
                    )}

                    <button
                      onClick={stopExercise}
                      className="px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-600 dark:hover:bg-slate-500 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300 flex items-center"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"></path>
                      </svg>
                      Stop
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add custom animation keyframes */}
      <style>{`
        @keyframes breathe-in {
          0% {
            transform: scale(1);
          }
          100% {
            transform: scale(1.5);
          }
        }

        @keyframes breathe-out {
          0% {
            transform: scale(1.5);
          }
          100% {
            transform: scale(1);
          }
        }

        .animate-breathe-in {
          animation: breathe-in forwards;
        }

        .animate-breathe-out {
          animation: breathe-out forwards;
        }
      `}</style>
    </div>
  );
};

export default BreathingExercise;