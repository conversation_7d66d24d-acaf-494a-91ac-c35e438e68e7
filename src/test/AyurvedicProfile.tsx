import React, { useState, useRef, useEffect } from 'react';

interface Question {
  id: string;
  text: string;
  options: {
    id: string;
    text: string;
    vata: number;
    pitta: number;
    kapha: number;
  }[];
}

interface DoshaResult {
  vata: number;
  pitta: number;
  kapha: number;
}

interface DoshaInfo {
  name: string;
  sanskrit: string;
  elements: string[];
  qualities: string[];
  strengths: string[];
  challenges: string[];
  balanceTips: string[];
  image: string;
}

const AyurvedicProfile: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [answers, setAnswers] = useState<string[]>([]);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [doshaResult, setDoshaResult] = useState<DoshaResult>({ vata: 0, pitta: 0, kapha: 0 });
  const [dominantDosha, setDominantDosha] = useState<string>('');
  const [secondaryDosha, setSecondaryDosha] = useState<string>('');
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Quiz questions
  const questions: Question[] = [
    {
      id: 'q1',
      text: 'How would you describe your body frame?',
      options: [
        { id: 'q1a', text: 'Thin, lean, or tall with prominent joints and veins', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q1b', text: 'Medium build with good muscle development', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q1c', text: 'Larger, solid build with well-developed body', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q2',
      text: 'How is your skin typically?',
      options: [
        { id: 'q2a', text: 'Dry, rough, or thin', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q2b', text: 'Warm, reddish, with freckles or acne', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q2c', text: 'Thick, oily, smooth, and cool', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q3',
      text: 'How would you describe your hair?',
      options: [
        { id: 'q3a', text: 'Dry, frizzy, or brittle', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q3b', text: 'Fine, straight, early graying or balding', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q3c', text: 'Thick, wavy, and oily', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q4',
      text: 'How is your appetite usually?',
      options: [
        { id: 'q4a', text: 'Variable, sometimes forget to eat', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q4b', text: 'Strong, get irritable if meals are missed', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q4c', text: 'Steady but can skip meals without discomfort', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q5',
      text: 'How do you typically respond to stress?',
      options: [
        { id: 'q5a', text: 'Anxious, worried, or overwhelmed', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q5b', text: 'Irritable, frustrated, or angry', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q5c', text: 'Calm, steady, or withdrawn', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q6',
      text: 'How would you describe your sleep pattern?',
      options: [
        { id: 'q6a', text: 'Light sleeper, tendency to wake up easily', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q6b', text: 'Moderate sleep, usually 6-7 hours is sufficient', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q6c', text: 'Deep sleeper, can sleep for long periods', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q7',
      text: 'How do you typically speak?',
      options: [
        { id: 'q7a', text: 'Quickly, sometimes jumps between topics', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q7b', text: 'Clear, precise, and to the point', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q7c', text: 'Slowly, methodically, with a melodious voice', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q8',
      text: 'How would you describe your energy levels throughout the day?',
      options: [
        { id: 'q8a', text: 'Variable, comes in bursts', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q8b', text: 'Strong and purposeful', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q8c', text: 'Steady but sometimes lethargic', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q9',
      text: 'How do you approach new situations or challenges?',
      options: [
        { id: 'q9a', text: 'Enthusiastic initially but may lose interest', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q9b', text: 'Focused, determined, and goal-oriented', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q9c', text: 'Methodical, careful, and thoughtful', vata: 0, pitta: 0, kapha: 2 }
      ]
    },
    {
      id: 'q10',
      text: 'How would you describe your memory?',
      options: [
        { id: 'q10a', text: 'Quick to learn but also quick to forget', vata: 2, pitta: 0, kapha: 0 },
        { id: 'q10b', text: 'Sharp and clear, especially for facts and details', vata: 0, pitta: 2, kapha: 0 },
        { id: 'q10c', text: 'Slow to learn but excellent long-term retention', vata: 0, pitta: 0, kapha: 2 }
      ]
    }
  ];

  // Dosha information
  const doshaInfo: { [key: string]: DoshaInfo } = {
    vata: {
      name: 'Vata',
      sanskrit: 'वात',
      elements: ['Air', 'Space'],
      qualities: ['Light', 'Dry', 'Cold', 'Rough', 'Subtle', 'Mobile'],
      strengths: [
        'Creativity and imagination',
        'Quick thinking and adaptability',
        'Enthusiasm and liveliness',
        'Spiritual connection',
        'Flexibility'
      ],
      challenges: [
        'Anxiety and worry',
        'Irregular habits',
        'Difficulty with focus and concentration',
        'Sensitivity to cold',
        'Dry skin and joints'
      ],
      balanceTips: [
        'Establish regular daily routines',
        'Stay warm and avoid cold, windy conditions',
        'Practice grounding meditation',
        'Eat warm, cooked foods with healthy oils',
        'Get adequate rest and avoid overexertion'
      ],
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Vata+Dosha'
    },
    pitta: {
      name: 'Pitta',
      sanskrit: 'पित्त',
      elements: ['Fire', 'Water'],
      qualities: ['Hot', 'Sharp', 'Light', 'Oily', 'Liquid', 'Spreading'],
      strengths: [
        'Intelligence and focus',
        'Strong digestion and metabolism',
        'Courage and confidence',
        'Leadership abilities',
        'Precision and organization'
      ],
      challenges: [
        'Irritability and anger',
        'Perfectionism',
        'Sensitivity to heat',
        'Skin inflammation',
        'Digestive sensitivity'
      ],
      balanceTips: [
        'Avoid excessive heat and direct sunlight',
        'Practice cooling breath work',
        'Eat cooling foods like fresh vegetables',
        'Make time for play and relaxation',
        'Avoid excessive competition and intensity'
      ],
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Pitta+Dosha'
    },
    kapha: {
      name: 'Kapha',
      sanskrit: 'कफ',
      elements: ['Earth', 'Water'],
      qualities: ['Heavy', 'Slow', 'Cold', 'Oily', 'Smooth', 'Dense', 'Soft', 'Stable'],
      strengths: [
        'Physical strength and endurance',
        'Calm and steady emotions',
        'Loyalty and reliability',
        'Good memory',
        'Compassion and forgiveness'
      ],
      challenges: [
        'Resistance to change',
        'Tendency toward lethargy',
        'Weight gain',
        'Congestion and mucus',
        'Attachment and possessiveness'
      ],
      balanceTips: [
        'Regular vigorous exercise',
        'Seek variety and new experiences',
        'Favor warm, light, and spicy foods',
        'Rise early and avoid daytime naps',
        'Use dry brushing and stimulating self-massage'
      ],
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Kapha+Dosha'
    }
  };

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Scroll to results when they're shown
  useEffect(() => {
    if (showResults && resultsRef.current) {
      resultsRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [showResults]);

  const handleOptionSelect = (optionId: string) => {
    const newAnswers = [...answers];
    newAnswers[currentQuestionIndex] = optionId;
    setAnswers(newAnswers);

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      calculateResults(newAnswers);
    }
  };

  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const calculateResults = (finalAnswers: string[]) => {
    let vataScore = 0;
    let pittaScore = 0;
    let kaphaScore = 0;

    finalAnswers.forEach((answerId, index) => {
      const question = questions[index];
      const selectedOption = question.options.find(option => option.id === answerId);
      
      if (selectedOption) {
        vataScore += selectedOption.vata;
        pittaScore += selectedOption.pitta;
        kaphaScore += selectedOption.kapha;
      }
    });

    const result = { vata: vataScore, pitta: pittaScore, kapha: kaphaScore };
    setDoshaResult(result);

    // Determine dominant and secondary doshas
    const scores = [
      { dosha: 'vata', score: vataScore },
      { dosha: 'pitta', score: pittaScore },
      { dosha: 'kapha', score: kaphaScore }
    ].sort((a, b) => b.score - a.score);

    setDominantDosha(scores[0].dosha);
    setSecondaryDosha(scores[1].dosha);
    setShowResults(true);
  };

  const resetQuiz = () => {
    setCurrentQuestionIndex(0);
    setAnswers([]);
    setShowResults(false);
    setDoshaResult({ vata: 0, pitta: 0, kapha: 0 });
    setDominantDosha('');
    setSecondaryDosha('');
  };

  const getProgressPercentage = () => {
    return ((currentQuestionIndex + 1) / questions.length) * 100;
  };

  const getDoshaPercentage = (dosha: keyof DoshaResult) => {
    const total = doshaResult.vata + doshaResult.pitta + doshaResult.kapha;
    return total > 0 ? Math.round((doshaResult[dosha] / total) * 100) : 0;
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Discover Your <span className="text-amber-500 dark:text-amber-400">Ayurvedic Constitution</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Take this quiz to learn about your unique mind-body type according to Ayurveda, the ancient science of life.
            </p>
          </div>

          {!showResults ? (
            <div className="max-w-3xl mx-auto">
              {/* Progress bar */}
              <div className="mb-8">
                <div className="flex justify-between text-sm text-slate-600 dark:text-slate-400 mb-2">
                  <span>Question {currentQuestionIndex + 1} of {questions.length}</span>
                  <span>{getProgressPercentage()}% Complete</span>
                </div>
                <div className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-amber-500 dark:bg-amber-400 transition-all duration-300"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>
              </div>

              {/* Current question */}
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 md:p-8 mb-6 transition-all duration-300">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                  {questions[currentQuestionIndex].text}
                </h3>
                <div className="space-y-4">
                  {questions[currentQuestionIndex].options.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => handleOptionSelect(option.id)}
                      className={`w-full text-left p-4 rounded-lg border transition-all duration-300 ${
                        answers[currentQuestionIndex] === option.id
                          ? 'border-amber-500 bg-amber-50 dark:bg-amber-900/20 dark:border-amber-400'
                          : 'border-slate-300 dark:border-slate-600 hover:border-amber-300 dark:hover:border-amber-500'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 rounded-full border flex-shrink-0 mr-3 flex items-center justify-center ${
                          answers[currentQuestionIndex] === option.id
                            ? 'border-amber-500 dark:border-amber-400'
                            : 'border-slate-400 dark:border-slate-500'
                        }`}>
                          {answers[currentQuestionIndex] === option.id && (
                            <div className="w-3 h-3 rounded-full bg-amber-500 dark:bg-amber-400"></div>
                          )}
                        </div>
                        <span className="text-slate-800 dark:text-slate-200">{option.text}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Navigation buttons */}
              <div className="flex justify-between">
                <button
                  onClick={handlePrevQuestion}
                  disabled={currentQuestionIndex === 0}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                    currentQuestionIndex === 0
                      ? 'bg-slate-200 dark:bg-slate-700 text-slate-400 dark:text-slate-500 cursor-not-allowed'
                      : 'bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300'
                  }`}
                >
                  Previous
                </button>
                {currentQuestionIndex === questions.length - 1 && answers[currentQuestionIndex] && (
                  <button
                    onClick={() => calculateResults(answers)}
                    className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300"
                  >
                    See Results
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div ref={resultsRef} className="max-w-4xl mx-auto">
              <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8 mb-10 text-center">
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
                  Your Ayurvedic Constitution
                </h3>
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Based on your responses, your dominant dosha is <span className="font-semibold text-amber-600 dark:text-amber-400">{doshaInfo[dominantDosha].name}</span> with <span className="font-semibold text-amber-600 dark:text-amber-400">{doshaInfo[secondaryDosha].name}</span> as your secondary influence.
                </p>
                
                {/* Dosha distribution chart */}
                <div className="mb-8">
                  <div className="flex h-6 rounded-full overflow-hidden mb-2">
                    <div 
                      className="bg-blue-400 dark:bg-blue-500 transition-all duration-500"
                      style={{ width: `${getDoshaPercentage('vata')}%` }}
                    ></div>
                    <div 
                      className="bg-red-400 dark:bg-red-500 transition-all duration-500"
                      style={{ width: `${getDoshaPercentage('pitta')}%` }}
                    ></div>
                    <div 
                      className="bg-green-400 dark:bg-green-500 transition-all duration-500"
                      style={{ width: `${getDoshaPercentage('kapha')}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-400 dark:bg-blue-500 rounded-full mr-1"></div>
                      <span className="text-slate-700 dark:text-slate-300">Vata {getDoshaPercentage('vata')}%</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-400 dark:bg-red-500 rounded-full mr-1"></div>
                      <span className="text-slate-700 dark:text-slate-300">Pitta {getDoshaPercentage('pitta')}%</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-400 dark:bg-green-500 rounded-full mr-1"></div>
                      <span className="text-slate-700 dark:text-slate-300">Kapha {getDoshaPercentage('kapha')}%</span>
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={resetQuiz}
                  className="px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300"
                >
                  Retake Quiz
                </button>
              </div>
              
              {/* Dominant dosha details */}
              <div className="mb-12">
                <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6 border-b border-slate-200 dark:border-slate-700 pb-2">
                  Understanding Your {doshaInfo[dominantDosha].name} Dosha
                </h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div className="lg:col-span-1">
                    <div className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md">
                      <img 
                        src={doshaInfo[dominantDosha].image} 
                        alt={`${doshaInfo[dominantDosha].name} Dosha`}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h4 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                          {doshaInfo[dominantDosha].name} <span className="text-amber-500 dark:text-amber-400">({doshaInfo[dominantDosha].sanskrit})</span>
                        </h4>
                        <p className="text-slate-700 dark:text-slate-300 mb-4">
                          Elements: {doshaInfo[dominantDosha].elements.join(' & ')}
                        </p>
                        <div className="mb-4">
                          <h5 className="font-medium text-slate-900 dark:text-white mb-2">Qualities:</h5>
                          <div className="flex flex-wrap gap-2">
                            {doshaInfo[dominantDosha].qualities.map((quality, index) => (
                              <span 
                                key={index}
                                className="inline-block px-3 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-sm"
                              >
                                {quality}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="lg:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                          <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                          </svg>
                          Strengths
                        </h4>
                        <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                          {doshaInfo[dominantDosha].strengths.map((strength, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                              {strength}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                          <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                          </svg>
                          Challenges
                        </h4>
                        <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                          {doshaInfo[dominantDosha].challenges.map((challenge, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                              {challenge}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md md:col-span-2">
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                          <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                          </svg>
                          Balance Tips
                        </h4>
                        <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                          {doshaInfo[dominantDosha].balanceTips.map((tip, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Secondary dosha influence */}
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                  Your Secondary {doshaInfo[secondaryDosha].name} Influence
                </h3>
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Your secondary dosha also plays an important role in your constitution. Here are some key aspects of {doshaInfo[secondaryDosha].name} that may influence you:
                </p>
                
                <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-3">Key Strengths</h4>
                      <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                        {doshaInfo[secondaryDosha].strengths.slice(0, 3).map((strength, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-3">Potential Challenges</h4>
                      <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                        {doshaInfo[secondaryDosha].challenges.slice(0, 3).map((challenge, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                            {challenge}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-3">Balance Tips</h4>
                      <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                        {doshaInfo[secondaryDosha].balanceTips.slice(0, 3).map((tip, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Disclaimer */}
              <div className="bg-slate-100 dark:bg-slate-700/50 rounded-xl p-6 text-sm text-slate-600 dark:text-slate-400">
                <p>
                  <strong>Disclaimer:</strong> This quiz provides a simplified assessment of your Ayurvedic constitution (prakriti). For a comprehensive analysis, please consult with a qualified Ayurvedic practitioner. Your constitution can be influenced by many factors including age, season, and lifestyle.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AyurvedicProfile;