import React, { useState, useRef, useEffect } from 'react';

interface YogaStyle {
  id: string;
  name: string;
  sanskrit?: string;
  origin: string;
  founded: string;
  intensity: 1 | 2 | 3 | 4 | 5; // 1 = gentle, 5 = intense
  focus: string[];
  benefits: string[];
  suitableFor: string[];
  notRecommendedFor: string[];
  description: string;
  image: string;
}

const YogaStyleComparison: React.FC = () => {
  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);
  const [showAllStyles, setShowAllStyles] = useState<boolean>(false);
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Yoga styles data
  const yogaStyles: YogaStyle[] = [
    {
      id: 'hatha',
      name: 'Hatha Yoga',
      sanskrit: 'हठ योग',
      origin: 'India',
      founded: '15th century',
      intensity: 2,
      focus: ['Physical postures', 'Breath control', 'Balance', 'Flexibility'],
      benefits: [
        'Improved flexibility and balance',
        'Stress reduction',
        'Better posture',
        'Gentle strength building',
        'Mindfulness development'
      ],
      suitableFor: [
        'Beginners',
        'Those seeking a gentle practice',
        'People recovering from injury',
        'Seniors',
        'Those wanting to learn foundational poses'
      ],
      notRecommendedFor: [
        'Those seeking intense cardio workout',
        'People looking for rapid weight loss'
      ],
      description: 'Hatha yoga is a traditional form that emphasizes physical postures (asanas) and breath control (pranayama). It serves as the foundation for many modern yoga styles and is generally taught at a slower pace with a focus on proper alignment and holding poses for several breaths. The word "hatha" can be translated as "forceful" in Sanskrit, but a typical class is gentle and accessible to beginners.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Hatha+Yoga'
    },
    {
      id: 'vinyasa',
      name: 'Vinyasa Flow',
      sanskrit: 'विन्यास',
      origin: 'India (modern adaptation)',
      founded: '20th century',
      intensity: 4,
      focus: ['Flowing sequences', 'Breath-movement coordination', 'Creativity', 'Cardiovascular fitness'],
      benefits: [
        'Improved cardiovascular health',
        'Increased strength and flexibility',
        'Enhanced mind-body connection',
        'Stress reduction',
        'Endurance building'
      ],
      suitableFor: [
        'Those seeking a more dynamic practice',
        'People who enjoy variety',
        'Fitness enthusiasts',
        'Those who get bored easily'
      ],
      notRecommendedFor: [
        'Complete beginners without guidance',
        'Those with certain injuries',
        'People who prefer slow, methodical practice',
        'Those with limited mobility'
      ],
      description: 'Vinyasa yoga connects movement with breath in a flowing sequence of poses. Unlike some forms of yoga where poses are held for several minutes, vinyasa classes feature a continuous flow of movements that synchronize with inhalations and exhalations. Classes vary widely but typically include sun salutations and creative sequences that build heat, strength, and flexibility. The dynamic nature of vinyasa makes each class unique.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Vinyasa+Flow'
    },
    {
      id: 'ashtanga',
      name: 'Ashtanga Yoga',
      sanskrit: 'अष्टांग योग',
      origin: 'India',
      founded: 'Developed by K. Pattabhi Jois in the 20th century',
      intensity: 5,
      focus: ['Set sequence', 'Strength', 'Discipline', 'Internal heat', 'Breath control'],
      benefits: [
        'Builds significant strength and stamina',
        'Improves focus and discipline',
        'Detoxifies through heat and sweat',
        'Creates a moving meditation',
        'Develops consistent practice'
      ],
      suitableFor: [
        'Athletic practitioners',
        'Those who thrive on routine',
        'Dedicated students seeking depth',
        'People looking for physical challenge'
      ],
      notRecommendedFor: [
        'Beginners without proper guidance',
        'Those with significant injuries',
        'People who prefer gentle exercise',
        'Those who dislike routine'
      ],
      description: 'Ashtanga yoga follows a specific sequence of postures always performed in the same order. It is a rigorous practice that builds internal heat and intense focus through a fixed series of postures linked by breath. Traditional practice involves "Mysore style" where students practice at their own pace in a group setting with individualized instruction. The Primary Series, known as "Yoga Chikitsa" (yoga therapy), detoxifies and aligns the body.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Ashtanga+Yoga'
    },
    {
      id: 'iyengar',
      name: 'Iyengar Yoga',
      sanskrit: 'अयंगर योग',
      origin: 'India',
      founded: 'Developed by B.K.S. Iyengar in the 20th century',
      intensity: 3,
      focus: ['Precise alignment', 'Props usage', 'Detailed instruction', 'Longer holds', 'Therapeutic applications'],
      benefits: [
        'Improved alignment and posture',
        'Accessibility through prop modifications',
        'Therapeutic for injuries and conditions',
        'Develops body awareness and precision',
        'Builds strength through proper alignment'
      ],
      suitableFor: [
        'Detail-oriented practitioners',
        'Those recovering from injuries',
        'People with physical limitations',
        'Students seeking technical precision',
        'Those interested in yoga therapy'
      ],
      notRecommendedFor: [
        'Those seeking primarily cardio exercise',
        'People who dislike detailed instruction',
        'Practitioners who prefer flowing movement'
      ],
      description: 'Iyengar yoga emphasizes precise alignment and the use of props (blocks, straps, blankets, chairs) to help students achieve proper form. Poses are typically held for longer periods while adjustments are made. This methodical approach makes yoga accessible to people of all ages and physical conditions while providing a framework for progressing safely. Iyengar teachers undergo rigorous training and certification, ensuring high-quality instruction.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Iyengar+Yoga'
    },
    {
      id: 'kundalini',
      name: 'Kundalini Yoga',
      sanskrit: 'कुण्डलिनी योग',
      origin: 'India',
      founded: 'Ancient practice, popularized in the West by Yogi Bhajan in the 1960s',
      intensity: 3,
      focus: ['Energy awakening', 'Kriyas (movement sets)', 'Breathwork', 'Meditation', 'Chanting'],
      benefits: [
        'Increased energy and vitality',
        'Enhanced awareness and consciousness',
        'Stress reduction and nervous system regulation',
        'Strengthened immune system',
        'Emotional balance and clarity'
      ],
      suitableFor: [
        'Those interested in spiritual development',
        'People seeking transformation',
        'Practitioners looking for both physical and energetic practice',
        'Those drawn to meditation and breathwork'
      ],
      notRecommendedFor: [
        'Those uncomfortable with chanting or spiritual elements',
        'People seeking purely physical exercise',
        'Those with certain psychiatric conditions without professional guidance'
      ],
      description: 'Kundalini yoga focuses on awakening the dormant energy (kundalini) at the base of the spine through specific breathwork, movement sequences (kriyas), meditation, and chanting. Classes typically include dynamic breathing techniques, mantras, mudras (hand positions), and exercises designed to target specific aspects of physical and mental well-being. The practice aims to expand consciousness and prepare the body, mind, and nervous system for the awakening of kundalini energy.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Kundalini+Yoga'
    },
    {
      id: 'restorative',
      name: 'Restorative Yoga',
      sanskrit: '',
      origin: 'Developed from Iyengar Yoga principles',
      founded: 'Popularized by Judith Hanson Lasater in the 1970s',
      intensity: 1,
      focus: ['Deep relaxation', 'Passive stretching', 'Nervous system regulation', 'Stress relief', 'Healing'],
      benefits: [
        'Deep stress reduction',
        'Improved sleep quality',
        'Nervous system regulation',
        'Enhanced recovery from illness or injury',
        'Counterbalance to busy lifestyle and other intense activities'
      ],
      suitableFor: [
        'Everyone, regardless of experience level',
        'Those recovering from illness or injury',
        'People with high stress levels',
        'Athletes needing recovery',
        'Those with fatigue or insomnia'
      ],
      notRecommendedFor: [
        'Those exclusively seeking physical fitness or weight loss',
        'People who find stillness challenging without preparation'
      ],
      description: "Restorative yoga uses props to support the body in positions of comfort and ease, holding passive poses for extended periods (typically 5-10 minutes). This practice activates the parasympathetic nervous system ('rest and digest' mode), allowing for deep relaxation and healing. Unlike more active styles, restorative yoga is not about stretching or strengthening but about releasing tension and finding complete rest. It's particularly beneficial during times of stress, illness, or recovery.",
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Restorative+Yoga'
    },
    {
      id: 'yin',
      name: 'Yin Yoga',
      origin: 'Blend of Hatha Yoga and Taoist principles',
      founded: 'Developed by Paulie Zink, Paul Grilley, and Sarah Powers in the late 20th century',
      intensity: 1,
      focus: ['Deep connective tissue', 'Meridian stimulation', 'Long holds', 'Meditation', 'Surrender'],
      benefits: [
        'Increased flexibility in connective tissues',
        'Enhanced joint mobility',
        'Balanced energy flow through meridians',
        'Improved meditation capacity',
        'Complementary to active yang practices'
      ],
      suitableFor: [
        'All levels of practitioners',
        'Athletes with tight muscles',
        'Those seeking balance to active lifestyles',
        'Meditation practitioners',
        'People with stress and anxiety'
      ],
      notRecommendedFor: [
        'Those with specific joint injuries without modifications',
        'People seeking cardiovascular exercise',
        'Those with hypermobility without proper guidance'
      ],
      description: 'Yin yoga targets the deep connective tissues (fascia, ligaments, joints) through passive poses held for extended periods (typically 3-5 minutes). While yang yoga styles (like vinyasa or ashtanga) work the muscles through repetitive movement, yin yoga works the deeper tissues through stillness and time. The practice is based partly on Traditional Chinese Medicine principles, with poses designed to stimulate meridian lines and improve energy flow. Yin yoga is both physically and mentally challenging, requiring patience and surrender.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Yin+Yoga'
    },
    {
      id: 'bikram',
      name: 'Bikram Yoga',
      origin: 'India/United States',
      founded: 'Developed by Bikram Choudhury in the 1970s',
      intensity: 4,
      focus: ['Heat', 'Fixed sequence', 'Endurance', 'Detoxification', 'Precision'],
      benefits: [
        'Increased flexibility due to heat',
        'Detoxification through sweating',
        'Cardiovascular endurance',
        'Mental discipline and focus',
        'Potential weight management'
      ],
      suitableFor: [
        'Those who enjoy heat and sweating',
        'Practitioners seeking physical challenge',
        'People who prefer structured, predictable classes',
        'Those looking to build mental endurance'
      ],
      notRecommendedFor: [
        'People with heat sensitivity or certain medical conditions',
        'Those with high blood pressure or heart conditions',
        'Pregnant women without prior experience',
        'People prone to dehydration'
      ],
      description: 'Bikram yoga consists of a specific series of 26 postures and two breathing exercises performed in a room heated to approximately 105°F (40.6°C) with 40% humidity. Each class runs exactly 90 minutes and follows the same sequence. The heat is intended to warm the muscles, increase flexibility, and promote detoxification through sweating. The practice is known for its challenging nature, both physically and mentally, as practitioners work through discomfort in the heated environment.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Bikram+Yoga'
    }
  ];

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const toggleStyleSelection = (styleId: string) => {
    if (selectedStyles.includes(styleId)) {
      setSelectedStyles(selectedStyles.filter(id => id !== styleId));
    } else {
      if (selectedStyles.length < 3) {
        setSelectedStyles([...selectedStyles, styleId]);
      }
    }
  };

  const clearSelection = () => {
    setSelectedStyles([]);
  };

  const renderIntensityLevel = (level: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((value) => (
          <div 
            key={value}
            className={`w-6 h-2 mx-0.5 rounded-full ${
              value <= level 
                ? 'bg-amber-500 dark:bg-amber-400' 
                : 'bg-slate-200 dark:bg-slate-600'
            }`}
          ></div>
        ))}
        <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
          {level === 1 ? 'Gentle' : level === 5 ? 'Intense' : ''}
        </span>
      </div>
    );
  };

  // Get styles to display in the grid
  const displayStyles = showAllStyles 
    ? yogaStyles 
    : yogaStyles.slice(0, 4);

  // Get selected styles for comparison
  const stylesToCompare = yogaStyles.filter(style => selectedStyles.includes(style.id));

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Yoga <span className="text-amber-500 dark:text-amber-400">Style Comparison</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore different yoga styles to find the practice that best suits your needs, goals, and preferences.
            </p>
          </div>

          {/* Style selection grid */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
              Explore Yoga Styles
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {displayStyles.map((style) => (
                <div 
                  key={style.id}
                  className={`bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md transition-all duration-300 ${
                    selectedStyles.includes(style.id) 
                      ? 'ring-2 ring-amber-500 dark:ring-amber-400' 
                      : 'hover:shadow-lg'
                  }`}
                >
                  <div className="relative h-48">
                    <img 
                      src={style.image} 
                      alt={style.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-0 right-0 p-2">
                      <button
                        onClick={() => toggleStyleSelection(style.id)}
                        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                          selectedStyles.includes(style.id)
                            ? 'bg-amber-500 text-white'
                            : 'bg-white/80 dark:bg-slate-800/80 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-amber-900/30'
                        }`}
                      >
                        {selectedStyles.includes(style.id) ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                      {style.name}
                      {style.sanskrit && (
                        <span className="text-sm font-normal text-amber-500 dark:text-amber-400 ml-2">
                          {style.sanskrit}
                        </span>
                      )}
                    </h4>
                    
                    <div className="mb-4">
                      <div className="text-sm text-slate-600 dark:text-slate-400 mb-1">Intensity</div>
                      {renderIntensityLevel(style.intensity)}
                    </div>
                    
                    <div className="mb-4">
                      <div className="text-sm text-slate-600 dark:text-slate-400 mb-1">Focus</div>
                      <div className="flex flex-wrap gap-1">
                        {style.focus.slice(0, 3).map((focus, index) => (
                          <span 
                            key={index}
                            className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-xs"
                          >
                            {focus}
                          </span>
                        ))}
                        {style.focus.length > 3 && (
                          <span className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-xs">
                            +{style.focus.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <p className="text-slate-700 dark:text-slate-300 text-sm line-clamp-3">
                      {style.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            {!showAllStyles && (
              <div className="text-center">
                <button
                  onClick={() => setShowAllStyles(true)}
                  className="px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300"
                >
                  Show All Styles
                </button>
              </div>
            )}
          </div>

          {/* Comparison section */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                Compare Styles
                <span className="ml-2 text-sm font-normal text-slate-600 dark:text-slate-400">
                  (Select up to 3)
                </span>
              </h3>
              
              {selectedStyles.length > 0 && (
                <button
                  onClick={clearSelection}
                  className="text-sm text-amber-600 dark:text-amber-400 hover:underline"
                >
                  Clear Selection
                </button>
              )}
            </div>
            
            {selectedStyles.length === 0 ? (
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-8 text-center">
                <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">No Styles Selected</h4>
                <p className="text-slate-700 dark:text-slate-300 max-w-md mx-auto">
                  Select yoga styles from the grid above to compare their features, benefits, and suitability.
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="p-4 text-left bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white rounded-tl-xl"></th>
                      {stylesToCompare.map((style) => (
                        <th key={style.id} className="p-4 text-center bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white last:rounded-tr-xl">
                          <div className="font-semibold">{style.name}</div>
                          {style.sanskrit && (
                            <div className="text-sm font-normal text-amber-500 dark:text-amber-400">
                              {style.sanskrit}
                            </div>
                          )}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Origin</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-center text-slate-700 dark:text-slate-300">
                          {style.origin}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Founded</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-center text-slate-700 dark:text-slate-300">
                          {style.founded}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Intensity</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-center">
                          <div className="flex justify-center">
                            {renderIntensityLevel(style.intensity)}
                          </div>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Focus</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-center">
                          <div className="flex flex-wrap justify-center gap-1">
                            {style.focus.map((focus, index) => (
                              <span 
                                key={index}
                                className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-xs"
                              >
                                {focus}
                              </span>
                            ))}
                          </div>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Key Benefits</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300">
                          <ul className="list-disc pl-5 text-sm text-left">
                            {style.benefits.map((benefit, index) => (
                              <li key={index} className="mb-1">{benefit}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Suitable For</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300">
                          <ul className="list-disc pl-5 text-sm text-left">
                            {style.suitableFor.map((person, index) => (
                              <li key={index} className="mb-1">{person}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 border-b border-slate-200 dark:border-slate-700 font-medium text-slate-900 dark:text-white">Not Recommended For</td>
                      {stylesToCompare.map((style) => (
                        <td key={style.id} className="p-4 border-b border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300">
                          <ul className="list-disc pl-5 text-sm text-left">
                            {style.notRecommendedFor.map((person, index) => (
                              <li key={index} className="mb-1">{person}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 font-medium text-slate-900 dark:text-white rounded-bl-xl">Description</td>
                      {stylesToCompare.map((style, index) => (
                        <td 
                          key={style.id} 
                          className={`p-4 text-sm text-slate-700 dark:text-slate-300 ${
                            index === stylesToCompare.length - 1 ? 'rounded-br-xl' : ''
                          }`}
                        >
                          {style.description}
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Find your style quiz CTA */}
          <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8">
            <div className="max-w-3xl mx-auto text-center">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Not Sure Which Style Is Right For You?
              </h3>
              <p className="text-slate-700 dark:text-slate-300 mb-6">
                Take our comprehensive yoga style quiz to discover which practice aligns best with your goals, preferences, and physical condition.
              </p>
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300">
                Take the Yoga Style Quiz
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YogaStyleComparison;