import React, { useState, useRef, useEffect } from 'react';

interface ScheduleItem {
  time: string;
  activity: string;
  description: string;
  icon: string;
  highlight?: boolean;
}

interface DaySchedule {
  id: string;
  name: string;
  schedule: ScheduleItem[];
}

const DailySchedule: React.FC = () => {
  const [activeDay, setActiveDay] = useState<string>('weekday');
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  const scheduleData: DaySchedule[] = [
    {
      id: 'weekday',
      name: 'Weekday Schedule',
      schedule: [
        {
          time: '05:30 - 06:00',
          activity: 'Wake Up Bell',
          description: 'Start your day with the traditional wake-up bell, signaling the beginning of your yogic journey.',
          icon: '🔔'
        },
        {
          time: '06:00 - 07:30',
          activity: 'Hatha Yoga',
          description: 'Morning practice focusing on asanas and breathing techniques to awaken the body and mind.',
          icon: '🧘‍♀️',
          highlight: true
        },
        {
          time: '07:30 - 08:30',
          activity: 'Pranayama',
          description: 'Guided breathing exercises to increase vital energy and prepare for meditation.',
          icon: '💨'
        },
        {
          time: '08:30 - 09:30',
          activity: 'Breakfast',
          description: 'Nutritious vegetarian breakfast to fuel your practice.',
          icon: '🍎'
        },
        {
          time: '10:00 - 11:30',
          activity: 'Yoga Philosophy',
          description: 'Lectures on yoga sutras, ancient texts, and the philosophy behind the practice.',
          icon: '📚',
          highlight: true
        },
        {
          time: '11:30 - 13:00',
          activity: 'Alignment & Adjustment',
          description: 'Detailed instruction on proper alignment and adjustments in asanas.',
          icon: '⚖️'
        },
        {
          time: '13:00 - 14:00',
          activity: 'Lunch',
          description: 'Wholesome vegetarian lunch prepared with local organic ingredients.',
          icon: '🥗'
        },
        {
          time: '14:00 - 16:00',
          activity: 'Rest & Self-Study',
          description: 'Time for personal reflection, journaling, or studying course materials.',
          icon: '📝'
        },
        {
          time: '16:00 - 17:30',
          activity: 'Ashtanga Yoga',
          description: 'Dynamic afternoon practice focusing on the Ashtanga primary series.',
          icon: '🔄',
          highlight: true
        },
        {
          time: '17:30 - 18:30',
          activity: 'Meditation',
          description: 'Guided meditation session to calm the mind and connect with inner peace.',
          icon: '✨'
        },
        {
          time: '18:30 - 19:30',
          activity: 'Dinner',
          description: 'Light vegetarian dinner to complete your day of practice.',
          icon: '🍲'
        },
        {
          time: '20:00 - 21:00',
          activity: 'Kirtan/Satsang',
          description: 'Evening gathering for chanting, singing, and spiritual discourse.',
          icon: '🎵'
        },
        {
          time: '21:30',
          activity: 'Lights Out',
          description: 'Rest for the night to prepare for the next day of practice.',
          icon: '🌙'
        }
      ]
    },
    {
      id: 'weekend',
      name: 'Weekend Schedule',
      schedule: [
        {
          time: '06:00 - 07:00',
          activity: 'Wake Up & Tea',
          description: 'A gentle start to the weekend with herbal tea and self-reflection.',
          icon: '🍵'
        },
        {
          time: '07:00 - 08:30',
          activity: 'Gentle Yoga',
          description: 'A more relaxed yoga session focusing on restoration and deep stretching.',
          icon: '🧘‍♂️'
        },
        {
          time: '08:30 - 09:30',
          activity: 'Breakfast',
          description: 'Nutritious vegetarian breakfast with community sharing.',
          icon: '🍎'
        },
        {
          time: '10:00 - 12:00',
          activity: 'Nature Walk',
          description: 'Guided walk along the Ganges or in the surrounding hills of Rishikesh.',
          icon: '🌿',
          highlight: true
        },
        {
          time: '12:00 - 13:30',
          activity: 'Lunch',
          description: 'Special weekend lunch featuring local delicacies.',
          icon: '🥗'
        },
        {
          time: '13:30 - 15:30',
          activity: 'Free Time',
          description: 'Explore Rishikesh, visit local markets, or simply relax.',
          icon: '🛍️'
        },
        {
          time: '15:30 - 17:00',
          activity: 'Workshop',
          description: 'Special weekend workshop on varying topics like Ayurveda, massage, or cooking.',
          icon: '🧪',
          highlight: true
        },
        {
          time: '17:00 - 18:30',
          activity: 'Sunset Meditation',
          description: 'Meditation by the Ganges as the sun sets over the Himalayas.',
          icon: '🌅',
          highlight: true
        },
        {
          time: '18:30 - 19:30',
          activity: 'Dinner',
          description: 'Community dinner with all students and teachers.',
          icon: '🍲'
        },
        {
          time: '20:00 - 21:30',
          activity: 'Cultural Program',
          description: 'Music, dance, or movie night celebrating yogic and Indian culture.',
          icon: '🎭'
        },
        {
          time: '22:00',
          activity: 'Lights Out',
          description: 'Rest for the night to prepare for Sunday activities.',
          icon: '🌙'
        }
      ]
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  const activeSchedule = scheduleData.find(day => day.id === activeDay)?.schedule || [];

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        {/* Header with day selection */}
        <div className="bg-amber-400 dark:bg-indigo-600 p-6 text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-slate-900 dark:text-white mb-4">
            Daily Schedule at Shanti Yog Peeth
          </h2>
          <p className="text-slate-800 dark:text-slate-100 mb-6 max-w-2xl mx-auto">
            Our carefully designed schedule balances physical practice, philosophical study, and personal time for a complete yogic experience.
          </p>
          
          <div className="flex justify-center space-x-4">
            {scheduleData.map(day => (
              <button
                key={day.id}
                onClick={() => setActiveDay(day.id)}
                className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                  activeDay === day.id
                    ? 'bg-white text-amber-500 dark:text-indigo-600 shadow-md'
                    : 'bg-amber-500/20 dark:bg-indigo-500/20 text-white hover:bg-amber-500/30 dark:hover:bg-indigo-500/30'
                }`}
              >
                {day.name}
              </button>
            ))}
          </div>
        </div>
        
        {/* Schedule timeline */}
        <div className="p-6 md:p-8">
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-amber-200 dark:bg-slate-600"></div>
            
            {/* Schedule items */}
            <div className="space-y-6">
              {activeSchedule.map((item, index) => (
                <div 
                  key={index}
                  className={`relative pl-20 ${
                    item.highlight 
                      ? 'bg-amber-50 dark:bg-slate-700/50 rounded-lg p-4 border-l-4 border-amber-400 dark:border-indigo-500'
                      : ''
                  }`}
                >
                  {/* Time circle */}
                  <div className="absolute left-6 top-1/2 -translate-y-1/2 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white dark:bg-slate-700 border-4 border-amber-400 dark:border-indigo-500 flex items-center justify-center z-10">
                    <span className="text-lg">{item.icon}</span>
                  </div>
                  
                  <div>
                    <div className="flex flex-wrap items-baseline gap-2 mb-1">
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {item.activity}
                      </h3>
                      <span className="text-sm font-medium text-amber-500 dark:text-amber-400">
                        {item.time}
                      </span>
                    </div>
                    <p className="text-slate-600 dark:text-slate-300 text-sm">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Footer note */}
        <div className="bg-slate-50 dark:bg-slate-700/30 p-4 text-center text-sm text-slate-500 dark:text-slate-400">
          <p>Schedule may vary based on season and special events. All activities are optional but encouraged for the full experience.</p>
        </div>
      </div>
    </div>
  );
};

export default DailySchedule;
