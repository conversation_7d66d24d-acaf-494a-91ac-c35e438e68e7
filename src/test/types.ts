export interface TargetAudienceItem {
  id: string;
  title: string;
  description: string;
  benefits: string[];
  programs: string[];
  testimonial: {
    text: string;
    author: string;
    image: string;
  };
}

export interface ResultItem {
  id: string;
  title: string;
  description: string;
  icon?: string;
  stats?: {
    value: string;
    label: string;
  }[];
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface TestimonialItem {
  id: number;
  name: string;
  location: string;
  quote: string;
  image: string;
  rating: number;
}

export interface CourseItem {
  id: string;
  title: string;
  duration: string;
  level: string;
  price: number;
  image: string;
  featured?: boolean;
  description: string;
}
