import React, { useState, useRef, useEffect } from 'react';

interface Testimonial {
  id: string;
  name: string;
  location: string;
  image: string;
  text: string;
  programType: 'teacher-training' | 'retreat' | 'workshop' | 'regular-class';
  rating: 1 | 2 | 3 | 4 | 5;
}

const TestimonialWall: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Program types for filtering
  const programTypes = [
    { id: 'all', name: 'All Programs' },
    { id: 'teacher-training', name: 'Teacher Training' },
    { id: 'retreat', name: 'Retreats' },
    { id: 'workshop', name: 'Workshops' },
    { id: 'regular-class', name: 'Regular Classes' },
  ];

  // Sample testimonials data
  const testimonials: Testimonial[] = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      location: 'Delhi, India',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=PS',
      text: 'The 200-hour teacher training at Shanti Yog Peeth transformed my practice and my life. The instructors were incredibly knowledgeable and supportive throughout the journey. I left with not only a certification but a deeper connection to yoga philosophy and myself.',
      programType: 'teacher-training',
      rating: 5
    },
    {
      id: '2',
      name: 'Michael Johnson',
      location: 'London, UK',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=MJ',
      text: 'The Himalayan retreat was a life-changing experience. Waking up to mountain views and practicing yoga in such a serene environment helped me disconnect from the chaos of city life and reconnect with my inner peace. The vegetarian food was also incredible!',
      programType: 'retreat',
      rating: 5
    },
    {
      id: '3',
      name: 'Sophia Chen',
      location: 'Singapore',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=SC',
      text: "I attended the weekend Ayurveda workshop and was amazed by how much practical knowledge I gained in just two days. The instructor made complex concepts accessible, and I've been able to incorporate many of the teachings into my daily routine for better health.",
      programType: 'workshop',
      rating: 4
    },
    {
      id: '4',
      name: 'David Miller',
      location: 'Sydney, Australia',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=DM',
      text: "I've been attending the regular morning classes for six months now, and the improvement in my flexibility and overall wellbeing is remarkable. The teachers are attentive and provide modifications for all levels. This studio has become my second home.",
      programType: 'regular-class',
      rating: 5
    },
    {
      id: '5',
      name: 'Anika Patel',
      location: 'Mumbai, India',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=AP',
      text: 'The 300-hour advanced teacher training exceeded my expectations. The curriculum was comprehensive, covering everything from advanced asanas to the subtleties of pranayama and meditation. I feel fully prepared to share these teachings with my own students.',
      programType: 'teacher-training',
      rating: 5
    },
    {
      id: '6',
      name: 'Thomas Weber',
      location: 'Berlin, Germany',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=TW',
      text: "The meditation retreat came at the perfect time in my life. The guidance provided helped me establish a consistent meditation practice that I've maintained for months now. The peaceful environment and supportive community made all the difference.",
      programType: 'retreat',
      rating: 4
    },
    {
      id: '7',
      name: 'Emma Wilson',
      location: 'Toronto, Canada',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=EW',
      text: 'The prenatal yoga workshop gave me tools and confidence to maintain my practice throughout my pregnancy. The instructor was knowledgeable about the specific needs of expectant mothers and created a nurturing space for all of us.',
      programType: 'workshop',
      rating: 5
    },
    {
      id: '8',
      name: 'Raj Mehta',
      location: 'Bangalore, India',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=RM',
      text: "I've tried many yoga studios, but the evening classes here are special. The sequencing is thoughtful, the pace is perfect, and the atmosphere is both challenging and supportive. It's the highlight of my day.",
      programType: 'regular-class',
      rating: 4
    },
    {
      id: '9',
      name: 'Sarah Thompson',
      location: 'New York, USA',
      image: 'https://placehold.co/300x300/e2e8f0/1e293b?text=ST',
      text: 'The 500-hour teacher training was intense but incredibly rewarding. The depth of knowledge shared by the instructors was impressive, and the community of teachers I met has become a valuable support network in my teaching career.',
      programType: 'teacher-training',
      rating: 5
    }
  ];

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Filter testimonials based on program type and search query
  const filteredTestimonials = testimonials.filter(testimonial => {
    const matchesFilter = activeFilter === 'all' || testimonial.programType === activeFilter;
    const matchesSearch = testimonial.text.toLowerCase().includes(searchQuery.toLowerCase()) || 
                         testimonial.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  // Render star ratings
  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, index) => (
          <svg 
            key={index} 
            className={`w-5 h-5 ${index < rating ? 'text-amber-400' : 'text-slate-300 dark:text-slate-600'}`} 
            fill="currentColor" 
            viewBox="0 0 20 20" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Student <span className="text-amber-500 dark:text-amber-400">Testimonials</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Read what our students have to say about their transformative experiences at Shanti Yog Peeth.
            </p>
          </div>

          {/* Search and filter section */}
          <div className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {/* Search input */}
              <div className="md:col-span-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search testimonials..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-3 pl-10 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-400 dark:focus:ring-amber-500"
                  />
                  <svg className="w-5 h-5 text-slate-400 absolute left-3 top-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
              </div>

              {/* Reset filters button */}
              <div className="md:col-span-1 flex items-center">
                <button
                  onClick={() => {
                    setActiveFilter('all');
                    setSearchQuery('');
                  }}
                  className="px-4 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300 flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Reset Filters
                </button>
              </div>
            </div>

            {/* Program type tabs */}
            <div className="flex flex-wrap gap-2 mb-6">
              {programTypes.map(type => (
                <button
                  key={type.id}
                  onClick={() => setActiveFilter(type.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    activeFilter === type.id
                      ? 'bg-amber-400 dark:bg-amber-600 text-slate-900 dark:text-white shadow-md'
                      : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                  }`}
                >
                  {type.name}
                </button>
              ))}
            </div>
          </div>

          {/* Testimonials masonry grid */}
          {filteredTestimonials.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTestimonials.map(testimonial => (
                <div
                  key={testimonial.id}
                  className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 flex flex-col h-full transform hover:scale-[1.02]"
                >
                  <div className="p-6">
                    {/* Quote icon */}
                    <svg className="w-10 h-10 text-amber-400/30 dark:text-amber-400/20 mb-4" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                    </svg>
                    
                    {/* Testimonial text */}
                    <p className="text-slate-700 dark:text-slate-300 mb-6 italic">"{testimonial.text}"</p>
                    
                    {/* Rating */}
                    <div className="mb-4">
                      {renderStars(testimonial.rating)}
                    </div>
                    
                    {/* Author info */}
                    <div className="flex items-center mt-auto">
                      <img 
                        src={testimonial.image} 
                        alt={testimonial.name} 
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                      <div>
                        <h4 className="font-semibold text-slate-900 dark:text-white">{testimonial.name}</h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400">{testimonial.location}</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Program type badge */}
                  <div className="px-6 py-3 bg-slate-100 dark:bg-slate-600/50 mt-auto">
                    <span className="text-xs font-medium text-amber-600 dark:text-amber-400">
                      {programTypes.find(type => type.id === testimonial.programType)?.name}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-slate-50 dark:bg-slate-700 rounded-xl">
              <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">No testimonials found</h3>
              <p className="text-slate-700 dark:text-slate-300">Try adjusting your filters or search query.</p>
            </div>
          )}
          
          {/* Call to action */}
          <div className="mt-12 text-center">
            <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8 max-w-3xl mx-auto">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">Share Your Experience</h3>
              <p className="text-slate-700 dark:text-slate-300 mb-6">
                Have you studied with us? We'd love to hear about your experience at Shanti Yog Peeth.
              </p>
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300">
                Submit Your Testimonial
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialWall;