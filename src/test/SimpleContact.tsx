import React, { useState } from 'react';
import { Mail, Phone, MapPin } from 'lucide-react';
import WhatsAppIcon from '../components/icons/WhatsAppIcon';
import InstagramIcon from '../components/icons/InstagramIcon';
import FacebookIcon from '../components/icons/FacebookIcon';

const SimpleContact: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const [formStatus, setFormStatus] = useState<{
    submitted: boolean;
    success: boolean;
    message: string;
  } | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Simulate form submission
    setFormStatus({ submitted: true, success: true, message: 'Thank you for your message! We will get back to you soon.' });

    // Reset form after successful submission
    setFormData({
      name: '',
      email: '',
      message: ''
    });

    // Reset status after 5 seconds
    setTimeout(() => {
      setFormStatus(null);
    }, 5000);
  };

  return (
    <section id="contact" className="py-20 md:py-32 bg-amber-50/50 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-amber-100 rounded-bl-full opacity-50"></div>
      <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-amber-100 rounded-tr-full opacity-50"></div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-4">
            <div className="h-px bg-amber-400 w-16 mr-4"></div>
            <span className="text-amber-500 uppercase tracking-wider text-sm font-semibold">Get in Touch</span>
            <div className="h-px bg-amber-400 w-16 ml-4"></div>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900">
            Begin Your <span className="text-amber-500">Yoga Journey</span>
          </h2>

          <p className="text-slate-700 max-w-3xl mx-auto">
            Have questions about our classes, retreats, or teacher training programs?
            Reach out to us and start your transformative journey today.
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-12 max-w-5xl mx-auto">
          {/* Social Media and Contact Info */}
          <div className="lg:w-1/2">
            <div className="bg-white rounded-xl p-8 shadow-lg h-full">
              <h3 className="text-xl font-bold text-slate-900 mb-6">Connect With Us</h3>

              <div className="space-y-8">
                {/* Social Media */}
                <div>
                  <h4 className="font-medium text-slate-900 mb-4">Follow Us</h4>
                  <div className="flex space-x-4">
                    <a
                      href="https://www.instagram.com/aryayogpeeth/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300"
                      aria-label="Instagram"
                    >
                      <InstagramIcon size={24} />
                    </a>
                    <a
                      href="https://www.facebook.com/aryayogpeeth/"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300"
                      aria-label="Facebook"
                    >
                      <FacebookIcon size={24} />
                    </a>
                    <a
                      href="https://wa.me/918762101031"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white hover:shadow-lg transition-all duration-300"
                      aria-label="WhatsApp"
                    >
                      <WhatsAppIcon size={24} />
                    </a>
                  </div>
                  <p className="mt-4 text-slate-600 text-sm">
                    Follow us for daily inspiration, updates, and to connect with our community.
                  </p>
                </div>

                {/* WhatsApp Banner */}
                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <WhatsAppIcon className="w-10 h-10 text-green-500 mr-4" />
                    <div>
                      <h4 className="font-medium text-slate-900">Have Questions? Ask on WhatsApp</h4>
                      <p className="text-slate-600 text-sm">Get quick answers to your questions</p>
                    </div>
                  </div>
                  <a
                    href="https://wa.me/918762101031"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mt-4 block w-full py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg font-medium text-center transition-colors duration-300"
                  >
                    Chat on WhatsApp
                  </a>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <div className="flex items-start">
                    <Mail className="w-5 h-5 text-amber-500 mt-1 mr-3" />
                    <div>
                      <h4 className="font-medium text-slate-900">Email</h4>
                      <a href="mailto:<EMAIL>" className="text-slate-600 hover:text-amber-500 transition-colors">
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Phone className="w-5 h-5 text-amber-500 mt-1 mr-3" />
                    <div>
                      <h4 className="font-medium text-slate-900">Phone</h4>
                      <a href="tel:+918762101031" className="text-slate-600 hover:text-amber-500 transition-colors">
                        +91 87621 01031
                      </a>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <MapPin className="w-5 h-5 text-amber-500 mt-1 mr-3" />
                    <div>
                      <h4 className="font-medium text-slate-900">Location</h4>
                      <p className="text-slate-600">
                        Laxman Jhula, Rishikesh<br />
                        Uttarakhand, India
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Simple Contact Form */}
          <div className="lg:w-1/2">
            <div className="bg-white rounded-xl p-8 shadow-lg h-full">
              <h3 className="text-xl font-bold text-slate-900 mb-6">Send a Quick Message</h3>

              {formStatus && (
                <div className={`p-4 mb-6 rounded-lg ${formStatus.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {formStatus.message}
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-slate-700 mb-2">
                    Your Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent bg-white text-slate-900"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-slate-700 mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent bg-white text-slate-900"
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-2">
                    Your Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={4}
                    className="w-full px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent bg-white text-slate-900"
                  ></textarea>
                </div>

                <div className="flex items-center">
                  <input
                    id="privacy"
                    name="privacy"
                    type="checkbox"
                    required
                    className="h-4 w-4 text-amber-500 focus:ring-amber-400 border-slate-300 rounded"
                  />
                  <label htmlFor="privacy" className="ml-2 block text-sm text-slate-600">
                    I agree to the <a href="/privacy" className="text-amber-500 hover:underline">privacy policy</a>
                  </label>
                </div>

                <button
                  type="submit"
                  className="w-full py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-lg font-medium transition-colors duration-300"
                >
                  Send Message
                </button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-sm text-slate-500">
                  Or ask your questions directly on{' '}
                  <a
                    href="https://www.instagram.com/aryayogpeeth/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-pink-500 font-medium hover:underline"
                  >
                    Instagram
                  </a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SimpleContact;
