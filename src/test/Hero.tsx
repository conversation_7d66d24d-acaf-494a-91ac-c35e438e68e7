import React, { useState, useEffect } from 'react';

const Hero: React.FC = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    // Set loaded state after a small delay for entrance animations
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 100);

    // Handle scroll for fade effects
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Calculate opacity based on scroll position
  const overlayOpacity = Math.min(0.7 + (scrollY / 1000), 0.9);
  const contentOpacity = Math.max(1 - (scrollY / 700), 0);
  const contentTransform = `translateY(${scrollY * 0.3}px)`;

  return (
    <section
      id="home"
      className="relative min-h-[100vh] md:min-h-screen flex items-center justify-center overflow-hidden bg-cover bg-center"
      style={{
        backgroundImage: 'url("/images/hero.webp")',
        backgroundPosition: 'center center'
      }}
    >
      {/* Enhanced overlay with scroll-based opacity */}
      <div
        className="absolute inset-0 bg-gradient-to-b from-amber-900/40 via-amber-800/30 to-slate-900/70"
        style={{
          opacity: overlayOpacity,
          transition: 'opacity 0.3s ease-out'
        }}
      ></div>

      {/* Improved animated elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-20 -left-20 w-96 h-96 bg-amber-400/20 rounded-full blur-3xl animate-blob"></div>
        <div className="absolute top-1/3 -right-20 w-80 h-80 bg-amber-500/20 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-20 left-1/3 w-72 h-72 bg-amber-300/20 rounded-full blur-3xl animate-blob animation-delay-4000"></div>

        {/* Additional subtle light elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-white/5 rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-amber-200/10 rounded-full blur-2xl"></div>
      </div>

      {/* Content container with improved animations - positioned lower on mobile */}
      <div
        className="container mx-auto px-4 md:px-6 relative z-10 text-center pt-20 md:pt-0 flex flex-col justify-center min-h-[80vh]"
        style={{
          transform: contentTransform,
          opacity: contentOpacity,
          transition: 'transform 0.3s ease-out, opacity 0.3s ease-out'
        }}
      >
        {/* Animated entrance for heading */}
        <h1
          className={`text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 tracking-tight leading-tight transition-all duration-1000 transform ${
            isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}
        >
          Welcome to <span className="text-amber-400 relative inline-block">
            Arya Yog Peeth
            <span className="absolute bottom-0 left-0 w-full h-1 bg-amber-400/30"></span>
          </span>
        </h1>

        {/* Animated entrance for paragraph */}
        <p
          className={`text-xl md:text-2xl text-white/90 max-w-3xl mx-auto mb-10 transition-all duration-1000 delay-300 transform ${
            isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}
        >
          Experience authentic yoga in the heart of Rishikesh with Yogi Arya, an internationally recognized yoga master with over 15 years of teaching experience.
        </p>

        {/* Certification banner - between paragraph and buttons */}
        <div
          className={`flex justify-center mb-10 transition-all duration-1000 delay-400 transform ${
            isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}
        >
          <div className="inline-flex items-center gap-3 px-4 py-2 bg-white/10 backdrop-blur-md rounded-full border border-white/20 shadow-lg">
            <img
              src="https://placehold.co/120x120/e7a974/ffffff?text=YA"
              alt="Yoga Alliance"
              className="w-8 h-8 rounded-full object-cover"
            />
            <span className="text-white text-sm font-medium">Yoga Alliance Certified School</span>
            <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
          </div>
        </div>

        {/* Animated entrance for buttons */}
        <div
          className={`flex flex-col sm:flex-row justify-center gap-4 mb-12 transition-all duration-1000 delay-500 transform ${
            isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
          }`}
        >
          <a
            href="#about-arya"
            className="group px-8 py-3 bg-amber-400 hover:bg-amber-500 text-slate-900 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg relative overflow-hidden"
          >
            <span className="relative z-10">Discover My Journey</span>
            <span className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500"></span>
          </a>
          <a
            href="#who-is-it-for"
            className="group px-8 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white border border-white/30 rounded-full font-medium transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
          >
            <span className="relative z-10">Is This For You?</span>
            <span className="absolute inset-0 bg-white/10 transform scale-x-0 group-hover:scale-x-100 origin-left transition-transform duration-500"></span>
          </a>
        </div>

        {/* Empty space at the bottom for better spacing */}
        <div className="h-8"></div>
      </div>
    </section>
  );
};

export default Hero;
