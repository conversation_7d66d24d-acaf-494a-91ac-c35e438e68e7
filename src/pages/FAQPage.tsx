import React, { useState } from 'react';
import { ChevronDown, Plane, Book, HelpCircle, Globe, Calendar, MapPin } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
  category: string;
}

const faqs: FAQItem[] = [
  // General FAQs
  {
    question: "What is the best time to visit Rishikesh?",
    answer: "The best time to visit Rishikesh is from September to April when the weather is pleasant. Summers (May-June) can be hot, and monsoon season (July-August) brings heavy rainfall.",
    category: "general"
  },
  {
    question: "What is the language of instruction?",
    answer: "All our courses and programs are conducted in English. Our teachers are fluent in English and can provide clear instructions and guidance.",
    category: "general"
  },
  // Visa Information
  {
    question: "What type of visa do I need?",
    answer: "Most students require a Student Visa or Tourist Visa depending on the duration of their stay. For courses longer than 180 days, a Student Visa is mandatory.",
    category: "visa"
  },
  {
    question: "How long does the visa process take?",
    answer: "The visa processing time varies by country but typically takes 5-15 working days. We recommend applying at least 1 month before your intended travel date.",
    category: "visa"
  },
  // Travel Requirements
  {
    question: "What vaccinations are required?",
    answer: "While there are no mandatory vaccinations for India, we recommend consulting your healthcare provider about Hepatitis A, Typhoid, and routine vaccinations.",
    category: "travel"
  },
  {
    question: "How do I reach Rishikesh?",
    answer: "The nearest airport is Dehradun's Jolly Grant Airport (DED), about 20km from Rishikesh. We can arrange airport pickup for an additional fee.",
    category: "travel"
  },
  // Course Preparation
  {
    question: "What should I bring for the course?",
    answer: "Essential items include comfortable practice clothes, meditation cushion, notebook, water bottle, and any personal medications. A detailed packing list will be provided upon registration.",
    category: "course"
  },
  {
    question: "Do I need prior yoga experience?",
    answer: "The level of experience required varies by program. Our 200-hour TTC welcomes beginners, while advanced courses may require previous certification or practice.",
    category: "course"
  }
];

const categories = [
  { id: 'general', name: 'General Questions', icon: HelpCircle },
  { id: 'visa', name: 'Visa Information', icon: Globe },
  { id: 'travel', name: 'Travel Requirements', icon: Plane },
  { id: 'course', name: 'Course Preparation', icon: Book }
];

const FAQPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [expandedQuestions, setExpandedQuestions] = useState<string[]>([]);

  const toggleQuestion = (question: string) => {
    setExpandedQuestions(prev =>
      prev.includes(question)
        ? prev.filter(q => q !== question)
        : [...prev, question]
    );
  };

  const filteredFAQs = faqs.filter(faq => activeCategory === 'all' || faq.category === activeCategory);

  return (
    <div className="pt-18">
      {/* Hero Section */}
      <div className="bg-amber-400/10 dark:bg-indigo-900/20 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Help <span className="text-amber-500 dark:text-amber-400">Center</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Find answers to common questions about our programs, travel requirements, and preparation guidelines.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Category Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg sticky top-24">
              <h2 className="text-xl font-bold text-slate-900 dark:text-white mb-6">Categories</h2>
              <div className="space-y-2">
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center px-4 py-2 rounded-lg transition-colors duration-300 ${
                      activeCategory === category.id
                        ? 'bg-amber-400/10 text-amber-500 dark:bg-indigo-600/20 dark:text-amber-400'
                        : 'text-slate-600 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-700/50'
                    }`}
                  >
                    <category.icon className="w-5 h-5 mr-3" />
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* FAQ Content */}
          <div className="lg:w-3/4">
            {/* Pre-Travel Guide */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg mb-8">
              <div className="flex items-center mb-6">
                <Calendar className="w-6 h-6 text-amber-500 dark:text-amber-400 mr-3" />
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Pre-Travel Guide</h2>
              </div>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-medium text-slate-900 dark:text-white">Before You Arrive</h3>
                  <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                    <li>• Check visa requirements</li>
                    <li>• Book your flights</li>
                    <li>• Arrange accommodation</li>
                    <li>• Get travel insurance</li>
                  </ul>
                </div>
                <div className="space-y-4">
                  <h3 className="font-medium text-slate-900 dark:text-white">What to Pack</h3>
                  <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                    <li>• Comfortable clothing</li>
                    <li>• Practice wear</li>
                    <li>• Essential documents</li>
                    <li>• Basic medications</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* FAQ Questions */}
            <div className="space-y-4">
              {filteredFAQs.map((faq, index) => (
                <div
                  key={index}
                  className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden"
                >
                  <button
                    onClick={() => toggleQuestion(faq.question)}
                    className="w-full flex items-center justify-between p-6 text-left"
                  >
                    <span className="font-medium text-slate-900 dark:text-white">{faq.question}</span>
                    <ChevronDown
                      className={`w-5 h-5 text-slate-500 transition-transform duration-200 ${
                        expandedQuestions.includes(faq.question) ? 'rotate-180' : ''
                      }`}
                    />
                  </button>
                  {expandedQuestions.includes(faq.question) && (
                    <div className="px-6 pb-6">
                      <p className="text-slate-600 dark:text-slate-300">{faq.answer}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Travel Requirements Section */}
      <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-slate-900 dark:text-white">
              Travel <span className="text-amber-500 dark:text-amber-400">Requirements</span>
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Essential information to help you plan your journey to Rishikesh.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <Globe className="w-8 h-8 text-amber-500 dark:text-amber-400" />
                <h3 className="text-lg font-bold text-slate-900 dark:text-white ml-3">Visa Requirements</h3>
              </div>
              <ul className="space-y-3 text-slate-600 dark:text-slate-300">
                <li>• Valid passport (6 months)</li>
                <li>• Visa application form</li>
                <li>• Course registration proof</li>
                <li>• Passport photos</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <Plane className="w-8 h-8 text-amber-500 dark:text-amber-400" />
                <h3 className="text-lg font-bold text-slate-900 dark:text-white ml-3">Travel Documents</h3>
              </div>
              <ul className="space-y-3 text-slate-600 dark:text-slate-300">
                <li>• Return flight tickets</li>
                <li>• Travel insurance</li>
                <li>• Vaccination records</li>
                <li>• Emergency contacts</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <MapPin className="w-8 h-8 text-amber-500 dark:text-amber-400" />
                <h3 className="text-lg font-bold text-slate-900 dark:text-white ml-3">Local Information</h3>
              </div>
              <ul className="space-y-3 text-slate-600 dark:text-slate-300">
                <li>• Local transport options</li>
                <li>• Accommodation details</li>
                <li>• Weather conditions</li>
                <li>• Cultural guidelines</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FAQPage;