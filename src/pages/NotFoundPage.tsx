import { Link } from 'react-router-dom';
import ParticleBackground from '../components/ParticleBackground';

const NotFoundPage = () => {
  return (
    <div className="relative min-h-screen flex items-center justify-center px-4 bg-slate-50 dark:bg-slate-900 overflow-hidden">
      <ParticleBackground />
      <div className="relative z-10 text-center max-w-2xl mx-auto p-6">
        <h1 className="text-5xl md:text-6xl font-bold text-orange-600 mb-4">
          404 – You’ve Bent the URL Too Far!
        </h1>
        <div className="flex justify-center mb-6">
          <span className="text-7xl md:text-8xl">🧘‍♂️</span>
        </div>
        <p className="text-lg md:text-xl text-slate-600 dark:text-slate-300 mb-8">
          This page has reached Nirvana and is no longer here.
        </p>
        <Link
          to="/"
          className="inline-block px-6 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors duration-300"
        >
          Find Your Flow Again
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;
