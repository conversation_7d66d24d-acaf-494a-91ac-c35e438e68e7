import React from "react";

const PrivacyPolicyPage: React.FC = () => (
  <div className="max-w-3xl mx-auto py-16 px-4">
    <h1 className="text-3xl font-bold mb-6">Privacy Policy</h1>
    <p className="mb-4">
      This Privacy Policy describes how we collect, use, and protect your personal information when you use our website.
    </p>
    <h2 className="text-xl font-semibold mt-8 mb-2">Information We Collect</h2>
    <p className="mb-4">
      We may collect personal information such as your name, email address, and any other information you provide when you contact us or subscribe to our newsletter.
    </p>
    <h2 className="text-xl font-semibold mt-8 mb-2">How We Use Your Information</h2>
    <p className="mb-4">
      We use your information to provide and improve our services, communicate with you, and send you updates or promotional materials if you have subscribed to our newsletter.
    </p>
    <h2 className="text-xl font-semibold mt-8 mb-2">Data Security</h2>
    <p className="mb-4">
      We take reasonable measures to protect your personal information from unauthorized access, disclosure, or misuse.
    </p>
    <h2 className="text-xl font-semibold mt-8 mb-2">Third-Party Services</h2>
    <p className="mb-4">
      Our website may contain links to third-party sites. We are not responsible for the privacy practices or content of those sites.
    </p>
    <h2 className="text-xl font-semibold mt-8 mb-2">Your Rights</h2>
    <p className="mb-4">
      You may contact us to request access to, correction of, or deletion of your personal information.
    </p>
    <h2 className="text-xl font-semibold mt-8 mb-2">Changes to This Policy</h2>
    <p className="mb-4">
      We may update this Privacy Policy from time to time. Changes will be posted on this page.
    </p>
    <p className="mt-8 text-slate-500">
      If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.
    </p>
  </div>
);

export default PrivacyPolicyPage;
