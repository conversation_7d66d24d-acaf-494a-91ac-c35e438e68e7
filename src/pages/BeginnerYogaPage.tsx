import React, { useState } from 'react';
import { <PERSON>, Star, Clock, Zap, Brain, Activity, User, ThumbsUp, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ArrowR<PERSON>, Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const beginnerClassSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address')
});

type BeginnerClassFormData = z.infer<typeof beginnerClassSchema>;

const BeginnerYogaPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* 1. Hero Section */}
      <div className="bg-gradient-to-b from-amber-400/10 to-transparent dark:from-indigo-900/20 dark:to-transparent py-20 md:py-28">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-6xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Curious About <span className="text-amber-500 dark:text-amber-400">Yoga</span>?
            <br />A Beginner's Guide
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto mb-8">
            Discover what yoga is, its benefits, and how you can easily start your practice with us, right here in Rishikesh or online.
          </p>
          <div className="flex justify-center relative mt-12">
            {/* Hero Image - Large, Creative Placement */}
            <div className="relative w-full max-w-5xl">
              {/* Decorative blurred background shape for depth */}
              <div
                className="absolute -top-8 -left-8 w-[110%] h-[110%] rounded-3xl z-0"
                style={{
                  background: 'radial-gradient(circle at 60% 40%, #fde68a 0%, #fbbf24 60%, transparent 100%)',
                  filter: 'blur(40px)',
                  opacity: 0.35,
                }}
                aria-hidden="true"
              />
              <img
                src="/images/banner.jpeg"
                alt="Welcoming yoga space in Rishikesh"
                className="relative z-10 w-full h-auto rounded-3xl shadow-2xl object-cover border-4 border-white dark:border-slate-900 transition-transform duration-1000 ease-in-out hover:scale-105"
                style={{ minHeight: 320, maxHeight: 520, boxShadow: '0 8px 48px 0 rgba(251,191,36,0.18)' }}
              />
              {/* Optional: floating effect */}
              <div
                className="absolute -bottom-6 right-8 bg-white/70 dark:bg-slate-900/70 px-6 py-3 rounded-xl shadow-lg z-20"
                style={{
                  backdropFilter: 'blur(6px)',
                  boxShadow: '0 2px 16px 0 rgba(251,191,36,0.10)',
                }}
              >
                <span className="text-lg font-semibold text-amber-600 dark:text-amber-300 tracking-wide">
                  Start your journey with us
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 md:px-6 py-16">
        {/* 2. What IS Yoga? */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            So, What Exactly is <span className="text-amber-500 dark:text-amber-400">Yoga</span>?
          </h2>
          <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-8 items-center">
              <div className="md:w-1/2">
                <p className="text-slate-700 dark:text-slate-300 mb-4">
                  Yoga is an ancient practice that originated in India thousands of years ago. At its core, yoga is about creating harmony between your mind, body, and breath.
                </p>
                <p className="text-slate-700 dark:text-slate-300 mb-4">
                  Think of yoga as a moving meditation—where gentle physical movements (asanas) are combined with conscious breathing (pranayama) to help you become more present and aware.
                </p>
                <p className="text-slate-700 dark:text-slate-300">
                  While many people know yoga for its physical postures, it's much more than that. It's a holistic practice that can help you find balance, peace, and well-being in your life.
                </p>
              </div>
              <div className="md:w-1/2">
                <img 
                  src="https://placehold.co/600x400/e2e8f0/1e293b?text=Peaceful+Meditation" 
                  alt="Person peacefully meditating" 
                  className="w-full h-auto rounded-lg shadow-md"
                />
              </div>
            </div>
          </div>
        </section>

        {/* 3. Why Try Yoga? */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            What Can <span className="text-amber-500 dark:text-amber-400">Yoga</span> Do For You?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Benefit Cards */}
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-amber-100 dark:hover:bg-indigo-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <Brain className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Reduce Stress & Anxiety</h3>
              <p className="text-slate-700 dark:text-slate-300">Yoga's combination of movement and mindfulness helps calm the nervous system and reduce stress hormones.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-sky-100 dark:hover:bg-sky-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <Activity className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Improve Flexibility & Balance</h3>
              <p className="text-slate-700 dark:text-slate-300">Regular practice gradually increases your range of motion and improves both physical and mental balance.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-purple-100 dark:hover:bg-purple-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <Zap className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Build Strength</h3>
              <p className="text-slate-700 dark:text-slate-300">Yoga uses your body weight to gently build strength in a balanced way, without strain or injury.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-pink-100 dark:hover:bg-pink-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <Clock className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Promote Better Sleep</h3>
              <p className="text-slate-700 dark:text-slate-300">The relaxation techniques in yoga can help improve sleep quality and combat insomnia.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-lime-100 dark:hover:bg-emerald-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <Star className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Increase Mindfulness & Focus</h3>
              <p className="text-slate-700 dark:text-slate-300">Yoga teaches you to be present, helping improve concentration and mental clarity in daily life.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-rose-100 dark:hover:bg-rose-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <Heart className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Boost Energy Levels</h3>
              <p className="text-slate-700 dark:text-slate-300">Certain yoga practices can invigorate the body and mind, leaving you feeling energized and refreshed.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-teal-100 dark:hover:bg-teal-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <User className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Connect With Yourself</h3>
              <p className="text-slate-700 dark:text-slate-300">Yoga creates space for self-reflection and inner connection, helping you understand yourself better.</p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow hover:bg-green-100 dark:hover:bg-yellow-900/40">
              <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
                <ThumbsUp className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Improve Overall Health</h3>
              <p className="text-slate-700 dark:text-slate-300">Research shows yoga can help manage chronic conditions, improve immunity, and support holistic health.</p>
            </div>
          </div>
        </section>

        {/* 4. Busting Myths & Addressing Fears */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            Is Yoga Right For Me? <span className="text-amber-500 dark:text-amber-400">(Spoiler: Yes!)</span>
          </h2>
          
          <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-2">"I'm not flexible."</h3>
                    <p className="text-slate-700 dark:text-slate-300">Yoga builds flexibility; it's not a prerequisite. Everyone starts somewhere, and good instructors offer modifications for all levels.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-2">"I'm not spiritual/religious."</h3>
                    <p className="text-slate-700 dark:text-slate-300">Yoga can be practiced purely for physical and mental benefits. The spiritual elements are entirely optional and up to you.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-2">"It looks too difficult/I might get injured."</h3>
                    <p className="text-slate-700 dark:text-slate-300">Beginner classes focus on safe, foundational poses. Our teachers are trained to offer alternatives and ensure proper alignment.</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-2">"I don't have the 'yoga body'."</h3>
                    <p className="text-slate-700 dark:text-slate-300">Yoga is for every body type, size, age, and fitness level. The practice is about connecting with your unique body, not conforming to an image.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-2">"I don't have the right clothes/gear."</h3>
                    <p className="text-slate-700 dark:text-slate-300">Comfortable, stretchy clothing is all you need to start. We can provide mats and props for in-person classes, or suggest alternatives for home practice.</p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="w-6 h-6 text-amber-500 dark:text-amber-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white mb-2">"I don't have time."</h3>
                    <p className="text-slate-700 dark:text-slate-300">Even 10-15 minutes of yoga can be beneficial. Our offerings include various lengths, and the time invested in yoga often gives back more energy and productivity.</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 rounded-lg overflow-hidden">
              <img 
                src="https://placehold.co/1200x600/e2e8f0/1e293b?text=Diverse+Yoga+Class" 
                alt="Diverse group of people practicing yoga" 
                className="w-full h-auto"
              />
            </div>
          </div>
        </section>

        {/* 5. What to Expect in Your First Class */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            Your First Step <span className="text-amber-500 dark:text-amber-400">Onto the Mat</span>
          </h2>
          
          <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/2">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">What a Typical Beginner Session Includes:</h3>
                <ul className="space-y-3 text-slate-700 dark:text-slate-300">
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-amber-100 dark:bg-indigo-900/30 text-amber-500 dark:text-amber-400 mr-2 text-sm font-medium">1</span>
                    <span><strong>Introduction & Centering:</strong> A few minutes to arrive, settle in, and connect with your breath.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-amber-100 dark:bg-indigo-900/30 text-amber-500 dark:text-amber-400 mr-2 text-sm font-medium">2</span>
                    <span><strong>Gentle Warm-up:</strong> Simple movements to prepare the body, often done seated or standing.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-amber-100 dark:bg-indigo-900/30 text-amber-500 dark:text-amber-400 mr-2 text-sm font-medium">3</span>
                    <span><strong>Basic Poses:</strong> Fundamental postures with clear, detailed instruction and modifications offered.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-amber-100 dark:bg-indigo-900/30 text-amber-500 dark:text-amber-400 mr-2 text-sm font-medium">4</span>
                    <span><strong>Breathing Techniques:</strong> Simple pranayama (breathing exercises) to connect breath and movement.</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-amber-100 dark:bg-indigo-900/30 text-amber-500 dark:text-amber-400 mr-2 text-sm font-medium">5</span>
                    <span><strong>Final Relaxation:</strong> Savasana, a guided rest period lying down to integrate the practice.</span>
                  </li>
                </ul>
                
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mt-6 mb-4">Practical Tips:</h3>
                <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                  <li>• Wear comfortable, stretchy clothing you can move freely in</li>
                  <li>• Bring water and a small towel</li>
                  <li>• Arrive 10-15 minutes early for your first class</li>
                  <li>• Eat only lightly 1-2 hours before practice</li>
                  <li>• Let your teacher know about any injuries or conditions</li>
                  <li>• Remember: yoga is non-competitive—focus on your own experience</li>
                </ul>
              </div>
              
              <div className="md:w-1/2">
                <img 
                  src="https://placehold.co/600x800/e2e8f0/1e293b?text=Yoga+Class+Setup" 
                  alt="Yoga class setup with mats" 
                  className="w-full h-auto rounded-lg shadow-md"
                />
              </div>
            </div>
          </div>
        </section>

        {/* 6. How to Start Your Yoga Journey */}
        <section id="start-options" className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            Ready to Begin? <span className="text-amber-500 dark:text-amber-400">Choose Your Path</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Online Courses */}
            <div className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all">
              <div className="h-48 bg-slate-200 dark:bg-slate-700">
                <img 
                  src="https://placehold.co/500x300/e2e8f0/1e293b?text=Online+Yoga+Courses" 
                  alt="Online yoga courses" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3">Online Yoga Courses</h3>
                <p className="text-slate-700 dark:text-slate-300 mb-4">
                  Learn at your own pace from anywhere in the world. Our beginner-friendly online courses include detailed instruction, pose breakdowns, and guided practices.
                </p>
                <ul className="space-y-2 text-slate-600 dark:text-slate-400 mb-6">
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    Beginner Foundations (10 sessions)
                  </li>
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    Gentle Morning Yoga (15 minutes daily)
                  </li>
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    Yoga for Stress Relief
                  </li>
                </ul>
                <Link 
                  to="/courses" 
                  className="w-full block text-center py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors"
                >
                  Explore Online Courses
                </Link>
              </div>
            </div>

            {/* Retreats */}
            <div className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all">
              <div className="h-48 bg-slate-200 dark:bg-slate-700">
                <img 
                  src="https://placehold.co/500x300/e2e8f0/1e293b?text=Beginner+Retreats" 
                  alt="Yoga retreats for beginners" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3">Beginner-Friendly Retreats</h3>
                <p className="text-slate-700 dark:text-slate-300 mb-4">
                  Immerse yourself in the yoga lifestyle with our specially designed beginner retreats in beautiful Rishikesh, the yoga capital of the world.
                </p>
                <ul className="space-y-2 text-slate-600 dark:text-slate-400 mb-6">
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    7-Day Yoga Fundamentals Retreat
                  </li>
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    10-Day Mind-Body Wellness
                  </li>
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    Weekend Yoga Introduction
                  </li>
                </ul>
                <Link 
                  to="/retreats" 
                  className="w-full block text-center py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors"
                >
                  Browse Retreats
                </Link>
              </div>
            </div>

            {/* Teacher Trainings */}
            <div className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all">
              <div className="h-48 bg-slate-200 dark:bg-slate-700">
                <img 
                  src="https://placehold.co/500x300/e2e8f0/1e293b?text=Foundations+Training" 
                  alt="Yoga foundations training" 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3">Foundations Training</h3>
                <p className="text-slate-700 dark:text-slate-300 mb-4">
                  For those wanting a deeper immersion, our 100-hour Foundations program offers a comprehensive introduction to yoga without the full commitment of teacher training.
                </p>
                <ul className="space-y-2 text-slate-600 dark:text-slate-400 mb-6">
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    100-Hour Yoga Foundations
                  </li>
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    Ideal for complete beginners
                  </li>
                  <li className="flex items-center">
                    <span className="w-5 h-5 mr-2 text-amber-500 dark:text-amber-400">✓</span>
                    Can be applied toward future YTT
                  </li>
                </ul>
                <Link 
                  to="/courses" 
                  className="w-full block text-center py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors"
                >
                  Learn More
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* 7. Meet Our Welcoming Teachers */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            Guided by <span className="text-amber-500 dark:text-amber-400">Experienced & Caring</span> Teachers
          </h2>
          
          <div className="flex flex-col md:flex-row gap-8 max-w-4xl mx-auto">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg flex flex-col items-center text-center md:w-1/2">
              <div className="w-32 h-32 rounded-full overflow-hidden mb-4">
                <img 
                  src="https://placehold.co/300x300/e2e8f0/1e293b?text=Ananya" 
                  alt="Yoga teacher Ananya" 
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Ananya Sharma</h3>
              <p className="text-amber-500 dark:text-amber-400 mb-4">Beginner Yoga Specialist</p>
              <p className="text-slate-700 dark:text-slate-300 mb-4">
                With over 10 years of teaching experience, Ananya specializes in making yoga accessible to absolute beginners. Her gentle approach and clear instructions create a safe space for new practitioners.
              </p>
              <p className="italic text-slate-600 dark:text-slate-400">
                "Everyone has a perfect practice within them—my job is simply to help you discover it, one breath at a time."
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg flex flex-col items-center text-center md:w-1/2">
              <div className="w-32 h-32 rounded-full overflow-hidden mb-4">
                <img 
                  src="https://placehold.co/300x300/e2e8f0/1e293b?text=Raj" 
                  alt="Yoga teacher Raj" 
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Raj Patel</h3>
              <p className="text-amber-500 dark:text-amber-400 mb-4">Foundations & Alignment Expert</p>
              <p className="text-slate-700 dark:text-slate-300 mb-4">
                Raj's background in physical therapy informs his teaching style, with a focus on proper alignment and adaptation for different body types. His classes are perfect for anyone concerned about physical limitations.
              </p>
              <p className="italic text-slate-600 dark:text-slate-400">
                "Yoga isn't about touching your toes, it's about what you learn on the way down."
              </p>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <Link 
              to="/teachers" 
              className="inline-flex items-center text-amber-500 dark:text-amber-400 font-medium hover:underline"
            >
              Meet all our teachers
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </div>
        </section>

        {/* 8. Your Next Step */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            Take a Deep Breath and <span className="text-amber-500 dark:text-amber-400">Begin...</span>
          </h2>
          
          <div className="bg-amber-50 dark:bg-indigo-900/20 rounded-xl p-8 shadow-lg max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Ready to Start Your Yoga Journey?</h3>
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Choose the path that feels right for you. Whether it's exploring our online resources, joining a retreat, or starting with a beginner course—your yoga journey begins with a single step.
                </p>
                
                <div className="space-y-4">
                  <Link 
                    to="/courses" 
                    className="w-full block text-center py-3 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors"
                  >
                    Explore Beginner Online Courses
                  </Link>
                  
                  <Link 
                    to="/retreats" 
                    className="w-full block text-center py-3 px-4 bg-white hover:bg-slate-100 dark:bg-slate-800 dark:hover:bg-slate-700 text-slate-900 dark:text-white border border-amber-400 dark:border-indigo-600 rounded-lg font-medium transition-colors"
                  >
                    View Beginner-Friendly Retreats
                  </Link>
                  
                  <Link 
                    to="/contact" 
                    className="w-full block text-center py-3 px-4 bg-transparent hover:bg-white/50 dark:hover:bg-slate-800/50 text-slate-900 dark:text-white border border-slate-300 dark:border-slate-600 rounded-lg font-medium transition-colors"
                  >
                    Have Questions? Contact Us
                  </Link>
                </div>
              </div>
              
              <RegistrationForm />
            </div>
          </div>
        </section>

        {/* 9. Testimonials */}
        <section className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-10">
            Hear From <span className="text-amber-500 dark:text-amber-400">Beginners Like You</span>
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img 
                    src="https://placehold.co/100x100/e2e8f0/1e293b?text=Sarah" 
                    alt="Student Sarah" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-bold text-slate-900 dark:text-white">Sarah T.</h3>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Complete beginner, now 6 months in</p>
                </div>
              </div>
              <div className="mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Smile key={star} className="w-5 h-5 inline-block text-amber-400 dark:text-amber-400" />
                ))}
              </div>
              <p className="text-slate-700 dark:text-slate-300 italic">
                "I was so nervous about trying yoga—I couldn't touch my toes and was worried I'd be the only inflexible person in class! The beginner's retreat at Shanti made me feel completely at ease. The teachers were supportive and made everything accessible."
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img 
                    src="https://placehold.co/100x100/e2e8f0/1e293b?text=Miguel" 
                    alt="Student Miguel" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-bold text-slate-900 dark:text-white">Miguel R.</h3>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Started with online foundations</p>
                </div>
              </div>
              <div className="mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Smile key={star} className="w-5 h-5 inline-block text-amber-400 dark:text-amber-400" />
                ))}
              </div>
              <p className="text-slate-700 dark:text-slate-300 italic">
                "I never thought I'd enjoy yoga as a 45-year-old who's never been particularly athletic. The online beginner course let me learn at my own pace without feeling self-conscious. Now I practice almost every day and have noticed huge improvements in my back pain and stress levels."
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                  <img 
                    src="https://placehold.co/100x100/e2e8f0/1e293b?text=Aisha" 
                    alt="Student Aisha" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-bold text-slate-900 dark:text-white">Aisha M.</h3>
                  <p className="text-sm text-slate-600 dark:text-slate-400">Completed 100-Hour Foundations</p>
                </div>
              </div>
              <div className="mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Smile key={star} className="w-5 h-5 inline-block text-amber-400 dark:text-amber-400" />
                ))}
              </div>
              <p className="text-slate-700 dark:text-slate-300 italic">
                "Coming to Rishikesh for the 100-Hour Foundations program was the best decision I've made. As someone who had only tried yoga a few times from YouTube videos, I was worried it would be too advanced. But the course was perfectly paced, and I left with not just yoga skills but a whole new perspective on life."
              </p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

const RegistrationForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<BeginnerClassFormData>({
    resolver: zodResolver(beginnerClassSchema)
  });

  const onSubmit = async (data: BeginnerClassFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch('https://hub.rksh.in/mailer/mailer.php', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'beginner-class',
          ...data
        })
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit registration');
      }
      
      setSubmitStatus({
        type: 'success',
        message: 'Thank you for registering! We will contact you with class details soon.'
      });
      
      reset();
    } catch (error) {
      setSubmitStatus({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to submit registration. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus(null), 5000);
    }
  };

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl p-8 shadow-lg">
      {submitStatus && (
        <div className={`p-4 mb-6 rounded-lg ${
          submitStatus.type === 'success'
            ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-300'
            : 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-300'
        }`}>
          {submitStatus.message}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="name">
            Your Name
          </label>
          <input
            type="text"
            id="name"
            {...register('name')}
            className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
              errors.name ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
            }`}
            placeholder="Enter your name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name.message}</p>
          )}
        </div>
        
        <div>
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="email">
            Your Email
          </label>
          <input
            type="email"
            id="email"
            {...register('email')}
            className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
              errors.email ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
            }`}
            placeholder="Enter your email"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email.message}</p>
          )}
        </div>
        
        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full py-3 px-4 flex items-center justify-center rounded-lg font-medium transition-colors duration-300 ${
            isSubmitting
              ? 'bg-amber-300 dark:bg-indigo-500 cursor-not-allowed'
              : 'bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700'
          } text-slate-900 dark:text-white`}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Registering...
            </>
          ) : (
            'Register for Classes'
          )}
        </button>
      </form>
    </div>
  );
};

export default BeginnerYogaPage;
