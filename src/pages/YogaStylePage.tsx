import React from 'react';
import { useParams } from 'react-router-dom';

interface RelatedService {
  id: number;
  title: string;
  type: 'course' | 'retreat' | 'online';
  description: string;
  image: string;
  link: string;
}

interface YogaStyle {
  title: string;
  description: string;
  history: string;
  keyPrinciples: string[];
  benefits: string[];
  relatedServices: RelatedService[];
}

const yogaStyles: Record<string, YogaStyle> = {
  'hatha-yoga': {
    title: 'Hatha Yoga: Foundations of Balance',
    description: `Often considered the parent of many modern yoga styles practiced today, Hatha Yoga provides a fundamental pathway to harmonizing body, mind, and spirit. The term 'Hatha' itself is rich in meaning, derived from Sanskrit words 'Ha' (sun) and 'Tha' (moon), representing the integration of opposing forces – activity and receptivity, masculine and feminine, physical and mental. It is the 'forceful' or 'willful' yoga, implying a dedicated physical practice to achieve deeper states of awareness.

    A typical Hatha class involves a sequence of physical postures (asanas) performed with mindful attention to alignment and breath (pranayama). Unlike more dynamic styles, <PERSON><PERSON> often encourages holding poses for several breaths, allowing practitioners to explore stability, build strength, and cultivate inner stillness. This deliberate pacing makes it an accessible entry point for beginners while offering profound depth for experienced yogis seeking to refine their practice and connect with yoga's traditional roots. It serves as excellent preparation for deeper meditative states.`,
    history: `The term 'Hatha Yoga' finds early mentions in texts dating back possibly to the 1st century CE, but its systematic codification emerged much later. Early Tantric Buddhist and Hindu texts around the 11th century began outlining specific techniques. Key figures like Matsyendranath and his disciple Gorakhnath, associated with the Nath tradition (roughly 10th-11th centuries), are traditionally considered pioneers, emphasizing practices to control vital energy (prana) and awaken spiritual potential (kundalini).

    The most influential text that structured Hatha Yoga is the *Hatha Yoga Pradipika*, compiled by Swami Svatmarama in the 15th century. This text outlines core practices including asanas, pranayama, shatkarmas (purification techniques), mudras (seals), and bandhas (energy locks). Hatha Yoga evolved from being a primarily preparatory practice for Raja Yoga (the yoga of meditation) to a comprehensive system in itself. In the 20th century, teachers like T. Krishnamacharya and his students (including B.K.S. Iyengar and K. Pattabhi Jois) significantly shaped modern Hatha Yoga, leading to the diverse styles we see today, all branching from these foundational teachings.`,
    keyPrinciples: [
      'Asana (Stable & Comfortable Postures): Practicing physical postures with steadiness (Sthira) and ease (Sukham) to prepare the body and mind.',
      'Pranayama (Breath Regulation): Conscious control and expansion of breath to influence life force (prana) and calm the nervous system.',
      'Shatkarma (Purification Techniques): Six cleansing practices (like Neti, Nauli) to purify the internal body and prepare for deeper practices (optional in many modern classes).',
      'Mudra & Bandha (Seals & Locks): Subtle techniques to direct and contain vital energy within the body.',
      'Dhyana (Meditation) & Dharana (Concentration): Cultivating mental focus and moving towards meditative awareness, often supported by the physical practices.',
      'Mindful Diet (Mitahara): Emphasis on consuming moderate, wholesome food conducive to practice.',
      'Relaxation (Savasana): Conscious relaxation to integrate the benefits of the practice.'
    ],
    benefits: [
      'Increases overall body flexibility and range of motion.',
      'Builds muscular strength and endurance through sustained holds.',
      'Improves posture and spinal alignment.',
      'Calms the nervous system, reducing stress and anxiety.',
      'Enhances lung capacity and respiratory function through pranayama.',
      'Develops greater body awareness and proprioception.',
      'Improves concentration and mental focus.',
      'Promotes balance, both physically and mentally.',
      'Supports detoxification processes through twists and purification techniques.',
      'Provides a solid foundation for meditation and deeper spiritual exploration.'
    ],
    relatedServices: [
      {
        id: 1,
        title: 'Foundational Hatha Yoga Teacher Training (200-Hour)',
        type: 'course',
        description: 'Master the fundamentals of Hatha philosophy, asana, pranayama, and teaching skills in this comprehensive certification program.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Hatha+YTT+Foundations',
        link: '/courses/hatha-ytt-200'
      },
      {
        id: 2,
        title: 'Classical Hatha Immersion Retreat',
        type: 'retreat',
        description: 'A 10-day deep dive into traditional Hatha practices, including Shatkarmas, advanced pranayama, and silent meditation.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Classical+Hatha+Retreat',
        link: '/retreats/classical-hatha-immersion'
      },
      {
        id: 3,
        title: 'Online Hatha Yoga for Beginners Series',
        type: 'online',
        description: 'Start your yoga journey with clear guidance on basic Hatha postures, breathing, and alignment from home.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Online+Hatha+Beginners',
        link: '/courses/online-hatha-beginners'
      }
    ]
  },
  'ashtanga-yoga': {
    title: 'Ashtanga Yoga: The Eight-Limbed Path in Motion',
    description: `Ashtanga Vinyasa Yoga is a rigorous and highly structured style known for its set sequence of postures linked by breath-synchronized movement (vinyasa). Developed in the 20th century, this practice generates significant internal heat (tapas), aimed at purifying the body and mind. The consistent repetition of the sequences allows practitioners to cultivate deep focus, discipline, and a moving meditative state.

    There are six series in the Ashtanga system, though most practitioners focus on the Primary Series (Yoga Chikitsa - Yoga Therapy) and Intermediate Series (Nadi Shodhana - Nerve Cleansing). Classes are traditionally taught in two formats: 'Led' classes, where a teacher calls out the postures and breath counts, and 'Mysore' style, where students practice independently at their own pace, receiving individual adjustments and guidance from the teacher. This powerful practice demands dedication but rewards with profound strength, flexibility, and mental clarity.`,
    history: `The Ashtanga Vinyasa system was codified and popularized by Sri K. Pattabhi Jois (1915-2009) in Mysore, India. Jois studied extensively under his teacher, Sri T. Krishnamacharya, considered the "grandfather of modern yoga," starting in the 1920s. Krishnamacharya is said to have passed down the specific sequences and vinyasa system to Jois, possibly based on an ancient text called the *Yoga Korunta* (though the existence and contents of this text are debated).

    Pattabhi Jois began teaching this method formally around 1948 at his Ashtanga Yoga Research Institute (now the K. Pattabhi Jois Ashtanga Yoga Institute or KPJAYI) in Mysore. The practice gained significant international recognition from the 1970s onwards, as Western students traveled to India to study directly with Jois. His teachings emphasized the importance of daily practice and adherence to the traditional method, establishing Ashtanga as a major global yoga lineage.`,
    keyPrinciples: [
      'Tristhana (Three Places of Attention): Synchronizing Ujjayi Pranayama (Victorious Breath), Asana (Posture), and Drishti (Gazing Point).',
      'Vinyasa (Breath-Synchronized Movement): Linking each posture with specific inhalations and exhalations, creating continuous flow and internal heat.',
      'Set Sequences: Practicing prescribed series of postures in a specific order (Primary, Intermediate, Advanced A, B, C, D).',
      'Ujjayi Pranayama (Victorious Breath): A specific breathing technique with a slight constriction in the throat, generating heat and rhythmic focus.',
      'Bandhas (Energy Locks): Engaging Mula Bandha (Root Lock) and Uddiyana Bandha (Abdominal Lock) to control and direct internal energy (prana).',
      'Drishti (Gazing Point): Maintaining a specific point of focus for the eyes in each posture to cultivate concentration and internal awareness.',
      'Daily Practice (Abhyasa): Traditionally practiced six days a week, with rest days on Saturdays and during moon days (full and new moon).'
    ],
    benefits: [
      'Develops exceptional physical strength and stamina.',
      'Significantly increases flexibility and range of motion.',
      'Improves cardiovascular health due to its vigorous nature.',
      'Promotes detoxification through internal heat and sweating.',
      'Enhances mental concentration, focus, and discipline.',
      'Calms the nervous system through rhythmic breathing and movement.',
      'Builds self-awareness and introspection through consistent practice.',
      'Cultivates a sense of inner stability and groundedness.',
      'Increases energy levels and vitality.',
      'Can lead to profound mental and emotional purification.'
    ],
    relatedServices: [
      {
        id: 1,
        title: 'Ashtanga Primary Series Intensive Course',
        type: 'course',
        description: 'Deepen your understanding and practice of the full Ashtanga Primary Series with detailed instruction on alignment, vinyasa, and philosophy.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Ashtanga+Primary+Intensive',
        link: '/courses/ashtanga-primary-intensive'
      },
      {
        id: 2,
        title: 'Authentic Mysore Style Ashtanga Retreat',
        type: 'retreat',
        description: 'Immerse yourself in daily Mysore-style practice, chanting, and philosophy talks in a supportive, traditional environment for 14 days.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Mysore+Practice+Retreat',
        link: '/retreats/authentic-mysore-ashtanga'
      },
      {
        id: 3,
        title: 'Online Introduction to Ashtanga Vinyasa',
        type: 'online',
        description: 'Learn the foundational elements of Ashtanga - Sun Salutations, standing poses, and the Tristhana method - through guided online videos.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Online+Ashtanga+Intro',
        link: '/courses/online-ashtanga-intro'
      }
    ]
  },
  'kundalini-yoga': {
    title: 'Kundalini Yoga: The Yoga of Awareness',
    description: `Kundalini Yoga, as taught by Yogi Bhajan®, is a dynamic and powerful technology designed to awaken the dormant spiritual energy – Kundalini – believed to reside at the base of the spine. Often referred to as the "Yoga of Awareness," it aims to expand consciousness and foster a direct experience of one's higher self.

    A typical Kundalini Yoga class integrates physical postures (asanas), dynamic movements, specific breathing techniques (pranayama, like Breath of Fire), hand gestures (mudras), eye focus (drishti), body locks (bandhas), chanting (mantra), and meditation. These components are often combined into prescribed sets called Kriyas, each designed to achieve a specific outcome, such as strengthening the nervous system, balancing the glandular system, or enhancing intuition. It's a holistic practice that works quickly to generate energy and bring balance to the body, mind, and soul.`,
    history: `While the concept of Kundalini energy and practices to awaken it are ancient, rooted in Tantric and Yogic traditions of India spanning centuries, the specific system known as Kundalini Yoga was introduced to the West by Yogi Bhajan (Harbhajan Singh Khalsa Yogiji) in 1968. He arrived in North America during a time of cultural shift and felt a calling to teach openly what had traditionally been a secret science, aiming to equip people with tools to navigate the pressures of the "Aquarian Age."

    Yogi Bhajan founded the Healthy, Happy, Holy Organization (3HO) in 1969 to spread these teachings. He emphasized that Kundalini Yoga was a "householder's yoga," practical for people living active lives with families and jobs. He taught thousands of Kriyas and meditations until his passing in 2004, establishing a global community and training infrastructure that continues to share this lineage.`,
    keyPrinciples: [
      'Awakening Kundalini Energy: The primary goal is to safely awaken and raise the latent spiritual energy up the spine through the chakras.',
      'Kriya (Completed Action): Specific sequences combining postures, breath, sound, and focus to produce precise effects on the body, mind, and energy systems.',
      'Pranayama (Breath Control): Utilizing various potent breathing techniques (e.g., Breath of Fire, alternate nostril breathing, long deep breathing) to alter consciousness and energy levels.',
      'Mantra (Sound Current): Using chanting, often in Gurmukhi or Sanskrit, to vibrate the consciousness, focus the mind, and elevate energy.',
      'Meditation: Employing diverse meditation techniques, often incorporating mantra, mudra, and specific timings, to cultivate awareness and stillness.',
      'Bandhas (Body Locks): Applying internal locks (Root, Diaphragm, Neck) to direct prana and Kundalini energy flow.',
      'Focus on Awareness & Neutral Mind: Cultivating the ability to observe thoughts and sensations without reaction, developing intuition and a state of inner balance.'
    ],
    benefits: [
      'Strengthens the nervous system, increasing resilience to stress.',
      'Balances the endocrine (glandular) system, impacting hormones and mood.',
      'Increases vitality, energy levels, and stamina.',
      'Enhances lung capacity and respiratory efficiency.',
      'Promotes emotional balance and reduces reactivity.',
      'Sharpens intuition and expands self-awareness.',
      'Clears subconscious patterns and mental blocks.',
      'Improves focus, concentration, and mental clarity.',
      'Can aid in overcoming addictive behaviors and patterns.',
      'Fosters a deeper connection to one\'s spiritual self and purpose.'
    ],
    relatedServices: [
      {
        id: 1,
        title: 'Kundalini Yoga & Meditation Teacher Training (Level 1)',
        type: 'course',
        description: 'Become a certified Kundalini Yoga teacher through this immersive program covering Kriyas, meditation, philosophy, and lifestyle.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Kundalini+Level+1+YTT',
        link: '/courses/kundalini-teacher-training-level1'
      },
      {
        id: 2,
        title: 'Radiant Body Kundalini Awakening Retreat',
        type: 'retreat',
        description: 'A 7-day transformative journey using potent Kriyas and meditations to activate your radiant energy and deepen awareness.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Radiant+Body+Retreat',
        link: '/retreats/kundalini-radiant-body'
      },
      {
        id: 3,
        title: 'Online Course: Mastering Breath of Fire & Core Kriyas',
        type: 'online',
        description: 'Learn essential Kundalini techniques like Breath of Fire and foundational Kriyas for energy and clarity online.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Online+Kundalini+Techniques',
        link: '/courses/online-kundalini-techniques'
      }
    ]
  },
  'yin-yoga': {
    title: 'Yin Yoga: The Quiet Practice of Deep Release',
    description: `Yin Yoga is a slow, meditative style of yoga that targets the body's deeper connective tissues – fascia, ligaments, joints, and even bones – rather than focusing primarily on muscles. Characterized by passive postures held for extended periods, typically ranging from 3 to 5 minutes or longer, Yin Yoga invites practitioners to relax muscular effort and surrender into the pose.

    This approach applies gentle, sustained stress to the yin tissues, encouraging them to become healthier, more hydrated, and resilient over time. It complements more active (yang) forms of yoga and exercise by promoting flexibility, improving joint mobility, and cultivating profound stillness. Drawing principles from Traditional Chinese Medicine (TCM), Yin poses are often sequenced to stimulate meridian pathways, enhancing the flow of Qi (vital life force) throughout the body. It is a practice of quiet introspection and deep physical opening.`,
    history: `Yin Yoga, as a distinct style, emerged primarily in the late 1970s and 1980s through the work of several key figures. Paulie Zink, a martial arts expert and Taoist Yoga teacher, taught long-held floor poses derived from Tao Yin (Daoist Yoga) traditions. His student, Paul Grilley, became fascinated by these practices and, drawing on his studies of anatomy (especially with Dr. Garry Parker and later Dr. Hiroshi Motoyama, who researched meridians), began teaching a synthesis of these long holds with anatomical understanding and meridian theory in the late 1980s.

    Sarah Powers, another influential teacher who studied with Grilley, coined the term "Yin Yoga" to distinguish it from more active, muscular (Yang) styles. Powers further integrated Buddhist mindfulness principles and a focus on meridian pathways into the practice. Together, Zink, Grilley, and Powers (along with others like Bernie Clark who later helped popularize it through writing and teaching) established Yin Yoga as a recognized and valuable approach within the broader yoga landscape, emphasizing its benefits as a complement to yang activities.`,
    keyPrinciples: [
      'Long, Passive Holds: Maintaining poses for extended durations (typically 3-10 minutes) with minimal muscular engagement.',
      'Targeting Connective Tissues: Applying gentle, sustained stress to fascia, ligaments, and joints to stimulate health and hydration.',
      'Finding an Appropriate Edge: Moving slowly into a pose to find a point of safe and sustainable sensation, avoiding sharp pain.',
      'Resolving to Remain Still: Cultivating physical stillness within the pose to allow the deeper tissues to respond, minimizing fidgeting.',
      'Relaxing Muscles: Consciously releasing muscular tension around the targeted area to allow the stress to reach the deeper yin tissues.',
      'Listening to the Body: Paying close attention to sensations and modifying or exiting the pose when necessary.',
      'Utilizing Props: Employing bolsters, blocks, and blankets to support the body and facilitate relaxation and appropriate stress.',
      'Mindfulness & Introspection: Using the long holds as an opportunity for meditation, observing thoughts, feelings, and sensations without judgment.',
      'Meridian Stimulation (Optional): Often sequencing poses based on Traditional Chinese Medicine principles to influence the flow of Qi through energy pathways.'
    ],
    benefits: [
      'Increases flexibility, particularly in the hips, pelvis, and lower spine.',
      'Improves joint mobility and range of motion by stressing joints healthily.',
      'Hydrates and strengthens fascia and connective tissues.',
      'Releases deep-seated tension in the body and myofascial networks.',
      'Calms the nervous system, reducing stress and anxiety significantly.',
      'Enhances circulation, particularly to the joints and deep tissues.',
      'Stimulates meridian pathways, potentially balancing organ health (TCM perspective).',
      'Develops patience, perseverance, and the ability to sit with discomfort.',
      'Cultivates deep states of relaxation and introspection.',
      'Provides an excellent complement to dynamic (Yang) activities and sports.'
    ],
    relatedServices: [
      {
        id: 1,
        title: 'Yin Yoga & Anatomy Teacher Training (100-Hour)',
        type: 'course',
        description: 'Specialize in teaching Yin Yoga with in-depth knowledge of functional anatomy, fascia, meridians, and safe practice.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Yin+Anatomy+YTT',
        link: '/courses/yin-anatomy-teacher-training'
      },
      {
        id: 2,
        title: 'Deep Relaxation Yin & Yoga Nidra Retreat',
        type: 'retreat',
        description: 'A 5-day journey into profound rest, combining gentle Yin Yoga with guided Yoga Nidra for ultimate stress release.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Yin+Nidra+Retreat',
        link: '/retreats/yin-yoga-nidra'
      },
      {
        id: 3,
        title: 'Online Yin Yoga for Joint Health Series',
        type: 'online',
        description: 'Learn targeted Yin sequences online to support mobility and health in your hips, spine, and shoulders.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Online+Yin+Joints',
        link: '/courses/online-yin-joint-health'
      }
    ]
  },
  'vinyasa-flow': {
    title: 'Vinyasa Flow: The Dance of Breath and Movement',
    description: `Vinyasa Flow Yoga is characterized by its fluid transitions between postures, seamlessly linking movement with the rhythm of the breath. Often called simply "Flow Yoga," this dynamic style emphasizes continuous movement, creating a practice that can feel like a moving meditation. Unlike fixed-sequence styles like Ashtanga, Vinyasa classes offer variety and creativity, with sequences often varying from class to class and teacher to teacher.

    The term "Vinyasa" translates from Sanskrit as "to place in a special way," highlighting the intentional sequencing of poses designed to build heat, strength, flexibility, and focus. Classes often start with warm-ups like Sun Salutations (Surya Namaskar) and progress through standing poses, balances, backbends, twists, and seated postures, culminating in relaxation. It's an invigorating practice that enhances cardiovascular health, builds full-body awareness, and encourages present-moment focus.`,
    history: `Vinyasa Flow evolved primarily from the Ashtanga Vinyasa tradition taught by Sri K. Pattabhi Jois, which itself stems from the teachings of Sri T. Krishnamacharya in the early-to-mid 20th century. Krishnamacharya emphasized the importance of adapting yoga to the individual and explored linking breath with movement. While Ashtanga codified this into fixed sequences, the principles of vinyasa (breath-synchronized movement) and Tristhana (breath, posture, gaze) laid the groundwork for more fluid styles.

    As yoga spread West and teachers began experimenting, the rigidity of the set Ashtanga sequences gave way to more creative interpretations. Teachers started developing unique flows and sequences, adapting the practice to different student needs and intentions. There isn't a single founder of Vinyasa Flow; rather, it represents a modern evolution and creative adaptation of traditional principles, becoming one of the most popular and diverse styles of yoga practiced globally today. Key figures who studied with Jois or were influenced by Krishnamacharya's lineage contributed to its rise.`,
    keyPrinciples: [
      'Breath-Synchronized Movement: Each movement into or out of a posture is initiated and guided by either an inhalation or an exhalation.',
      'Continuous Flow: Poses transition smoothly one into the next, minimizing static holds compared to Hatha, creating a dance-like quality.',
      'Ujjayi Breath (often used): Maintaining a steady, audible breath (similar to Ashtanga) to build internal heat and maintain rhythmic focus.',
      'Creative and Varied Sequencing: Teachers design unique sequences, often building towards peak poses or focusing on specific themes or body parts.',
      'Mindful Transitions: Emphasis not just on the postures themselves, but on the awareness and control used while moving between them.',
      'Cultivating Presence: The continuous movement and breath focus anchor the practitioner in the present moment, fostering a state of moving meditation.',
      'Adaptability: Sequences can be modified for different levels and intentions, making it accessible yet challenging.'
    ],
    benefits: [
      'Builds cardiovascular endurance and improves heart health.',
      'Increases muscular strength and endurance throughout the body.',
      'Enhances flexibility and agility through dynamic movement.',
      'Improves balance, coordination, and proprioception.',
      'Promotes mental focus and concentration.',
      'Reduces stress and anxiety by encouraging present-moment awareness.',
      'Can aid in weight management due to its often vigorous nature.',
      'Increases energy levels and reduces feelings of sluggishness.',
      'Develops a strong mind-body connection.',
      'Offers creative expression and variety, preventing boredom.'
    ],
    relatedServices: [
      {
        id: 1,
        title: 'Creative Vinyasa Sequencing Teacher Training (300-Hour Advanced)',
        type: 'course',
        description: 'Elevate your teaching skills with advanced techniques in designing intelligent, safe, and inspiring Vinyasa Flow sequences.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Vinyasa+300hr+YTT',
        link: '/courses/vinyasa-advanced-ytt-300'
      },
      {
        id: 2,
        title: 'Dynamic Flow & Ocean Air Yoga Retreat',
        type: 'retreat',
        description: 'Energize your body and soul with daily Vinyasa Flow practices, workshops, and relaxation by the sea for 7 days.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Dynamic+Flow+Retreat',
        link: '/retreats/dynamic-flow-ocean'
      },
      {
        id: 3,
        title: 'Online Vinyasa Flow Library Access',
        type: 'online',
        description: 'Practice anytime, anywhere with our growing library of diverse Vinyasa Flow classes ranging from beginner to advanced.',
        image: 'https://placehold.co/800x600/e2e8f0/1e293b?text=Online+Vinyasa+Library',
        link: '/courses/online-vinyasa-library'
      }
    ]
  },
   'meditation': {
      "title": "Meditation: Cultivating Inner Stillness and Awareness",
      "description": "Meditation encompasses a range of practices designed to train attention and awareness, ultimately fostering mental clarity, emotional calm, and a stable sense of presence. Techniques vary widely, including focusing on the breath, observing thoughts and sensations without judgment (mindfulness), repeating a mantra, or engaging in guided visualizations. It is not about stopping thoughts, but rather learning to observe them without attachment, creating space between stimulus and response. Accessible to everyone, meditation offers a powerful pathway to reduce stress, enhance self-understanding, and cultivate inner peace in our often-hectic lives.",
      "history": "The practice of meditation has ancient origins, with evidence suggesting its practice as far back as 5,000 BCE, depicted in early wall art in the Indus Valley. Formal documentation appears in the Hindu Vedas around 1500 BCE. Meditation flourished and diversified within various traditions across Asia, notably in Hinduism (Vedanta, Yoga Sutras), Buddhism (Vipassana, Zazen, Tibetan practices tracing back to the 6th-4th centuries BCE), Jainism, and Taoism in China. These practices spread globally, particularly via the Silk Road trade routes, influencing contemplative practices in Judaism (Kabbalah, Hitbodedut), Christianity (Hesychasm), and Islam (Sufism, Tafakkur). While historically intertwined with religious and spiritual goals like enlightenment or communion with the divine, meditation gained significant secular interest in the West during the 20th century, particularly from the 1960s onwards, popularized by teachers like Swami Vivekananda, Paramahansa Yogananda, Maharishi Mahesh Yogi (Transcendental Meditation), and the rise of mindfulness-based approaches (like MBSR) supported by scientific research into its psychological and physiological benefits.",
      "keyPrinciples": [
        "Focused Attention (Samatha): Training the mind to concentrate on a single object, like the breath, a mantra, or a visual point.",
        "Open Monitoring (Vipassanā/Mindfulness): Cultivating non-judgmental awareness of whatever arises in the present moment – thoughts, feelings, sensations.",
        "Intention Setting: Clarifying the purpose of the practice, whether for stress reduction, self-awareness, or spiritual growth.",
        "Non-Judgmental Attitude: Observing experiences as they are, without labeling them as 'good' or 'bad'.",
        "Patience and Consistency: Understanding that meditation is a skill developed over time with regular practice.",
        "Letting Go: Practicing non-attachment to thoughts, outcomes, or specific states.",
        "Kindness and Compassion (Metta/Karuna): Cultivating feelings of goodwill towards oneself and others, often as a specific meditation form."
      ],
      "benefits": [
        "Clinically proven reduction in stress and anxiety levels.",
        "Improved attention span, focus, and concentration.",
        "Enhanced emotional regulation and reduced reactivity.",
        "Increased self-awareness and insight into thought patterns.",
        "May improve sleep quality and reduce insomnia.",
        "Cultivates compassion, empathy, and positive emotions.",
        "Can help manage symptoms of depression.",
        "May reduce chronic pain perception.",
        "Potential benefits for blood pressure and cardiovascular health.",
        "Supports cognitive function and may reduce age-related memory loss.",
        "Strengthens immune system function.",
        "Increases psychological well-being and resilience."
      ],
      "relatedServices": [
        {
          "id": 1,
          "title": "Certified Mindfulness Meditation Facilitator Program",
          "type": "course",
          "description": "A comprehensive training equipping you to guide others in various mindfulness meditation techniques effectively and ethically.",
          "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Mindfulness+Facilitator+Cert",
          "link": "/courses/mindfulness-facilitator-certification"
        },
        {
          "id": 2,
          "title": "Noble Silence: 10-Day Vipassana Meditation Retreat",
          "type": "retreat",
          "description": "An intensive silent retreat focused on developing insight and equanimity through the traditional practice of Vipassana meditation.",
          "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Vipassana+Silent+Retreat",
          "link": "/retreats/vipassana-silent-retreat"
        },
        {
          "id": 3,
          "title": "Online Meditation Kickstart: 21-Day Challenge",
          "type": "online",
          "description": "Build a consistent daily meditation habit with guided practices, practical tips, and community support over 21 days.",
          "image": "https://placehold.co/800x600/e2e8f0/1e293b?text=Meditation+21-Day+Challenge",
          "link": "/courses/online-meditation-challenge"
        }
      ]
    }
};


const YogaStylePage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const style = slug ? yogaStyles[slug as keyof typeof yogaStyles] : null;

  if (!style) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">Style Not Found</h1>
          <p className="text-slate-600 dark:text-slate-400">The yoga style you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-amber-50/50 dark:bg-slate-900">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-amber-900/40 via-amber-800/30 to-slate-900/70 dark:from-slate-900/70 dark:via-indigo-900/50 dark:to-slate-900/80"></div>
        <div className="container mx-auto px-4 relative z-10">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">{style.title}</h1>
          <p className="text-xl text-white/90 max-w-3xl">{style.description.split('\n')[0]}</p>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Left Column - History and Benefits */}
            <div className="lg:col-span-2 space-y-12">
              <div>
                <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">History & Origins</h2>
                <div className="prose dark:prose-invert max-w-none">
                  <p>{style.history}</p>
                </div>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">Key Principles</h2>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {style.keyPrinciples.map((principle, index) => (
                    <li 
                      key={index}
                      className="flex items-center space-x-3 text-slate-700 dark:text-slate-300"
                    >
                      <svg className="w-5 h-5 text-amber-500 dark:text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>{principle}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">Benefits</h2>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {style.benefits.map((benefit, index) => (
                    <li 
                      key={index}
                      className="flex items-center space-x-3 text-slate-700 dark:text-slate-300"
                    >
                      <svg className="w-5 h-5 text-amber-500 dark:text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Right Column - Related Services */}
            <div>
              <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">Related Services</h2>
              <div className="space-y-6">
                {style.relatedServices.map((service) => (
                  <div 
                    key={service.id}
                    className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <img 
                      src={service.image} 
                      alt={service.title} 
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-6">
                      <div className="mb-2">
                        <span className={`
                          inline-block px-3 py-1 rounded-full text-xs font-semibold
                          ${service.type === 'course' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : ''}
                          ${service.type === 'retreat' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' : ''}
                          ${service.type === 'online' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : ''}
                        `}>
                          {service.type.charAt(0).toUpperCase() + service.type.slice(1)}
                        </span>
                      </div>
                      <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 dark:text-slate-400 mb-4">
                        {service.description}
                      </p>
                      <a
                        href={service.link}
                        className="inline-block w-full py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-center"
                      >
                        Learn More
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default YogaStylePage;