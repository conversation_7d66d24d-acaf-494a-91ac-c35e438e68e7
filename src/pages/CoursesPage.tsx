import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import {} from 'react-router-dom';
import GridCourseCard from '../components/GridCourseCard';
import ListCourseCard from '../components/ListCourseCard';
import { Grid2x2, Grid3x3, TableProperties } from 'lucide-react';
import { Course, Certification } from '../data/data';

const CoursesPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('all');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc' | null>(null);
  const [viewMode, setViewModeState] = useState<'list' | 'grid2' | 'grid3'>(() => {
    const saved = localStorage.getItem('coursesViewMode');
    if (saved === 'list' || saved === 'grid3') return saved;
    return 'grid2';
  });

  const setViewMode = (mode: 'list' | 'grid2' | 'grid3') => {
    setViewModeState(mode);
    localStorage.setItem('coursesViewMode', mode);
  };

  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [certifications, setCertifications] = useState<Certification[]>([]);

  React.useEffect(() => {
    try {
      // Import data directly from data.ts
      import('../data/data').then(({ courses, certifications }) => {
        setCourses(courses);
        setCertifications(certifications);
      });
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  let filteredCourses = courses.filter(c => c.active);

  if (activeTab !== 'all') {
    filteredCourses = filteredCourses.filter((c) => {
      if (activeTab === 'short' && c.duration <= 14) return true;
      if (activeTab === 'medium' && c.duration > 14 && c.duration < 30) return true;
      if (activeTab === 'long' && c.duration >= 30) return true;
      return false;
    });
  }

  if (sortOrder) {
    filteredCourses = [...filteredCourses].sort((a, b) => {
      const priceA = a.price.shared || 0;
      const priceB = b.price.shared || 0;
      return sortOrder === 'asc' ? priceA - priceB : priceB - priceA;
    });
  }

  if (loading) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-slate-600 dark:text-slate-300">Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-red-600 dark:text-red-400">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
      <Helmet>
        <title>Yoga Teacher Training Courses | Shanti Yog Peeth</title>
        <meta name="description" content="Explore our yoga teacher training courses in Rishikesh. Find the right program to deepen your practice and become a certified yoga instructor." />
        <meta property="og:title" content="Yoga Teacher Training Courses | Shanti Yog Peeth" />
        <meta property="og:description" content="Explore our yoga teacher training courses in Rishikesh. Find the right program to deepen your practice and become a certified yoga instructor." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://yourdomain.com/courses" />
      </Helmet>
      <div className="bg-gradient-to-b from-amber-400/10 to-transparent dark:from-indigo-900/20 dark:to-transparent py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-6xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Transform Your <span className="text-amber-500 dark:text-amber-400">Practice</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Discover our comprehensive yoga teacher training programs and take the next step in your spiritual journey.
          </p>
        </div>
      </div>

      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="md:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg sticky top-24">
              <div className="mb-6">
                <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">Sort Courses</h3>
                <select
                  value={sortOrder || ''}
                  onChange={(e) => {
                    const val = e.target.value;
                    if (val === '') setSortOrder(null);
                    else if (val === 'asc') setSortOrder('asc');
                    else if (val === 'desc') setSortOrder('desc');
                  }}
                  className="w-full px-3 py-2 rounded-lg border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                >
                  <option value="">None</option>
                  <option value="asc">Price: Low to High</option>
                  <option value="desc">Price: High to Low</option>
                </select>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">View Mode</h3>
                <div className="flex justify-center gap-3">
                  <button
                    onClick={() => setViewMode('grid2')}
                    className={`px-3 py-2 rounded-xl border transition ${
                      viewMode === 'grid2'
                        ? 'border-amber-500 dark:border-amber-400 text-amber-500 dark:text-amber-400 bg-amber-50 dark:bg-slate-700'
                        : 'border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50'
                    }`}
                    title="2-Column Grid"
                  >
                    <Grid2x2 size={28} />
                  </button>
                  <button
                    onClick={() => setViewMode('grid3')}
                    className={`px-3 py-2 rounded-xl border transition ${
                      viewMode === 'grid3'
                        ? 'border-amber-500 dark:border-amber-400 text-amber-500 dark:text-amber-400 bg-amber-50 dark:bg-slate-700'
                        : 'border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50'
                    }`}
                    title="3-Column Grid"
                  >
                    <Grid3x3 size={28} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`px-3 py-2 rounded-xl border transition ${
                      viewMode === 'list'
                        ? 'border-amber-500 dark:border-amber-400 text-amber-500 dark:text-amber-400 bg-amber-50 dark:bg-slate-700'
                        : 'border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50'
                    }`}
                    title="List View"
                  >
                    <TableProperties size={28} style={{ transform: 'scaleX(-1)' }} />
                  </button>
                </div>
              </div>

              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">Duration</h3>
              <div className="space-y-2">
                {['all', 'short', 'medium', 'long'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`w-full text-left px-4 py-2 rounded-lg transition-all ${
                      activeTab === tab
                        ? 'bg-amber-400/10 dark:bg-indigo-600/20 text-amber-600 dark:text-amber-400 font-medium'
                        : 'hover:bg-slate-100 dark:hover:bg-slate-700/50 text-slate-700 dark:text-slate-300'
                    }`}
                  >
                    {tab === 'all' && 'All Courses'}
                    {tab === 'short' && 'Short (≤ 14 days)'}
                    {tab === 'medium' && 'Medium (15-29 days)'}
                    {tab === 'long' && 'Long (≥ 30 days)'}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Course Grid */}
          <div className="flex-1">
            {viewMode === 'list' ? (
              <div className="flex flex-col gap-6">
                {filteredCourses.map((course) => (
                  <ListCourseCard key={course.id} course={course} certifications={certifications} />
                ))}
              </div>
            ) : (
              <div className={`grid grid-cols-1 ${viewMode === 'grid2' ? 'lg:grid-cols-2' : 'lg:grid-cols-3'} gap-6`}>
                {filteredCourses.map((course) => (
                  <GridCourseCard key={course.id} course={course} certifications={certifications} />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

    </div>
  );
};

export default CoursesPage;
