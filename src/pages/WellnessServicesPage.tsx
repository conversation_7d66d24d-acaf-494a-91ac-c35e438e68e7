import React, { useState } from 'react';
import {
  Heart, Leaf, DollarSign, Clock,
  Activity, Brain, Droplet,
  Thermometer, Sun, Moon, Coffee, Utensils, BookOpen,
  Wind, Star,
  Mail, Phone, MapPin
} from 'lucide-react';

// Interfaces
interface Treatment {
  id: number;
  name: string;
  duration: string;
  price: string;
  description: string;
  benefits: string[];
  image?: string;
  category?: string;
}

export const treatments: Treatment[] = [
  {
    id: 1,
    name: "Abhyanga Massage",
    duration: "60 min",
    price: "$75",
    description: "Traditional Ayurvedic full-body massage using warm herbal oils.",
    benefits: [
      "Improves circulation",
      "Reduces stress and anxiety",
      "Promotes better sleep",
      "Nourishes the skin"
    ],
    image: "https://images.unsplash.com/photo-1600334129128-685c5582fd35?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Massage"
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    duration: "45 min",
    price: "$90",
    description: "Gentle pouring of warm oil on the forehead for deep relaxation.",
    benefits: [
      "Calms the mind",
      "Improves mental clarity",
      "Helps with insomnia",
      "Reduces anxiety"
    ],
    image: "https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Relaxation"
  },
  {
    id: 3,
    name: "Panchakarma",
    duration: "5-7 days",
    price: "$550",
    description: "Complete detoxification and rejuvenation program.",
    benefits: [
      "Deep cleansing",
      "Removes toxins",
      "Restores balance",
      "Improves overall health"
    ],
    image: "https://images.unsplash.com/photo-1512290923902-8a9f81dc236c?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Detox"
  },
  {
    id: 4,
    name: "Nasyam",
    duration: "30 min",
    price: "$60",
    description: "Nasal therapy to clear sinuses and improve respiratory health.",
    benefits: [
      "Clears nasal passages",
      "Relieves sinus congestion",
      "Improves sense of smell",
      "Helps with allergies"
    ],
    image: "https://images.unsplash.com/photo-1519823551278-64ac92734fb1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Therapy"
  },
  {
    id: 5,
    name: "Netratarpana",
    duration: "40 min",
    price: "$70",
    description: "Eye rejuvenation therapy using medicated ghee.",
    benefits: [
      "Soothes tired eyes",
      "Improves vision",
      "Reduces dark circles",
      "Relieves eye strain"
    ],
    image: "https://images.unsplash.com/photo-1494869042583-f6c911f04b4c?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Therapy"
  },
  {
    id: 6,
    name: "Karnapoorna",
    duration: "30 min",
    price: "$55",
    description: "Ear therapy using warm medicated oils.",
    benefits: [
      "Improves hearing",
      "Reduces ear infections",
      "Relieves tinnitus",
      "Calms the nervous system"
    ],
    image: "https://images.unsplash.com/photo-1559757175-7cb036bd7d15?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Therapy"
  },
  {
    id: 7,
    name: "Udawartana",
    duration: "50 min",
    price: "$85",
    description: "Herbal powder massage for exfoliation and weight management.",
    benefits: [
      "Exfoliates skin",
      "Reduces cellulite",
      "Improves circulation",
      "Aids weight loss"
    ],
    image: "https://images.unsplash.com/photo-1519415510236-718bdfcd89c8?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Massage"
  },
  {
    id: 8,
    name: "Marma Therapy",
    duration: "60 min",
    price: "$95",
    description: "Pressure point massage to stimulate energy flow.",
    benefits: [
      "Balances energy",
      "Relieves pain",
      "Improves organ function",
      "Enhances vitality"
    ],
    image: "https://images.unsplash.com/photo-1519824145371-296894a0daa9?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    category: "Massage"
  }
];

const wellnessPrograms = [
  {
    id: 1,
    name: "Holistic Panchakarma",
    description: "Complete detoxification and rejuvenation program for overall wellness.",
    icon: <Leaf className="w-8 h-8" />,
    duration: "7-21 days"
  },
  {
    id: 2,
    name: "Stress Relief",
    description: "Specialized program to reduce stress, anxiety, and depression.",
    icon: <Brain className="w-8 h-8" />,
    duration: "5-14 days"
  },
  {
    id: 3,
    name: "Weight Management",
    description: "Customized program for healthy weight loss and metabolism balance.",
    icon: <Activity className="w-8 h-8" />,
    duration: "14-28 days"
  },
  {
    id: 4,
    name: "Digestive Health",
    description: "Re-establish healthy digestion and improve gut function.",
    icon: <Droplet className="w-8 h-8" />,
    duration: "7-14 days"
  },
  {
    id: 5,
    name: "Diabetes Management",
    description: "Natural approach to managing diabetes and blood sugar levels.",
    icon: <Thermometer className="w-8 h-8" />,
    duration: "14-21 days"
  },
  {
    id: 6,
    name: "Pain Management",
    description: "Relief from chronic pain through Ayurvedic therapies.",
    icon: <Activity className="w-8 h-8" />,
    duration: "7-14 days"
  },
  {
    id: 7,
    name: "Skin Rejuvenation",
    description: "Revitalize your skin with natural Ayurvedic treatments.",
    icon: <Sun className="w-8 h-8" />,
    duration: "7-14 days"
  },
  {
    id: 8,
    name: "Post-Recovery Care",
    description: "Strengthen immunity and restore health after illness.",
    icon: <Heart className="w-8 h-8" />,
    duration: "14-21 days"
  },
  {
    id: 9,
    name: "Skin Disorder Treatment",
    description: "Address specific skin conditions with Ayurvedic remedies.",
    icon: <Sun className="w-8 h-8" />,
    duration: "14-21 days"
  },
  {
    id: 10,
    name: "Mental Wellness",
    description: "Enhance mental clarity, focus, and emotional balance.",
    icon: <Brain className="w-8 h-8" />,
    duration: "7-14 days"
  }
];

const packages = [
  {
    id: 1,
    name: "Panchakarma - 7 Days",
    price: "$550",
    duration: "7 days",
    description: "Essential detoxification and rejuvenation program",
    features: [
      "Initial Ayurvedic consultation",
      "Personalized treatment plan",
      "Daily Abhyanga massage",
      "Shirodhara sessions",
      "Herbal steam therapy",
      "Yoga and meditation classes",
      "Ayurvedic vegetarian meals"
    ]
  },
  {
    id: 2,
    name: "Panchakarma - 14 Days",
    price: "$1000",
    duration: "14 days",
    description: "Complete transformation and healing program",
    features: [
      "Comprehensive Ayurvedic assessment",
      "Customized treatment protocol",
      "Daily therapeutic massages",
      "Specialized detox procedures",
      "Herbal supplements",
      "Daily yoga and meditation",
      "Personalized diet plan",
      "Lifestyle counseling"
    ],
    popular: true
  },
  {
    id: 3,
    name: "Panchakarma - 21 Days",
    price: "$1400",
    duration: "21 days",
    description: "Intensive healing and rejuvenation",
    features: [
      "In-depth Ayurvedic consultation",
      "Full detoxification protocol",
      "Daily therapeutic treatments",
      "Advanced cleansing procedures",
      "Personalized herbal formulations",
      "Daily yoga and meditation",
      "Customized nutrition plan",
      "Take-home wellness program"
    ]
  },
  {
    id: 4,
    name: "Purification of 5 Senses",
    price: "$500",
    duration: "5 days",
    description: "Enhance perception and sensory clarity",
    features: [
      "Specialized treatments for each sense",
      "Nasyam for nasal passages",
      "Netratarpana for eyes",
      "Karnapoorna for ears",
      "Skin treatments",
      "Tongue cleansing therapy",
      "Daily Abhyanga massage"
    ]
  },
  {
    id: 5,
    name: "Weight Loss Therapy - 14 Days",
    price: "$1000",
    duration: "14 days",
    description: "Reset metabolism and establish healthy patterns",
    features: [
      "Metabolic assessment",
      "Udwartana powder massage",
      "Herbal steam therapy",
      "Detoxification procedures",
      "Customized diet plan",
      "Exercise regimen",
      "Herbal supplements"
    ]
  },
  {
    id: 6,
    name: "Weight Loss Therapy - 21 Days",
    price: "$1400",
    duration: "21 days",
    description: "Comprehensive weight management program",
    features: [
      "Complete body composition analysis",
      "Intensive detoxification",
      "Specialized massage therapies",
      "Personalized nutrition plan",
      "Daily fitness activities",
      "Stress management techniques",
      "Long-term maintenance strategy"
    ]
  }
];

const dailySchedule = [
  {
    time: "6:00 AM",
    activity: "Wake Up",
    icon: <Sun className="w-5 h-5" />,
    description: "Begin your day with the sunrise"
  },
  {
    time: "6:20 AM",
    activity: "Hydration",
    icon: <Droplet className="w-5 h-5" />,
    description: "Drink warm water to cleanse the digestive system"
  },
  {
    time: "6:30 AM",
    activity: "Herbal Tea",
    icon: <Coffee className="w-5 h-5" />,
    description: "Enjoy a cup of detoxifying herbal tea"
  },
  {
    time: "7:15 AM",
    activity: "Morning Yoga",
    icon: <Activity className="w-5 h-5" />,
    description: "Gentle yoga for joints and spine flexibility"
  },
  {
    time: "8:20 AM",
    activity: "Breakfast",
    icon: <Utensils className="w-5 h-5" />,
    description: "Light, nutritious Ayurvedic breakfast"
  },
  {
    time: "9:00 AM - 1:00 PM",
    activity: "Ayurvedic Treatments",
    icon: <Heart className="w-5 h-5" />,
    description: "Personalized treatments as prescribed"
  },
  {
    time: "12:30 PM - 2:00 PM",
    activity: "Lunch",
    icon: <Utensils className="w-5 h-5" />,
    description: "Balanced Ayurvedic vegetarian meal"
  },
  {
    time: "2:00 PM - 4:00 PM",
    activity: "Rest & Reading",
    icon: <BookOpen className="w-5 h-5" />,
    description: "Quiet time for rest and reflection"
  },
  {
    time: "4:00 PM",
    activity: "Herbal Tea",
    icon: <Coffee className="w-5 h-5" />,
    description: "Afternoon herbal refreshment"
  },
  {
    time: "5:00 PM - 6:00 PM",
    activity: "Meditation",
    icon: <Moon className="w-5 h-5" />,
    description: "Guided meditation and yoga nidra"
  },
  {
    time: "7:30 PM",
    activity: "Dinner",
    icon: <Utensils className="w-5 h-5" />,
    description: "Light evening meal"
  },
  {
    time: "9:00 PM",
    activity: "Sleep",
    icon: <Moon className="w-5 h-5" />,
    description: "Early rest for natural healing"
  }
];

const doshaTypes = [
  {
    name: "Vata",
    description: "Composed of Air and Space elements",
    characteristics: [
      "Creative and energetic when balanced",
      "Prone to anxiety and restlessness when imbalanced",
      "Light, cold, dry qualities",
      "Governs movement and nervous system"
    ],
    icon: <Wind className="w-8 h-8" />,
    color: "bg-blue-100 dark:bg-blue-900"
  },
  {
    name: "Pitta",
    description: "Composed of Fire and Water elements",
    characteristics: [
      "Intelligent and focused when balanced",
      "Prone to irritability and inflammation when imbalanced",
      "Hot, sharp, oily qualities",
      "Governs metabolism and digestion"
    ],
    icon: <Thermometer className="w-8 h-8" />,
    color: "bg-red-100 dark:bg-red-900"
  },
  {
    name: "Kapha",
    description: "Composed of Earth and Water elements",
    characteristics: [
      "Calm and nurturing when balanced",
      "Prone to lethargy and weight gain when imbalanced",
      "Heavy, cold, oily qualities",
      "Governs structure and lubrication"
    ],
    icon: <Droplet className="w-8 h-8" />,
    color: "bg-green-100 dark:bg-green-900"
  }
];

const testimonials = [
  {
    id: 1,
    name: "Sarah Johnson",
    location: "United States",
    quote: "The Panchakarma program completely transformed my health. After years of digestive issues, I finally feel balanced and energized.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
  },
  {
    id: 2,
    name: "Michael Chen",
    location: "Canada",
    quote: "I came for the stress relief program and left with not only reduced anxiety but also a new perspective on life and wellness.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
  },
  {
    id: 3,
    name: "Emma Rodriguez",
    location: "Australia",
    quote: "The weight management program helped me lose 15 pounds in a healthy, sustainable way. The personalized approach made all the difference.",
    rating: 4,
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60"
  }
];

const WellnessServicesPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [selectedDosha, setSelectedDosha] = useState<string | null>(null);

  // Filter treatments by category
  const filteredTreatments = activeCategory
    ? treatments.filter(treatment => treatment.category === activeCategory)
    : treatments;

  return (
    <div className="pt-18">
      {/* Enhanced Hero Section */}
      <div className="relative bg-amber-400/10 dark:bg-indigo-900/20 py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 z-0 opacity-20">
          <img
            src="https://images.unsplash.com/photo-**********-4ab6ce6db874?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80"
            alt="Ayurvedic background"
            className="w-full h-full object-cover"
          />
        </div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <h1 className="text-4xl md:text-6xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Authentic <span className="text-amber-500 dark:text-amber-400">Ayurvedic Wellness</span>
          </h1>
          <p className="text-xl md:text-2xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto mb-10">
            Experience traditional healing therapies in the spiritual heart of Rishikesh.
          </p>
          <div className="flex justify-center">
            <button className="px-8 py-4 bg-amber-500 hover:bg-amber-600 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-white rounded-lg font-medium transition-all duration-300 shadow-lg transform hover:scale-105">
              Book Consultation
            </button>
          </div>
        </div>
      </div>

      {/* Introduction to Ayurveda Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block">
              <div className="flex items-center mb-4 justify-center">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Ancient Wisdom
                </span>
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 ml-4"></div>
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Understanding <span className="text-amber-500 dark:text-amber-400">Ayurveda</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Ayurveda, the 5000-year-old "science of life," is one of the world's oldest holistic healing systems.
              It's based on the belief that health and wellness depend on a delicate balance between the mind, body, and spirit.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-16">
            {doshaTypes.map((dosha) => (
              <div
                key={dosha.name}
                className={`rounded-xl p-8 shadow-lg transition-all duration-300 cursor-pointer ${
                  selectedDosha === dosha.name
                    ? `${dosha.color} scale-105`
                    : 'bg-white dark:bg-slate-800 hover:shadow-xl'
                }`}
                onClick={() => setSelectedDosha(dosha.name === selectedDosha ? null : dosha.name)}
              >
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400">
                    {dosha.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-center text-slate-900 dark:text-white mb-4">
                  {dosha.name} Dosha
                </h3>
                <p className="text-slate-600 dark:text-slate-300 text-center mb-4">
                  {dosha.description}
                </p>
                {selectedDosha === dosha.name && (
                  <div className="mt-4 space-y-2 animate-fadeIn">
                    {dosha.characteristics.map((characteristic, index) => (
                      <div key={index} className="flex items-start">
                        <Leaf className="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2 mt-1 flex-shrink-0" />
                        <p className="text-sm text-slate-600 dark:text-slate-300">{characteristic}</p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="bg-amber-50 dark:bg-slate-800/50 rounded-2xl p-8 md:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">
                  Your Ayurvedic Journey Begins with a Consultation
                </h3>
                <p className="text-slate-600 dark:text-slate-300 mb-8">
                  Begin your wellness journey with a comprehensive Ayurvedic consultation.
                  Our experienced practitioners will assess your constitution (dosha) and create
                  a personalized treatment plan tailored to your unique needs.
                </p>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <Clock className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">60-Minute Session</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">In-depth analysis and personalized recommendations</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Heart className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">Holistic Assessment</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">Physical, mental, and lifestyle evaluation</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Leaf className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">Custom Treatment Plan</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">Dietary, lifestyle, and therapy recommendations</p>
                    </div>
                  </li>
                </ul>
                <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300">
                  Book Consultation
                </button>
              </div>
              <div className="relative">
                <img
                  src="https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80"
                  alt="Ayurvedic consultation"
                  className="rounded-lg shadow-xl"
                />
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-amber-400 dark:bg-indigo-600 rounded-lg -z-10"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Wellness Programs Section */}
      <section className="py-20 bg-slate-50 dark:bg-slate-800/30">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block">
              <div className="flex items-center mb-4 justify-center">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Specialized Care
                </span>
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 ml-4"></div>
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Wellness <span className="text-amber-500 dark:text-amber-400">Programs</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Our specialized wellness programs address specific health concerns through authentic Ayurvedic approaches.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
            {wellnessPrograms.slice(0, 8).map((program) => (
              <div
                key={program.id}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden"
              >
                <div className="p-6">
                  <div className="w-12 h-12 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400 mb-4">
                    {program.icon}
                  </div>
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
                    {program.name}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-4 text-sm">
                    {program.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-amber-500 dark:text-amber-400 font-medium">
                      {program.duration}
                    </span>
                    <button className="text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300">
                      Learn More →
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300">
              View All Programs
            </button>
          </div>
        </div>
      </section>

      {/* Ayurvedic Consultation Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Personal Consultation
                </span>
              </div>
              <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">
                Ayurvedic Wellness Consultation
              </h2>
              <p className="text-slate-600 dark:text-slate-300 mb-8">
                Begin your wellness journey with a comprehensive Ayurvedic consultation. 
                Our experienced practitioners will assess your constitution (dosha) and create 
                a personalized treatment plan.
              </p>
              <ul className="space-y-4 mb-8">
                <li className="flex items-start">
                  <Clock className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">60-Minute Session</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">In-depth analysis and personalized recommendations</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Heart className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">Holistic Assessment</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Physical, mental, and lifestyle evaluation</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <Leaf className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">Custom Treatment Plan</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Dietary, lifestyle, and therapy recommendations</p>
                  </div>
                </li>
              </ul>
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300">
                Book Consultation
              </button>
            </div>
            <div className="relative">
              <img 
                src="https://placehold.co/600x400/e2e8f0/1e293b?text=Consultation" 
                alt="Ayurvedic consultation"
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-amber-400 dark:bg-indigo-600 rounded-lg -z-10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Treatments Section */}
      {/* Enhanced Treatments Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <div className="inline-block">
              <div className="flex items-center mb-4 justify-center">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Authentic Therapies
                </span>
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 ml-4"></div>
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Ayurvedic <span className="text-amber-500 dark:text-amber-400">Treatments</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto mb-10">
              Choose from our range of authentic Ayurvedic treatments, each designed to promote
              balance and well-being.
            </p>

            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-3 mb-12">
              <button
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  activeCategory === null
                    ? 'bg-amber-500 text-white'
                    : 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700'
                }`}
                onClick={() => setActiveCategory(null)}
              >
                All Treatments
              </button>
              <button
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  activeCategory === 'Massage'
                    ? 'bg-amber-500 text-white'
                    : 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700'
                }`}
                onClick={() => setActiveCategory('Massage')}
              >
                Massage
              </button>
              <button
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  activeCategory === 'Therapy'
                    ? 'bg-amber-500 text-white'
                    : 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700'
                }`}
                onClick={() => setActiveCategory('Therapy')}
              >
                Therapy
              </button>
              <button
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  activeCategory === 'Relaxation'
                    ? 'bg-amber-500 text-white'
                    : 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700'
                }`}
                onClick={() => setActiveCategory('Relaxation')}
              >
                Relaxation
              </button>
              <button
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  activeCategory === 'Detox'
                    ? 'bg-amber-500 text-white'
                    : 'bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-slate-200 dark:hover:bg-slate-700'
                }`}
                onClick={() => setActiveCategory('Detox')}
              >
                Detox
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredTreatments.map((treatment) => (
              <div
                key={treatment.id}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
              >
                {treatment.image && (
                  <div className="h-48 overflow-hidden">
                    <img
                      src={treatment.image}
                      alt={treatment.name}
                      className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                    />
                  </div>
                )}
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                      {treatment.name}
                    </h3>
                    {treatment.category && (
                      <span className="text-xs font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 px-2 py-1 rounded-full">
                        {treatment.category}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-4 mb-4">
                    <span className="flex items-center text-amber-500 dark:text-amber-400">
                      <Clock className="w-4 h-4 mr-1" />
                      {treatment.duration}
                    </span>
                    <span className="flex items-center text-amber-500 dark:text-amber-400">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {treatment.price}
                    </span>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">
                    {treatment.description}
                  </p>
                  <ul className="space-y-2 mb-6">
                    {treatment.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-center text-sm text-slate-600 dark:text-slate-300">
                        <Leaf className="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                  <button
                    className="w-full py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300"
                  >
                    Book Treatment
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Pricing Packages Section */}
      <section className="py-20 bg-slate-50 dark:bg-slate-800/30">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block">
              <div className="flex items-center mb-4 justify-center">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Healing Programs
                </span>
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 ml-4"></div>
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Wellness <span className="text-amber-500 dark:text-amber-400">Packages</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto mb-4">
              Choose from our authentic Ayurvedic wellness packages for a complete healing experience.
            </p>
            <p className="text-sm text-slate-500 dark:text-slate-400 max-w-2xl mx-auto">
              All packages include consultations, treatments, accommodation, meals, and daily yoga and meditation classes.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {packages.slice(0, 3).map((pkg) => (
              <div
                key={pkg.id}
                className={`rounded-xl shadow-lg overflow-hidden ${
                  pkg.popular
                    ? 'bg-gradient-to-br from-amber-400 to-amber-500 dark:from-indigo-600 dark:to-indigo-700 transform scale-105'
                    : 'bg-white dark:bg-slate-800'
                }`}
              >
                {pkg.popular && (
                  <div className="bg-amber-600 dark:bg-indigo-800 text-white text-center py-2 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                <div className="p-8">
                  <div className="text-center mb-8">
                    <h3 className={`text-xl font-bold mb-2 ${pkg.popular ? 'text-white' : 'text-slate-900 dark:text-white'}`}>
                      {pkg.name}
                    </h3>
                    <div className={`text-3xl font-bold mb-4 ${pkg.popular ? 'text-white' : 'text-amber-500 dark:text-amber-400'}`}>
                      {pkg.price}
                    </div>
                    <p className={pkg.popular ? 'text-white/90' : 'text-slate-600 dark:text-slate-300'}>
                      {pkg.duration}
                    </p>
                  </div>
                  <p className={`mb-6 ${pkg.popular ? 'text-white/90' : 'text-slate-600 dark:text-slate-300'}`}>
                    {pkg.description}
                  </p>
                  <ul className="space-y-4 mb-8">
                    {pkg.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Heart className={`w-5 h-5 mr-2 mt-0.5 flex-shrink-0 ${pkg.popular ? 'text-white' : 'text-amber-500 dark:text-amber-400'}`} />
                        <span className={pkg.popular ? 'text-white' : 'text-slate-600 dark:text-slate-300'}>
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                  <button
                    className={`w-full py-3 rounded-lg font-medium transition-colors duration-300 ${
                      pkg.popular
                        ? 'bg-white text-amber-500 dark:text-indigo-600 hover:bg-slate-50'
                        : 'bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white'
                    }`}
                  >
                    Choose Package
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.slice(3).map((pkg) => (
              <div
                key={pkg.id}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-md overflow-hidden"
              >
                <div className="p-6">
                  <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-2">
                    {pkg.name}
                  </h3>
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-2xl font-bold text-amber-500 dark:text-amber-400">
                      {pkg.price}
                    </span>
                    <span className="text-sm text-slate-500 dark:text-slate-400">
                      {pkg.duration}
                    </span>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">
                    {pkg.description}
                  </p>
                  <button className="w-full py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Daily Schedule Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block">
              <div className="flex items-center mb-4 justify-center">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Daily Routine
                </span>
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 ml-4"></div>
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Wellness <span className="text-amber-500 dark:text-amber-400">Schedule</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Follow our carefully designed daily routine to maximize the benefits of your Ayurvedic wellness journey.
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-amber-200 dark:bg-indigo-900"></div>
              <div className="space-y-12">
                {dailySchedule.map((item, index) => (
                  <div key={index} className="relative pl-12">
                    <div className="absolute left-0 top-0 w-10 h-10 rounded-full bg-amber-100 dark:bg-indigo-900/30 flex items-center justify-center text-amber-500 dark:text-amber-400">
                      {item.icon}
                    </div>
                    <div className="bg-white dark:bg-slate-800 rounded-lg p-5 shadow-md">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-bold text-slate-900 dark:text-white">{item.activity}</h3>
                        <span className="text-sm font-medium bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 px-2 py-1 rounded-full">
                          {item.time}
                        </span>
                      </div>
                      <p className="text-slate-600 dark:text-slate-300 text-sm">
                        {item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-slate-50 dark:bg-slate-800/30">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="inline-block">
              <div className="flex items-center mb-4 justify-center">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Success Stories
                </span>
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-12 ml-4"></div>
              </div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Client <span className="text-amber-500 dark:text-amber-400">Testimonials</span>
            </h2>
            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Hear from those who have experienced the transformative power of our Ayurvedic wellness programs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8 relative"
              >
                <div className="absolute -top-5 left-1/2 transform -translate-x-1/2 w-10 h-10 bg-amber-400 dark:bg-indigo-600 rounded-full flex items-center justify-center text-white">
                  <Star className="w-5 h-5" />
                </div>
                <div className="flex justify-center mb-6">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-20 h-20 rounded-full object-cover border-4 border-amber-100 dark:border-indigo-900/30"
                  />
                </div>
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < testimonial.rating
                          ? 'text-amber-400 dark:text-amber-400'
                          : 'text-slate-300 dark:text-slate-600'
                      }`}
                      fill={i < testimonial.rating ? 'currentColor' : 'none'}
                    />
                  ))}
                </div>
                <p className="text-slate-600 dark:text-slate-300 text-center mb-4 italic">
                  "{testimonial.quote}"
                </p>
                <div className="text-center">
                  <h4 className="font-bold text-slate-900 dark:text-white">{testimonial.name}</h4>
                  <p className="text-sm text-slate-500 dark:text-slate-400">{testimonial.location}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300">
              View All Testimonials
            </button>
          </div>
        </div>
      </section>

      {/* Contact/Booking Section */}
      <section className="py-20 bg-white dark:bg-slate-900">
        <div className="container mx-auto px-4 md:px-6">
          <div className="max-w-5xl mx-auto">
            <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-slate-800 dark:to-indigo-900/30 rounded-2xl p-8 md:p-12 shadow-xl">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div>
                  <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">
                    Begin Your Wellness Journey Today
                  </h2>
                  <p className="text-slate-600 dark:text-slate-300 mb-8">
                    Take the first step towards holistic health and balance. Our experienced practitioners
                    are ready to guide you on your personalized Ayurvedic wellness journey.
                  </p>
                  <div className="space-y-6 mb-8">
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-amber-400 dark:bg-indigo-600 flex items-center justify-center text-white mr-4 flex-shrink-0">
                        <Mail className="w-5 h-5" />
                      </div>
                      <div>
                        <h3 className="font-bold text-slate-900 dark:text-white">Email Us</h3>
                        <p className="text-slate-600 dark:text-slate-400 text-sm"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-amber-400 dark:bg-indigo-600 flex items-center justify-center text-white mr-4 flex-shrink-0">
                        <Phone className="w-5 h-5" />
                      </div>
                      <div>
                        <h3 className="font-bold text-slate-900 dark:text-white">Call Us</h3>
                        <p className="text-slate-600 dark:text-slate-400 text-sm">+91 9410706622</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-amber-400 dark:bg-indigo-600 flex items-center justify-center text-white mr-4 flex-shrink-0">
                        <MapPin className="w-5 h-5" />
                      </div>
                      <div>
                        <h3 className="font-bold text-slate-900 dark:text-white">Visit Us</h3>
                        <p className="text-slate-600 dark:text-slate-400 text-sm">Jonk Village, Swargashram, Rishikesh</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-6">
                    Book a Consultation
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Your Name
                      </label>
                      <input
                        type="text"
                        className="w-full px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-600 focus:border-transparent dark:bg-slate-700"
                        placeholder="Enter your name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Email Address
                      </label>
                      <input
                        type="email"
                        className="w-full px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-600 focus:border-transparent dark:bg-slate-700"
                        placeholder="Enter your email"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        className="w-full px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-600 focus:border-transparent dark:bg-slate-700"
                        placeholder="Enter your phone number"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        Message
                      </label>
                      <textarea
                        className="w-full px-4 py-2 border border-slate-300 dark:border-slate-600 rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-600 focus:border-transparent dark:bg-slate-700 h-24"
                        placeholder="Tell us about your wellness goals"
                      ></textarea>
                    </div>
                    <button className="w-full py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300">
                      Submit Request
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default WellnessServicesPage;