import React, { useState } from 'react';
import { Book, Video, FileText, Link as LinkIcon, ExternalLink, Search } from 'lucide-react';

interface Resource {
  id: number;
  title: string;
  type: 'book' | 'video' | 'article' | 'link';
  description: string;
  author?: string;
  url?: string;
  tags: string[];
  image?: string;
}

const ResourcesPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  
  const resources: Resource[] = [
    {
      id: 1,
      title: "Light on Yoga",
      type: "book",
      author: "B.K.S. Iyengar",
      description: "The definitive guide to the philosophy and practice of Yoga--the ancient healing discipline for body and mind--by its greatest living teacher.",
      tags: ["asana", "philosophy", "beginner", "reference"],
      image: "https://placehold.co/300x400/e2e8f0/1e293b?text=Light+on+Yoga"
    },
    {
      id: 2,
      title: "The Heart of Yoga",
      type: "book",
      author: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      description: "The first yoga text to outline a step-by-step sequence for developing a complete practice according to viniyoga--yoga adapted to the needs of the individual.",
      tags: ["philosophy", "practice", "viniyoga"],
      image: "https://placehold.co/300x400/e2e8f0/1e293b?text=Heart+of+Yoga"
    },
    {
      id: 3,
      title: "30-Minute Vinyasa Flow for Beginners",
      type: "video",
      description: "A gentle yet effective vinyasa flow sequence designed for beginners to build strength and flexibility.",
      author: "Shanti Yoga",
      url: "https://example.com/videos/beginner-vinyasa",
      tags: ["vinyasa", "beginner", "short-practice"],
      image: "https://placehold.co/640x360/e2e8f0/1e293b?text=Vinyasa+Flow"
    },
    {
      id: 4,
      title: "Meditation for Stress Reduction",
      type: "video",
      description: "A guided 15-minute meditation practice specifically designed to reduce stress and anxiety.",
      author: "Shanti Yoga",
      url: "https://example.com/videos/stress-meditation",
      tags: ["meditation", "stress-relief", "beginner"],
      image: "https://placehold.co/640x360/e2e8f0/1e293b?text=Meditation"
    },
    {
      id: 5,
      title: "The Science Behind Yoga's Benefits",
      type: "article",
      description: "Explore the latest scientific research on how yoga affects the body and mind, from improved flexibility to stress reduction.",
      author: "Dr. Maya Patel",
      url: "https://example.com/articles/yoga-science",
      tags: ["science", "research", "benefits"],
      image: "https://placehold.co/800x400/e2e8f0/1e293b?text=Yoga+Science"
    },
    {
      id: 6,
      title: "Understanding the Yoga Sutras",
      type: "article",
      description: "A deep dive into Patanjali's Yoga Sutras and how they can be applied to modern practice and life.",
      author: "Dr. Raj Sharma",
      url: "https://example.com/articles/yoga-sutras",
      tags: ["philosophy", "sutras", "patanjali"],
      image: "https://placehold.co/800x400/e2e8f0/1e293b?text=Yoga+Sutras"
    },
    {
      id: 7,
      title: "Yoga Journal",
      type: "link",
      description: "One of the most comprehensive resources for yoga poses, sequences, philosophy, and lifestyle.",
      url: "https://www.yogajournal.com",
      tags: ["magazine", "poses", "lifestyle"],
      image: "https://placehold.co/800x400/e2e8f0/1e293b?text=Yoga+Journal"
    },
    {
      id: 8,
      title: "Yoga Alliance",
      type: "link",
      description: "The largest nonprofit association representing the yoga community, with resources for teachers and practitioners.",
      url: "https://www.yogaalliance.org",
      tags: ["certification", "standards", "directory"],
      image: "https://placehold.co/800x400/e2e8f0/1e293b?text=Yoga+Alliance"
    }
  ];
  
  // Filter resources based on search term and selected type
  const filteredResources = resources.filter((resource) => {
    const matchesSearch = resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (resource.author && resource.author.toLowerCase().includes(searchTerm.toLowerCase())) ||
                          resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = selectedType === 'all' || resource.type === selectedType;
    
    return matchesSearch && matchesType;
  });
  
  // Get all unique tags
  const allTags = Array.from(new Set(resources.flatMap(r => r.tags))).sort();
  
  // Icon mapping for resource types
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'book':
        return <Book className="w-5 h-5" />;
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'article':
        return <FileText className="w-5 h-5" />;
      case 'link':
        return <LinkIcon className="w-5 h-5" />;
      default:
        return null;
    }
  };
  
  return (
    <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
      <div className="bg-gradient-to-b from-amber-400/10 to-transparent dark:from-indigo-900/20 dark:to-transparent py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-6xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Yoga <span className="text-amber-500 dark:text-amber-400">Resources</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Explore our curated collection of books, videos, articles, and links to deepen your yoga practice and knowledge.
          </p>
        </div>
      </div>
      
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="md:w-64 flex-shrink-0">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg sticky top-24">
              {/* Search */}
              <div className="mb-6">
                <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">Search</h3>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search resources..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  />
                  <Search className="absolute left-3 top-2.5 w-4 h-4 text-slate-400" />
                </div>
              </div>
              
              {/* Type Filter */}
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">Resource Type</h3>
              <div className="space-y-2 mb-6">
                {['all', 'book', 'video', 'article', 'link'].map((type) => (
                  <button
                    key={type}
                    onClick={() => setSelectedType(type)}
                    className={`w-full text-left px-4 py-2 rounded-lg transition-all flex items-center ${
                      selectedType === type
                        ? 'bg-amber-400/10 dark:bg-indigo-600/20 text-amber-600 dark:text-amber-400 font-medium'
                        : 'hover:bg-slate-100 dark:hover:bg-slate-700/50 text-slate-700 dark:text-slate-300'
                    }`}
                  >
                    {type !== 'all' && (
                      <span className="mr-2 text-amber-500 dark:text-amber-400">
                        {getTypeIcon(type)}
                      </span>
                    )}
                    {type === 'all' ? 'All Resources' : type.charAt(0).toUpperCase() + type.slice(1) + 's'}
                  </button>
                ))}
              </div>
              
              {/* Popular Tags */}
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-4">Popular Tags</h3>
              <div className="flex flex-wrap gap-2">
                {allTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => setSearchTerm(tag)}
                    className="px-3 py-1 text-xs rounded-full bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-indigo-900/30 transition-colors"
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Resources Grid */}
          <div className="flex-1">
            {filteredResources.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredResources.map((resource) => (
                  <div 
                    key={resource.id}
                    className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col"
                  >
                    {resource.image && (
                      <div className="relative aspect-video overflow-hidden">
                        <img 
                          src={resource.image} 
                          alt={resource.title} 
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute top-3 right-3 px-2 py-1 rounded-full bg-white/90 dark:bg-slate-800/90 text-amber-500 dark:text-amber-400 text-xs font-medium flex items-center">
                          {getTypeIcon(resource.type)}
                          <span className="ml-1">{resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="p-6 flex-1 flex flex-col">
                      <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">{resource.title}</h3>
                      {resource.author && (
                        <p className="text-sm text-amber-600 dark:text-amber-400 mb-3">By {resource.author}</p>
                      )}
                      <p className="text-slate-600 dark:text-slate-300 text-sm mb-4">{resource.description}</p>
                      
                      <div className="flex flex-wrap gap-2 mb-4">
                        {resource.tags.slice(0, 3).map((tag, index) => (
                          <span 
                            key={index}
                            className="text-xs bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 px-2 py-1 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                        {resource.tags.length > 3 && (
                          <span className="text-xs text-slate-500 dark:text-slate-400 px-2 py-1">
                            +{resource.tags.length - 3} more
                          </span>
                        )}
                      </div>
                      
                      {resource.url && (
                        <a 
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="mt-auto flex items-center justify-center px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm"
                        >
                          <ExternalLink className="w-4 h-4 mr-2" />
                          {resource.type === 'video' ? 'Watch Video' : 
                           resource.type === 'article' ? 'Read Article' :
                           resource.type === 'link' ? 'Visit Website' : 'View Resource'}
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white dark:bg-slate-800 rounded-xl p-8 text-center">
                <p className="text-slate-700 dark:text-slate-300">No resources found matching your criteria.</p>
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setSelectedType('all');
                  }}
                  className="mt-4 px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm"
                >
                  Reset Filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResourcesPage;