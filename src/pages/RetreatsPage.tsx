import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Calendar, Clock, MapPin, Users, Heart, Leaf } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2 } from 'lucide-react';
import { Retreat } from '../data/data';

const RetreatsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('all');

  const [retreats, setRetreats] = useState<Retreat[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  React.useEffect(() => {
    try {
      // Import data directly from data.ts
      import('../data/data').then(({ retreats }) => {
        setRetreats(retreats);
      });
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  const filteredRetreats = activeTab === 'all'
    ? retreats
    : retreats.filter((r) => r.theme.toLowerCase() === activeTab.toLowerCase());

  if (loading) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-slate-600 dark:text-slate-300">Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-red-600 dark:text-red-400">Error: {error}</p>
      </div>
    );
  }

  const groupBookingSchema = z.object({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    groupType: z.string().min(1, 'Please select a group type'),
    requirements: z.string().min(20, 'Please provide more details about your requirements')
  });

  type GroupBookingFormData = z.infer<typeof groupBookingSchema>;

  const GroupBookingSection = () => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<{
      type: 'success' | 'error';
      message: string;
    } | null>(null);

    const {
      register,
      handleSubmit,
      reset,
      formState: { errors }
    } = useForm<GroupBookingFormData>({
      resolver: zodResolver(groupBookingSchema)
    });

    const onSubmit = async (data: GroupBookingFormData) => {
      setIsSubmitting(true);
      try {
        const response = await fetch('https://hub.rksh.in/mailer/mailer.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'group-booking',
            ...data
          })
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to submit inquiry');
        }

        setSubmitStatus({
          type: 'success',
          message: 'Thank you for your inquiry! We will get back to you soon.'
        });

        reset();
      } catch (error) {
        setSubmitStatus({
          type: 'error',
          message: error instanceof Error ? error.message : 'Failed to submit inquiry. Please try again later.'
        });
      } finally {
        setIsSubmitting(false);
        setTimeout(() => setSubmitStatus(null), 5000);
      }
    };

    return (
      <div className=" bg-white dark:bg-slate-800 rounded-xl shadow-lg p-8">
        <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-6">Group Booking Inquiry</h3>

        {submitStatus && (
          <div className={`p-4 mb-6 rounded-lg ${
            submitStatus.type === 'success'
              ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-300'
              : 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-300'
          }`}>
            {submitStatus.message}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Your Name
              </label>
              <input
                type="text"
                id="name"
                {...register('name')}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                  errors.name ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
                }`}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name.message}</p>
              )}
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                {...register('email')}
                className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                  errors.email ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
                }`}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email.message}</p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="groupType" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Group Type
            </label>
            <select
              id="groupType"
              {...register('groupType')}
              className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                errors.groupType ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
              }`}
            >
              <option value="">Select group type</option>
              <option value="corporate">Corporate Wellness</option>
              <option value="studio">Yoga Studio</option>
              <option value="custom">Custom Program</option>
            </select>
            {errors.groupType && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.groupType.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="requirements" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
              Your Requirements
            </label>
            <textarea
              id="requirements"
              {...register('requirements')}
              rows={4}
              className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${
                errors.requirements ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'
              }`}
              placeholder="Tell us about your group size, preferred dates, interests, and any special requirements..."
            ></textarea>
            {errors.requirements && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.requirements.message}</p>
            )}
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full py-3 px-4 flex items-center justify-center rounded-lg font-medium transition-colors duration-300 ${
              isSubmitting
                ? 'bg-amber-300 dark:bg-indigo-500 cursor-not-allowed'
                : 'bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700'
            } text-slate-900 dark:text-white`}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit Inquiry'
            )}
          </button>
        </form>
      </div>
    );
  };

  return (
    <div className="pt-18">
      <Helmet>
        <title>Yoga Retreats in Rishikesh | Shanti Yog Peeth</title>
        <meta name="description" content="Find your perfect yoga retreat in Rishikesh. Explore meditation, wellness, and adventure retreats at Shanti Yog Peeth for a transformative experience." />
        <meta property="og:title" content="Yoga Retreats in Rishikesh | Shanti Yog Peeth" />
        <meta property="og:description" content="Find your perfect yoga retreat in Rishikesh. Explore meditation, wellness, and adventure retreats at Shanti Yog Peeth for a transformative experience." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://yourdomain.com/retreats" />
      </Helmet>
      <div className="bg-amber-400/10 dark:bg-indigo-900/20 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Find Your Perfect <span className="text-amber-500 dark:text-amber-400">Yoga Retreat in Rishikesh</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Escape to the Himalayas for transformative yoga and meditation retreats.  Discover authentic ashram experiences designed for wellness travelers and spiritual seekers.
          </p>
        </div>
      </div>

      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-wrap justify-center gap-2 mb-12">
            <button
              onClick={() => setActiveTab('all')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'all'
                ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
            >
              All Retreats
            </button>
            <button
              onClick={() => setActiveTab('meditation')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'meditation'
                ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
            >
              Meditation
            </button>
            <button
              onClick={() => setActiveTab('ayurveda')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'ayurveda'
                ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
            >
              Ayurveda
            </button>
            <button
              onClick={() => setActiveTab('adventure')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'adventure'
                ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
            >
              Adventure
            </button>
            <button
              onClick={() => setActiveTab('wellness')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'wellness'
                ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
            >
              Wellness
            </button>
            <button
              onClick={() => setActiveTab('women')}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${activeTab === 'women'
                ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-md'
                : 'bg-white/80 dark:bg-slate-700/80 text-slate-700 dark:text-slate-200 hover:bg-amber-100 dark:hover:bg-slate-600'
                }`}
            >
              Women's Retreats
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredRetreats.map((retreat) => {
              // Format duration
              const durationDisplay = `${retreat.duration} Days`;

              // Format price (show shared if available, else first key)
              let priceDisplay = '';
              if (retreat.price) {
                if (retreat.price.shared) {
                  priceDisplay = `$${retreat.price.shared}`;
                } else {
                  const firstKey = Object.keys(retreat.price)[0];
                  priceDisplay = `$${retreat.price[firstKey]}`;
                }
              }

              // Format dates (show as "05 January 2025")
              const formatDate = (dateStr: string) => {
                const [day, month, year] = dateStr.split('/').map(Number);
                const dateObj = new Date(year, month - 1, day);
                return dateObj.toLocaleDateString('en-GB', {
                  day: '2-digit',
                  month: 'long',
                  year: 'numeric'
                });
              };

              return (
                <div
                  key={retreat.id}
                  className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 flex flex-col"
                >
                  <div className="relative">
                    <img
                      src={retreat.image}
                      alt={retreat.title}
                      className="w-full h-64 object-cover"
                    />
                    <div className="absolute top-4 right-4 bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white text-xs font-bold px-3 py-1 rounded-full">
                      {retreat.theme}
                    </div>
                  </div>

                  <div className="p-6 flex-grow">
                    <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">{retreat.title}</h3>

                    <div className="flex flex-wrap gap-4 mb-4">
                      <div className="flex items-center text-slate-600 dark:text-slate-400 text-sm">
                        <Clock className="w-4 h-4 mr-1" />
                        {durationDisplay}
                      </div>
                      <div className="flex items-center text-slate-600 dark:text-slate-400 text-sm">
                        <MapPin className="w-4 h-4 mr-1" />
                        {retreat.location}
                      </div>
                      <div className="flex items-center text-slate-600 dark:text-slate-400 text-sm">
                        <Users className="w-4 h-4 mr-1" />
                        Max {retreat.capacity}
                      </div>
                    </div>

                    <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-3">
                      {retreat.description}
                    </p>

                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-2">Upcoming Dates:</h4>
                      <div className="space-y-1">
                        {retreat.dates.slice(0, 2).map((date, index) => (
                          <div
                            key={index}
                            className="flex items-center text-sm text-slate-600 dark:text-slate-400"
                          >
                            <Calendar className="w-4 h-4 mr-2 text-amber-500 dark:text-amber-400" />
                            {formatDate(date)}
                          </div>
                        ))}
                        {retreat.dates.length > 2 && (
                          <div className="text-sm text-amber-500 dark:text-amber-400">
                            +{retreat.dates.length - 2} more dates
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {retreat.highlights.slice(0, 3).map((highlight, index) => (
                        <span
                          key={index}
                          className="text-xs bg-amber-50 dark:bg-slate-700 text-amber-800 dark:text-amber-300 px-2 py-1 rounded-full flex items-center"
                        >
                          <Leaf className="w-3 h-3 mr-1" />
                          {highlight}
                        </span>
                      ))}
                      {retreat.highlights.length > 3 && (
                        <span className="text-xs bg-amber-50 dark:bg-slate-700 text-amber-800 dark:text-amber-300 px-2 py-1 rounded-full">
                          +{retreat.highlights.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="p-6 mt-auto border-t border-slate-100 dark:border-slate-700">
                    <div className="flex justify-between items-center space-x-4">
                      <span className="text-xl font-bold text-amber-500 dark:text-amber-400">
                        {priceDisplay}
                      </span>
                      <div className="flex space-x-2">
                        <Link
                          to={`/retreats/${retreat.slug}`}
                          className="px-4 py-2 bg-white hover:bg-slate-50 dark:bg-slate-700 dark:hover:bg-slate-600 text-amber-500 dark:text-amber-400 border border-amber-500 dark:border-amber-400 rounded-lg font-medium transition-colors duration-300 text-sm"
                        >
                          Learn More
                        </Link>
                        <Link
                          to="/apply"
                          className="px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm"
                        >
                          Book Now
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      <section className="py-16 bg-amber-50/50 dark:bg-slate-800/30">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
              The <span className="text-amber-500 dark:text-amber-400">Retreat Experience: Your Journey to Wellness</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
              Our yoga and meditation retreats in Rishikesh offer a unique opportunity to disconnect from daily life and reconnect with your true self in the spiritual heart of yoga.  Experience the peace of an authentic ashram.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white dark:bg-slate-800 p-8 rounded-xl shadow-md">
              <div className="w-16 h-16 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mb-6">
                <Heart className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Authentic Practices</h3>
              <p className="text-slate-600 dark:text-slate-300">
                Experience traditional yoga and meditation practices taught by teachers who embody the true essence of yogic tradition.
              </p>
            </div>

            <div className="bg-white dark:bg-slate-800 p-8 rounded-xl shadow-md">
              <div className="w-16 h-16 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mb-6">
                <MapPin className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Sacred Location</h3>
              <p className="text-slate-600 dark:text-slate-300">
                Nestled in the foothills of the Himalayas along the sacred Ganges River in Rishikesh, our retreat centers offer the perfect environment for transformation.
              </p>
            </div>

            <div className="bg-white dark:bg-slate-800 p-8 rounded-xl shadow-md">
              <div className="w-16 h-16 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mb-6">
                <Users className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Intimate Groups</h3>
              <p className="text-slate-600 dark:text-slate-300">
                We limit our retreat sizes to ensure personalized attention and to foster a sense of community among participants.
              </p>
            </div>

            <div className="bg-white dark:bg-slate-800 p-8 rounded-xl shadow-md">
              <div className="w-16 h-16 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400 mb-6">
                <Leaf className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Holistic Approach</h3>
              <p className="text-slate-600 dark:text-slate-300">
                Our retreats address all aspects of well-being—physical, mental, emotional, and spiritual—for a truly transformative experience.  Enjoy personalized yoga retreats tailored to your needs.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-xl">
            <div className="grid grid-cols-1 md:grid-cols-2">
              <div className="relative">
                <img
                  src="https://placehold.co/800x600/e2e8f0/1e293b?text=Custom+Retreats"
                  alt="Custom group retreats"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent flex items-center">
                  <div className="p-8">
                    <h3 className="text-2xl font-bold text-white mb-4">Custom Group Retreats in Rishikesh</h3>
                    <p className="text-white/90 mb-6">
                      Looking for a private yoga or meditation retreat experience for your organization, studio, or group of friends in Rishikesh?
                    </p>
                    <ul className="space-y-2 text-white/90">
                      <li className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-amber-400 mr-2 flex items-center justify-center text-slate-900 text-xs">✓</div>
                        Customized curriculum
                      </li>
                      <li className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-amber-400 mr-2 flex items-center justify-center text-slate-900 text-xs">✓</div>
                        Flexible dates
                      </li>
                      <li className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-amber-400 mr-2 flex items-center justify-center text-slate-900 text-xs">✓</div>
                        Tailored activities
                      </li>
                      <li className="flex items-center">
                        <div className="w-5 h-5 rounded-full bg-amber-400 mr-2 flex items-center justify-center text-slate-900 text-xs">✓</div>
                        Group pricing
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="p-8 md:p-12">
                <div className="flex items-center mb-4">
                  <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
                  <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Plan Your Retreat</span>
                </div>

                <h2 className="text-3xl font-bold mb-6 text-slate-900 dark:text-white">
                  Create Your <span className="text-amber-500 dark:text-amber-400">Perfect Yoga or Meditation Retreat</span>
                </h2>

                <p className="text-slate-700 dark:text-slate-300 mb-8">
                  Whether you're looking for a personal yoga retreat experience or want to organize a custom group retreat in Rishikesh, our team can help create the perfect program tailored to your needs and goals.
                </p>

                <GroupBookingSection />
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default RetreatsPage;
