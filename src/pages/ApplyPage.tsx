import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Clock, Award, Mail, Phone, User, BookOpen, Loader2 } from 'lucide-react';
import { useSearchParams } from 'react-router-dom';

interface Price {
  shared: string;
  private: string;
  sharedTwin: string;
}

interface ServiceWithDates {
  id: string | number;
  title: string;
  dates: string[];
  duration?: number;
  capacity?: number;
  level?: string;
  description?: string;
  price?: Price;
  active?: boolean;
}

interface ServiceWithoutDates {
  id: string | number;
  title: string;
  duration?: number;
  capacity?: number;
  level?: string;
  description?: string;
  price?: Price;
  active?: boolean;
}

type Service = ServiceWithDates | ServiceWithoutDates;


function hasServiceDates(service: Service | undefined): service is ServiceWithDates {
  return !!service && 'dates' in service && Array.isArray(service.dates);
}

function formatDate(dateStr: string): string {
  const [day, month, year] = dateStr.split('/').map(Number);
  const date = new Date(year, month - 1, day);
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
}

// Validation schema
const applyFormSchema = z.object({
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(8, 'Please enter a valid phone number with country code'),
  gender: z.enum(['male', 'female', 'other'], { required_error: 'Please select your gender' }),
  dob: z.string().min(4, 'Please select your date of birth'),
  country: z.string().min(2, 'Please enter your country'),
  serviceType: z.string().min(1, 'Please select a service type'),
  serviceId: z.string().min(1, 'Please select a service'),
  startDate: z.string().optional(),
  preferredDateTime: z.string().optional(),
  yogaExperience: z.string().min(50, 'Please provide more detail about your yoga experience'),
  teachingExperience: z.string().optional(),
  expectations: z.string().min(50, 'Please provide more detail about your expectations'),
  dietaryRestrictions: z.string().optional(),
  medicalConditions: z.string().optional()
});

type ApplyFormData = z.infer<typeof applyFormSchema>;

const ApplyPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitStatus, setSubmitStatus] = React.useState<{ type: 'success' | 'error'; message: string } | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors }
  } = useForm<ApplyFormData>({
    resolver: zodResolver(applyFormSchema),
    defaultValues: {
      serviceType: searchParams.get('type') || '',
      serviceId: searchParams.get('service') || ''
    }
  });

  const selectedServiceType = watch('serviceType');
  const selectedServiceId = watch('serviceId');

  const [courses, setCourses] = React.useState<Service[]>([]);
  const [retreats, setRetreats] = React.useState<Service[]>([]);
  const [selectedPackage, setSelectedPackage] = React.useState<string>('');

  React.useEffect(() => {
    try {
      // Import data directly from data.ts
      import('../data/data').then(({ courses, retreats }) => {
        // Filter inactive courses and retreats
        setCourses(courses.filter((c) => c.active !== false));
        setRetreats(retreats.filter((r) => r.active !== false));
      });
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }, []);

  const servicesData: Record<string, Service[]> = {
    Courses: courses,
    Retreats: retreats,
  };

  const servicesList = selectedServiceType ? servicesData[selectedServiceType] : [];
  const selectedService = servicesList.find((s: Service) => s.id === Number(selectedServiceId) || s.id === selectedServiceId);
  const hasDates = hasServiceDates(selectedService);

  const onSubmit = async (data: ApplyFormData) => {
    if (!selectedPackage) {
      alert('Please select a package option.');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('https://hub.rksh.in/mailer/mailer.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'application',
          selectedPackage,
          ...data,
          programTitle: selectedService?.title || '',
          programType: selectedServiceType || '',
          courseId: data.serviceId // for backward compatibility
        })
      });
      const result = await response.json();
      if (!response.ok) throw new Error(result.error || 'Failed to submit application');
      setSubmitStatus({ type: 'success', message: 'Thank you for your application! We will review it and get back to you soon.' });
      reset();
      setSelectedPackage('');
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } catch (error) {
      setSubmitStatus({ type: 'error', message: error instanceof Error ? error.message : 'Failed to submit application. Please try again later.' });
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus(null), 5000);
    }
  };

  return (
    <div className="pt-16">
      <div className="bg-amber-400/10 dark:bg-indigo-900/20 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Apply for <span className="text-amber-500 dark:text-amber-400">Our Programs</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Take the first step in your journey. Fill out the application form below to join our upcoming programs and services.
          </p>
        </div>
      </div>

      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          {submitStatus && (
            <div className={`max-w-4xl mx-auto mb-8 p-4 rounded-lg ${
              submitStatus.type === 'success'
                ? 'bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-300'
                : 'bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-300'
            }`}>
              {submitStatus.message}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl mx-auto">
            <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
              <div className="p-8">
                <div className="space-y-8">
                  {/* Personal Information */}
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6 flex items-center">
                      <User className="w-6 h-6 mr-2 text-amber-500 dark:text-amber-400" />
                      Personal Information
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Row 1: Full Name, Email, DOB */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="fullName">Full Name</label>
                        <input type="text" id="fullName" {...register('fullName')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.fullName ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} />
                        {errors.fullName && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.fullName.message}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="email">Email</label>
                        <input type="email" id="email" {...register('email')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.email ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} />
                        {errors.email && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email.message}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="dob">Date of Birth</label>
                        <input type="date" id="dob" {...register('dob')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.dob ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} />
                        {errors.dob && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.dob.message}</p>}
                      </div>
                      {/* Row 2: Phone, Country, Gender */}
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="phone">Mobile/WhatsApp (with country code)</label>
                        <input type="tel" id="phone" {...register('phone')} placeholder="e.g. +91 9876543210" className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.phone ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} />
                        {errors.phone && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone.message}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="country">Country</label>
                        <input type="text" id="country" {...register('country')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.country ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} />
                        {errors.country && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.country.message}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="gender">Gender</label>
                        <select id="gender" {...register('gender')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.gender ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`}>
                          <option value="">Select gender</option>
                          <option value="male">Male</option>
                          <option value="female">Female</option>
                          <option value="other">Other</option>
                        </select>
                        {errors.gender && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.gender.message}</p>}
                      </div>
                    </div>
                  </div>

                  {/* Service Selection */}
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6 flex items-center">
                      <BookOpen className="w-6 h-6 mr-2 text-amber-500 dark:text-amber-400" />
                      Program Selection
                    </h2>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="serviceType">Type of Service</label>
                          <select id="serviceType" {...register('serviceType')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.serviceType ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`}>
                            <option value="">Select type</option>
                            {Object.keys(servicesData).map(type => (
                              <option key={type} value={type}>{type}</option>
                            ))}
                          </select>
                          {errors.serviceType && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.serviceType.message}</p>}
                        </div>
                        {selectedServiceType && (
                          <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="serviceId">Select Service</label>
                            <select id="serviceId" {...register('serviceId')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.serviceId ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`}>
                              <option value="">Select a service</option>
                              {servicesList.map(service => (
                                <option key={service.id} value={service.id}>{service.title}</option>
                              ))}
                            </select>
                            {errors.serviceId && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.serviceId.message}</p>}
                          </div>
                        )}
                        {selectedService && hasDates && (
                          <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="startDate">Preferred Start Date</label>
                            <select id="startDate" {...register('startDate')} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.startDate ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`}>
                              <option value="">Select a start date</option>
                              {selectedService && hasServiceDates(selectedService) && selectedService.dates.map((date, idx) => (
                                <option key={idx} value={date}>{formatDate(date)}</option>
                              ))}
                            </select>
                            {errors.startDate && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.startDate.message}</p>}
                          </div>
                        )}
                        {selectedService && !hasDates && (
                          <div>
                            <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="preferredDateTime">Preferred Date/Time (Optional)</label>
                            <input type="text" id="preferredDateTime" {...register('preferredDateTime')} placeholder="e.g., Next weekend, evenings, etc." className="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white" />
                          </div>
                        )}
                      </div>

                      {selectedService && (
  <div className="mt-4 p-6 bg-amber-50 dark:bg-slate-700/30 rounded-lg border border-amber-400 dark:border-amber-400">
    {/* Service Title */}
    <h4 className="text-lg font-semibold text-amber-700 dark:text-amber-300 mb-2">{selectedService.title}</h4>
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div>
        <div className="text-sm text-amber-600 dark:text-amber-400 font-medium mb-1">Duration</div>
        <div className="text-slate-900 dark:text-white">{selectedService.duration} Days</div>
      </div>
      <div>
        <div className="text-sm text-amber-600 dark:text-amber-400 font-medium mb-1">Capacity</div>
        <div className="text-slate-900 dark:text-white">{selectedService.capacity} Students</div>
      </div>
      <div>
        <div className="text-sm text-amber-600 dark:text-amber-400 font-medium mb-1">Level</div>
        <div className="text-slate-900 dark:text-white">{selectedService.level}</div>
      </div>
      <div>
        <div className="text-sm text-amber-600 dark:text-amber-400 font-medium mb-1">Location</div>
        <div className="text-slate-900 dark:text-white">Rishikesh, India</div>
      </div>
    </div>
    <div className="mt-4">
      <div className="text-sm text-amber-600 dark:text-amber-400 font-medium mb-1">Course Overview</div>
      <p className="text-slate-700 dark:text-slate-300 text-sm line-clamp-2">{selectedService.description}</p>
    </div>
  </div>
)}

                      {selectedService && selectedService.price && (
                        <div className="mt-8">
                          <h3 className="text-xl font-semibold mb-4 text-slate-900 dark:text-white">Available Packages</h3>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {/* Package 1 */}
                            <div
                              className={`p-4 rounded-lg shadow flex flex-col items-center cursor-pointer transition border-2
  ${selectedPackage === `Shared Twin (1 Person) - $ ${selectedService.price?.shared}`
    ? 'border-amber-500 bg-amber-50 dark:bg-slate-700/50'
    : 'border-slate-500 dark:border-slate-200 bg-white dark:bg-slate-800'
  }`}
                              onClick={() =>
                                setSelectedPackage(
                                  `Shared Twin (1 Person) - $ ${selectedService.price?.shared}`
                                )
                              }
                            >
                              <div className="mb-2 flex space-x-1">
                                <div className="w-6 h-6 border border-slate-400 dark:border-slate-500 flex items-center justify-center rounded">
                                  <div className="w-4 h-2 bg-slate-400 dark:bg-slate-500"></div>
                                </div>
                                <div className="w-6 h-6 border border-slate-400 dark:border-slate-500 flex items-center justify-center rounded opacity-30">
                                  <div className="w-4 h-2 bg-slate-400 dark:bg-slate-500"></div>
                                </div>
                              </div>
                              <div className="font-medium text-slate-800 dark:text-slate-200 text-center">Shared Twin (1 Person)</div>
                              <div className="mt-1 text-slate-600 dark:text-slate-400">$ {selectedService.price?.shared}</div>
                            </div>

                            {/* Package 2 */}
                            <div
                              className={`p-4 rounded-lg shadow flex flex-col items-center cursor-pointer transition border-2
  ${selectedPackage === `Private Room (1 Person) - $ ${selectedService.price?.private}`
    ? 'border-amber-500 bg-amber-50 dark:bg-slate-700/50'
    : 'border-slate-500 dark:border-slate-200 bg-white dark:bg-slate-800'
  }`}
                              onClick={() =>
                                setSelectedPackage(
                                  `Private Room (1 Person) - $ ${selectedService.price?.private}`
                                )
                              }
                            >
                              <div className="mb-2 flex justify-center">
                                <div className="w-8 h-8 border border-slate-400 dark:border-slate-500 flex items-center justify-center rounded">
                                  <div className="w-6 h-3 bg-slate-400 dark:bg-slate-500"></div>
                                </div>
                              </div>
                              <div className="font-medium text-slate-800 dark:text-slate-200 text-center">Private Room (1 Person)</div>
                              <div className="mt-1 text-slate-600 dark:text-slate-400">$ {selectedService.price?.private}</div>
                            </div>

                            {/* Package 3 */}
                            <div
                              className={`p-4 rounded-lg shadow flex flex-col items-center cursor-pointer transition border-2
  ${selectedPackage === `Shared Twin (2 Persons) - $ ${selectedService.price?.sharedTwin}`
    ? 'border-amber-500 bg-amber-50 dark:bg-slate-700/50'
    : 'border-slate-500 dark:border-slate-200 bg-white dark:bg-slate-800'
  }`}
                              onClick={() =>
                                setSelectedPackage(
                                  `Shared Twin (2 Persons) - $ ${selectedService.price?.sharedTwin}`
                                )
                              }
                            >
                              <div className="mb-2 flex space-x-1">
                                <div className="w-6 h-6 border border-slate-400 dark:border-slate-500 flex items-center justify-center rounded">
                                  <div className="w-4 h-2 bg-slate-400 dark:bg-slate-500"></div>
                                </div>
                                <div className="w-6 h-6 border border-slate-400 dark:border-slate-500 flex items-center justify-center rounded">
                                  <div className="w-4 h-2 bg-slate-400 dark:bg-slate-500"></div>
                                </div>
                              </div>
                              <div className="font-medium text-slate-800 dark:text-slate-200 text-center">Shared Twin (2 Persons)</div>
                              <div className="mt-1 text-slate-600 dark:text-slate-400">$ {selectedService.price?.sharedTwin}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Experience and Expectations */}
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6 flex items-center">
                      <Award className="w-6 h-6 mr-2 text-amber-500 dark:text-amber-400" />
                      Experience & Expectations
                    </h2>
                    <div className="space-y-6">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="yogaExperience">Yoga Experience</label>
                        <textarea id="yogaExperience" {...register('yogaExperience')} rows={4} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.yogaExperience ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} placeholder="Please describe your yoga practice, including style(s), duration, and frequency..." />
                        {errors.yogaExperience && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.yogaExperience.message}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="teachingExperience">Teaching Experience (Optional)</label>
                        <textarea id="teachingExperience" {...register('teachingExperience')} rows={4} className="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white" placeholder="If you have any teaching experience (any field), please describe it here..." />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="expectations">Expectations from the Program</label>
                        <textarea id="expectations" {...register('expectations')} rows={4} className={`w-full px-4 py-2 rounded-lg border focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white ${errors.expectations ? 'border-red-500' : 'border-slate-300 dark:border-slate-600'}`} placeholder="What do you hope to achieve through this program?" />
                        {errors.expectations && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.expectations.message}</p>}
                      </div>
                    </div>
                  </div>

                  {/* Health Information */}
                  <div>
                    <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6 flex items-center">
                      <Clock className="w-6 h-6 mr-2 text-amber-500 dark:text-amber-400" />
                      Health Information
                    </h2>
                    <div className="space-y-6">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="dietaryRestrictions">Dietary Restrictions (Optional)</label>
                        <textarea id="dietaryRestrictions" {...register('dietaryRestrictions')} rows={2} className="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white" placeholder="Please list any dietary restrictions or preferences..." />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-2" htmlFor="medicalConditions">Medical Conditions (Optional)</label>
                        <textarea id="medicalConditions" {...register('medicalConditions')} rows={2} className="w-full px-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white" placeholder="Please list any medical conditions or health concerns we should be aware of..." />
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <button type="submit" disabled={isSubmitting} className={`w-full py-3 px-4 flex items-center justify-center rounded-lg font-medium transition-colors duration-300 ${isSubmitting ? 'bg-amber-300 dark:bg-indigo-500 cursor-not-allowed' : 'bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700'} text-slate-900 dark:text-white`}>
                    {isSubmitting ? (<><Loader2 className="w-5 h-5 mr-2 animate-spin" /> Submitting Application...</>) : 'Submit Application'}
                  </button>
                </div>
              </div>
            </div>
          </form>

          <div className="mt-12 text-center">
            <p className="text-slate-700 dark:text-slate-300 mb-4">Have questions about the application process?</p>
            <div className="flex justify-center space-x-4">
              <a href="mailto:<EMAIL>" className="flex items-center text-amber-500 dark:text-amber-400 hover:underline">
                <Mail className="w-4 h-4 mr-2" /> <EMAIL>
              </a>
              <a href="tel:+919876543210" className="flex items-center text-amber-500 dark:text-amber-400 hover:underline">
                <Phone className="w-4 h-4 mr-2" /> +91 98765 43210
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ApplyPage;
