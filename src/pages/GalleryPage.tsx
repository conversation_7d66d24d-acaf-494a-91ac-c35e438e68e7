import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { Loader2, ChevronLeft, ChevronRight, X } from 'lucide-react';
import OmSymbol from '../components/OmSymbol';

interface GalleryImage {
  id: number;
  src: string;
  alt: string;
  category: 'meditation' | 'asana' | 'retreat' | 'teaching';
}

const GalleryPage: React.FC = () => {
  const [images] = useState<GalleryImage[]>([
    { id: 1, src: '/images/banner.jpeg', alt: 'Morning Meditation Session', category: 'meditation' },
    { id: 2, src: '/images/school.jpeg', alt: 'Yoga School Teaching', category: 'teaching' },
    { id: 3, src: '/images/banner.jpeg', alt: 'Sunrise Asana Practice', category: 'asana' },
    { id: 4, src: '/images/school.jpeg', alt: 'Himalayan Retreat', category: 'retreat' },
    { id: 5, src: '/images/banner.jpeg', alt: 'Group Meditation', category: 'meditation' },
    { id: 6, src: '/images/school.jpeg', alt: 'Advanced Asana Class', category: 'teaching' },
    { id: 7, src: '/images/banner.jpeg', alt: 'Meditation by the Ganges', category: 'meditation' },
    { id: 8, src: '/images/school.jpeg', alt: 'Teacher Training Session', category: 'teaching' },
    { id: 9, src: '/images/banner.jpeg', alt: 'Evening Yoga Flow', category: 'asana' }
  ]);
  
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState<'all' | GalleryImage['category']>('all');
  const [showOm, setShowOm] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  const rotate = useTransform(scrollYProgress, [0, 1], [0, 360]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [1, 1.2, 1]);

  const filteredImages = activeCategory === 'all' 
    ? images 
    : images.filter(img => img.category === activeCategory);

  const handleCategoryChange = (category: typeof activeCategory) => {
    if (category === activeCategory) return;
    setShowOm(true);
    setActiveCategory(category);
    setTimeout(() => setShowOm(false), 2000);
  };

  const navigateImage = useCallback((direction: 'next' | 'prev') => {
    if (!selectedImage) return;
    
    const currentIndex = filteredImages.findIndex(img => img.id === selectedImage.id);
    let newIndex;
    
    if (direction === 'next') {
      newIndex = currentIndex === filteredImages.length - 1 ? 0 : currentIndex + 1;
    } else {
      newIndex = currentIndex === 0 ? filteredImages.length - 1 : currentIndex - 1;
    }
    
    setSelectedImage(filteredImages[newIndex]);
  }, [selectedImage, filteredImages]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!selectedImage) return;

      switch (e.key) {
        case 'Escape':
          setSelectedImage(null);
          break;
        case 'ArrowRight':
          navigateImage('next');
          break;
        case 'ArrowLeft':
          navigateImage('prev');
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, navigateImage]);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1500);
    return () => clearTimeout(timer);
  }, []);

  const getCategoryCount = (category: string) => {
    if (category === 'all') return images.length;
    return images.filter(img => img.category === category).length;
  };

  const categories = [
    { id: 'all', label: 'All Moments' },
    { id: 'meditation', label: 'Meditation' },
    { id: 'asana', label: 'Asana Practice' },
    { id: 'teaching', label: 'Teaching' },
    { id: 'retreat', label: 'Retreats' }
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <motion.div
          initial={{ rotate: 0 }}
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <Loader2 className="w-12 h-12 text-amber-400" />
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 pt-20" ref={containerRef}>
      <OmSymbol isVisible={showOm} />
      {/* Spiritual Mandala Background */}
      <motion.div 
        className="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[200vh] h-[200vh] opacity-5 pointer-events-none"
        style={{ rotate, scale }}
      >
        <div className="w-full h-full bg-[url('/images/mandala.svg')] bg-contain bg-center bg-no-repeat" />
      </motion.div>

      {/* Gallery Header */}
      <div className="text-center mb-16 px-4">
        <motion.h1 
          className="text-4xl md:text-5xl font-bold text-slate-900 dark:text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Sacred Moments
        </motion.h1>
        <motion.p 
          className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Journey through visual expressions of peace, transformation, and inner awakening
        </motion.p>
      </div>

      {/* Category Filters */}
      <div className="container mx-auto px-4 mb-12">
        <motion.div className="flex flex-wrap justify-center gap-4">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => handleCategoryChange(category.id as any)}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 transform hover:scale-105 flex items-center gap-2
                ${activeCategory === category.id
                  ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-lg'
                  : 'bg-white/10 backdrop-blur-sm text-slate-700 dark:text-slate-300 hover:bg-white/20 dark:hover:bg-slate-800/50'
                }`}
            >
              <span>{category.label}</span>
              <motion.span
                key={getCategoryCount(category.id)}
                initial={{ scale: 0.5, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className={`inline-flex items-center justify-center w-6 h-6 text-xs rounded-full
                  ${activeCategory === category.id
                    ? 'bg-white/20 dark:bg-black/20'
                    : 'bg-slate-200 dark:bg-slate-700'
                  }`}
              >
                {getCategoryCount(category.id)}
              </motion.span>
            </button>
          ))}
        </motion.div>
      </div>

      {/* Gallery Grid */}
      <div className="container mx-auto px-4 pb-20">
        <AnimatePresence mode="wait">
          <motion.div 
            key={activeCategory}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {filteredImages.map((image, index) => (
              <motion.div
                key={image.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative aspect-[3/4] overflow-hidden rounded-2xl cursor-pointer group"
                onClick={() => setSelectedImage(image)}
              >
                <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 backdrop-blur-[2px] transition-all duration-500" />
                
                <div className="relative h-full">
                  <img
                    src={image.src}
                    alt={image.alt}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>

                <motion.div 
                  className="absolute bottom-0 left-0 right-0 p-6 text-white"
                  initial={{ y: 20, opacity: 0 }}
                  whileHover={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <h3 className="text-lg font-semibold mb-1">{image.alt}</h3>
                  <p className="text-sm opacity-90 capitalize flex items-center">
                    <span className="w-1.5 h-1.5 bg-amber-400 rounded-full mr-2"></span>
                    {image.category}
                  </p>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Image Modal with Fixed Navigation */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/95 backdrop-blur-sm z-50 flex items-center justify-center"
            onClick={() => setSelectedImage(null)}
          >
            <motion.div
              className="relative w-full h-full flex items-center justify-center p-4"
              onClick={e => e.stopPropagation()}
            >
              {/* Fixed Navigation Buttons */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('prev');
                }}
                className="fixed left-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors duration-300"
                aria-label="Previous image"
              >
                <ChevronLeft className="w-6 h-6 text-white" />
              </button>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  navigateImage('next');
                }}
                className="fixed right-4 top-1/2 -translate-y-1/2 w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors duration-300"
                aria-label="Next image"
              >
                <ChevronRight className="w-6 h-6 text-white" />
              </button>

              {/* Close Button */}
              <button
                onClick={() => setSelectedImage(null)}
                className="fixed top-4 right-4 w-12 h-12 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors duration-300"
                aria-label="Close modal"
              >
                <X className="w-6 h-6 text-white" />
              </button>

              {/* Image Container */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={selectedImage.id}
                  className="relative max-w-5xl w-full"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3 }}
                >
                  <img
                    src={selectedImage.src}
                    alt={selectedImage.alt}
                    className="w-full h-auto max-h-[85vh] object-contain rounded-lg"
                  />
                  <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 via-black/40 to-transparent rounded-b-lg">
                    <h3 className="text-xl font-semibold text-white mb-2">{selectedImage.alt}</h3>
                    <p className="text-white/80 capitalize">{selectedImage.category}</p>
                  </div>
                </motion.div>
              </AnimatePresence>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default GalleryPage;
