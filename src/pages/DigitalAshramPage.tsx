import React from 'react';
import { Helmet } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';
import { Clock, Wind, Leaf, Flame, Move } from 'lucide-react';
import ActivityCard from '../digitalashram/ActivityCard';
import activitiesData from '../data/activitiesData';

const DigitalAshramPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-amber-50 dark:from-slate-900 dark:to-slate-800 pt-0">
      <Helmet>
        <title>Digital Ashram | Shanti Yog <PERSON>eeth</title>
        <meta name="description" content="Experience interactive yoga and wellness tools in our Digital Ashram - meditation timers, breathing exercises, Ayurvedic profiles, and more." />
      </Helmet>

      {/* Hero Section */}
      <section className="relative py-28 md:py-28 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-indigo-500/10 to-amber-500/10 dark:from-indigo-900/30 dark:to-amber-900/20"></div>

        {/* Animated circles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-20 -left-20 w-96 h-96 bg-indigo-400/20 dark:bg-indigo-600/20 rounded-full blur-3xl animate-blob"></div>
          <div className="absolute top-1/3 -right-20 w-80 h-80 bg-amber-500/20 dark:bg-amber-500/20 rounded-full blur-3xl animate-blob animation-delay-2000"></div>
          <div className="absolute bottom-20 left-1/3 w-72 h-72 bg-purple-300/20 dark:bg-purple-700/20 rounded-full blur-3xl animate-blob animation-delay-4000"></div>
        </div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-slate-900 dark:text-white mb-6">
              Digital <span className="text-amber-500 dark:text-amber-400">Ashram</span>
            </h1>
            <p className="text-xl text-slate-700 dark:text-slate-300 mb-10 max-w-3xl mx-auto">
              Experience the transformative practices of yoga and Ayurveda through our interactive digital tools. Cultivate mindfulness, balance, and well-being from anywhere in the world.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <a
                href="#activities"
                className="px-8 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-full font-medium transition-all duration-300 transform hover:scale-105"
              >
                Explore Activities
              </a>
              <Link
                to="/about"
                className="px-8 py-3 bg-white/10 hover:bg-white/20 backdrop-blur-sm text-slate-900 dark:text-white border border-slate-300 dark:border-slate-600 rounded-full font-medium transition-all duration-300 transform hover:scale-105"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Activities Section */}
      <section id="activities" className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <h2 className="text-3xl font-bold text-center text-slate-900 dark:text-white mb-4">
            Interactive <span className="text-amber-500 dark:text-amber-400">Activities</span>
          </h2>
          <p className="text-lg text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto mb-16">
            Explore our collection of digital tools designed to support your yoga and wellness journey. Each activity offers a unique way to deepen your practice.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {activitiesData.map(activity => (
              <ActivityCard
                key={activity.id}
                title={activity.title}
                description={activity.description}
                icon={
                  activity.icon === 'Clock' ? <Clock className="w-8 h-8 text-white" /> :
                  activity.icon === 'Wind' ? <Wind className="w-8 h-8 text-white" /> :
                  activity.icon === 'Leaf' ? <Leaf className="w-8 h-8 text-white" /> :
                  activity.icon === 'Flame' ? <Flame className="w-8 h-8 text-white" /> :
                  activity.icon === 'Move' ? <Move className="w-8 h-8 text-white" /> :
                  <Leaf className="w-8 h-8 text-white" />
                }
                path={`/digitalashram/${activity.slug}`}
                bgColor={activity.bgColor}
                textColor={activity.textColor}
                borderColor={activity.borderColor}
                iconBgColor={activity.iconBgColor}
              />
            ))}
          </div>

          <div className="mt-16 text-center">
            <p className="text-slate-700 dark:text-slate-300 mb-6">
              We're constantly developing new tools to enhance your practice. Check back soon for more activities!
            </p>
            <Link
              to="/contact"
              className="inline-flex items-center px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 rounded-lg font-medium transition-all duration-300"
            >
              Suggest an Activity
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DigitalAshramPage;
