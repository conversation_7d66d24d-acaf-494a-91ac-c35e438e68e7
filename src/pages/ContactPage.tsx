import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Mail, Phone, MapPin, Clock, Instagram, Facebook, Twitter, Youtube } from 'lucide-react';
import Contact from '../components/Contact';

const ContactPage: React.FC = () => {
  return (
    <div className="pt-18">
      <Helmet>
        <title>Contact <PERSON><PERSON> | Yoga School in Rishikesh</title>
        <meta name="description" content="Contact <PERSON> for yoga teacher training, retreats, and wellness programs in Rishikesh. We're here to help you start your yoga journey." />
        <meta property="og:title" content="Contact <PERSON>ti <PERSON>g <PERSON>th | Yoga School in Rishikesh" />
        <meta property="og:description" content="Contact <PERSON> for yoga teacher training, retreats, and wellness programs in Rishikesh. We're here to help you start your yoga journey." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://yourdomain.com/contact" />
      </Helmet>
      <div className="bg-amber-400/10 dark:bg-indigo-900/20 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Contact <span className="text-amber-500 dark:text-amber-400">Shanti Yog Peeth</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            We're here to answer your questions and help you begin your yoga journey in Rishikesh.
          </p>
        </div>
      </div>
      
      <Contact />
      
      <section className="py-16 bg-amber-50/50 dark:bg-slate-800/30">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
              Frequently Asked <span className="text-amber-500 dark:text-amber-400">Questions</span>
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">How do I get to Rishikesh?</h3>
              <p className="text-slate-600 dark:text-slate-300">
                The nearest airport is Dehradun (DED), about 35km away. We can arrange airport pickup for an additional fee. Alternatively, you can take a train to Haridwar and then a taxi to Rishikesh.
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">What should I bring to a retreat or course?</h3>
              <p className="text-slate-600 dark:text-slate-300">
                Comfortable yoga clothes, a yoga mat (though we provide mats if needed), meditation cushion if you have a preferred one, weather-appropriate clothing, toiletries, and any personal medications.
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Do I need a visa to visit India?</h3>
              <p className="text-slate-600 dark:text-slate-300">
                Yes, most nationalities require a visa to enter India. We recommend applying for an e-Tourist Visa online before your trip. The process is straightforward and typically takes 3-5 business days.
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">What's the best time to visit Rishikesh?</h3>
              <p className="text-slate-600 dark:text-slate-300">
                The most pleasant weather is from September to November and February to April. December and January can be cool, especially in the evenings. May to August is monsoon season with high humidity.
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Is WiFi available at your center?</h3>
              <p className="text-slate-600 dark:text-slate-300">
                Yes, we provide complimentary WiFi in common areas. However, we encourage a digital detox during retreats to fully immerse in the experience.
              </p>
            </div>
            
            <div className="bg-white dark:bg-slate-800 p-6 rounded-xl shadow-md">
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">What type of food is served?</h3>
              <p className="text-slate-600 dark:text-slate-300">
                We serve fresh, organic vegetarian meals prepared according to Ayurvedic principles. Vegan options are available upon request. Our food is locally sourced whenever possible.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <div>
              <div className="flex items-center mb-6">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Connect With Us</span>
              </div>
              
              <h2 className="text-3xl font-bold mb-8 text-slate-900 dark:text-white">
                Follow Our <span className="text-amber-500 dark:text-amber-400">Journey</span>
              </h2>
              
              <div className="grid grid-cols-2 gap-4">
                <a 
                  href="#" 
                  className="flex flex-col items-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <Instagram className="w-10 h-10 text-amber-500 dark:text-amber-400 mb-3" />
                  <span className="text-slate-900 dark:text-white font-medium">Instagram</span>
                  <span className="text-slate-500 dark:text-slate-400 text-sm">@shantiyogpeeth</span>
                </a>
                
                <a 
                  href="#" 
                  className="flex flex-col items-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <Facebook className="w-10 h-10 text-amber-500 dark:text-amber-400 mb-3" />
                  <span className="text-slate-900 dark:text-white font-medium">Facebook</span>
                  <span className="text-slate-500 dark:text-slate-400 text-sm">/shantiyogpeeth</span>
                </a>
                
                <a 
                  href="#" 
                  className="flex flex-col items-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <Twitter className="w-10 h-10 text-amber-500 dark:text-amber-400 mb-3" />
                  <span className="text-slate-900 dark:text-white font-medium">Twitter</span>
                  <span className="text-slate-500 dark:text-slate-400 text-sm">@shantiyogpeeth</span>
                </a>
                
                <a 
                  href="#" 
                  className="flex flex-col items-center p-6 bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <Youtube className="w-10 h-10 text-amber-500 dark:text-amber-400 mb-3" />
                  <span className="text-slate-900 dark:text-white font-medium">YouTube</span>
                  <span className="text-slate-500 dark:text-slate-400 text-sm">Shanti Yog Peeth</span>
                </a>
              </div>
            </div>
            
            <div>
              <div className="bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-6">Our Location</h3>
                  <div className="space-y-4 mb-6">
                    <div className="flex items-start">
                      <MapPin className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-0.5" />
                      <div>
                        <span className="block text-slate-900 dark:text-white font-medium">Address</span>
                        <span className="text-slate-600 dark:text-slate-300">
                          123 Yoga Street, Laxman Jhula<br />
                          Rishikesh, Uttarakhand 249302<br />
                          India
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <Phone className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-0.5" />
                      <div>
                        <span className="block text-slate-900 dark:text-white font-medium">Phone</span>
                        <span className="text-slate-600 dark:text-slate-300">
                          +91 98765 43210<br />
                          +91 98765 43211
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <Mail className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-0.5" />
                      <div>
                        <span className="block text-slate-900 dark:text-white font-medium">Email</span>
                        <span className="text-slate-600 dark:text-slate-300">
                          <EMAIL><br />
                          <EMAIL>
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <Clock className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-0.5" />
                      <div>
                        <span className="block text-slate-900 dark:text-white font-medium">Hours</span>
                        <span className="text-slate-600 dark:text-slate-300">
                          Monday - Saturday: 6:00 AM - 7:00 PM<br />
                          Sunday: 8:00 AM - 12:00 PM
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
