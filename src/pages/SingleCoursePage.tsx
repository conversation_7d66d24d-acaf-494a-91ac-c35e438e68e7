import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import {
  Calendar, Clock, Award, BookOpen, Users, DollarSign, ArrowLeft, Mail, Phone,
  Home, Utensils, MapPin, UserCheck, MountainSnow, Star, CheckCircle, XCircle, Plane, Info
} from 'lucide-react';
import ImageGallery from '../components/ImageGallery';
import { Course, Teacher, CommonData, Schedule, Certification } from '../data/data';

const SingleCoursePage: React.FC = () => {
  const { courseSlug } = useParams<{ courseSlug: string }>();

  const [courses, setCourses] = React.useState<Course[]>([]);
  const [commonData, setCommonData] = React.useState<CommonData | null>(null);
  const [schedules, setSchedules] = React.useState<Schedule[]>([]);
  const [teachers, setTeachers] = React.useState<Teacher[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [certifications, setCertifications] = React.useState<Certification[]>([]);

  // Import data directly from data.ts
  React.useEffect(() => {
    try {
      // Import all data from the data.ts file
      import('../data/data').then(({ courses, common, schedules, teachers, certifications }) => {
        setCourses(courses);
        setCommonData(common);
        setSchedules(schedules);
        setTeachers(teachers);
        setCertifications(certifications);
      });
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  const course = courses.find(c => c.slug === courseSlug);

  const accommodation = course?.accommodation || commonData?.accommodation;
  const food = course?.food || commonData?.food;
  const location = course?.location || commonData?.location;
  // Map excursion ids to activities if present
  let excursionActivities: string[] = [];
  if (
    course?.excursion &&
    Array.isArray(course.excursion) &&
    course.excursion.length > 0 &&
    commonData?.includedExcursions
  ) {
    excursionActivities = course.excursion
      .map((id: number) => {
        const found = commonData.includedExcursions.find((e) => e.id === id);
        return found ? found.activity : null;
      })
      .filter((a): a is string => !!a);
  }
  const thingsToDo = course?.thingsToDo || commonData?.thingsToDo;
  const whatsIncluded = course?.whatsIncluded || commonData?.whatsIncluded;
  const whatsNotIncluded = course?.whatsNotIncluded || commonData?.whatsNotIncluded;
  const howToGetThere = course?.howToGetThere || commonData?.howToGetThere;

  const schedule = schedules.find(s => s.id === course?.scheduleId);

  if (loading) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-slate-600 dark:text-slate-300">Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-red-600 dark:text-red-400">Error: {error}</p>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
        <div className="container mx-auto px-4 md:px-6 py-12 text-center">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Course Not Found</h1>
          <p className="text-slate-600 dark:text-slate-300 mb-8">The course you're looking for doesn't exist.</p>
          <Link
            to="/courses"
            className="inline-flex items-center text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Courses
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
      <Helmet>
        <title>{course.title} | Shanti Yog Peeth</title>
        <meta name="description" content={course.description} />
        <meta property="og:title" content={course.title} />
        <meta property="og:description" content={course.description} />
        {course.image && <meta property="og:image" content={course.image} />}
        <meta property="og:type" content="article" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={course.title} />
        <meta name="twitter:description" content={course.description} />
        {course.image && <meta name="twitter:image" content={course.image} />}
        <link rel="canonical" href={`https://shantiyogpeeth.com/courses/${course.slug}`} />
      </Helmet>
        {/* Hero Section */}
        <div className="relative h-[60vh] min-h-[650px] overflow-hidden">
          <img
            src={course.image}
            alt={course.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-white/50 via-transparent to-black/100 flex items-center">
            <div className="container mx-auto px-4 md:px-6">
              <Link
                to="/courses"
                className="inline-flex items-center text-white hover:text-amber-400 mb-6 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Courses
              </Link>
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">{course.title}</h1>
              <div className="flex flex-wrap items-center gap-6 text-white">
                <span className="flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  <span>{course.duration} Days</span>
                </span>
                <span className="flex items-center">
                  <Award className="w-5 h-5 mr-2" />
                  {course.certification
                    .map(certId => {
                      const cert = certifications.find(c => c.id === certId);
                      return cert ? cert.name : certId;
                    })
                    .join(', ')
                  }
                </span>
                <span className="flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  {course.level}
                </span>
                <span className="flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  {course.price.shared}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Image Gallery Section */}
        {course.gallery && course.gallery.length > 0 && (
          <div className="bg-transparent -mt-20">
            <ImageGallery images={course.gallery} height="h-40" />
          </div>
        )}

        {/* Main Content */}
        <div className="container mx-auto px-4 md:px-6 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content Area */}
            <div className="lg:col-span-2 space-y-12">
            {/* Inactive Banner */}
            {!course.active && (
              <div className="w-full border-2 border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-4 rounded-lg mb-6">
                <p className="text-center font-semibold">This course is currently unavailable.</p>
              </div>
            )}

            {/* Description */}
            {course.description && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Course Description</h2>
                <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{course.description}</p>
              </section>
            )}

            {/* Highlights */}
            {course.highlights && course.highlights.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Course Highlights</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {course.highlights.map((highlight, index) => (
                    <div
                      key={index}
                      className="flex items-start space-x-3 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm"
                    >
                      <div className="flex-shrink-0 w-8 h-8 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400">
                        {index + 1}
                      </div>
                      <span className="text-slate-600 dark:text-slate-300">{highlight}</span>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Curriculum */}
            {course.curriculum && course.curriculum.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Curriculum Overview</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {course.curriculum.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center space-x-3 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm"
                    >
                      <BookOpen className="w-5 h-5 text-amber-500 dark:text-amber-400" />
                      <span className="text-slate-600 dark:text-slate-300">{item}</span>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* --- NEW SECTIONS START --- */}

            {/* Program Overview */}
            {course.programOverview && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <Info className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Program Overview
                </h2>
                <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{course.programOverview}</p>
              </section>
            )}

            {/* Daily Schedule */}
            {schedule && schedule.dailySchedule.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <Clock className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Typical Daily Schedule
                </h2>
                <p className="text-slate-600 dark:text-slate-400 mb-4">
                  This is a sample daily schedule and is subject to change. Actual timings and activities may vary depending on various factors, including the specific changes in program, the teachers' availability, and the needs of the students. Additionally, weather conditions and local events could also influence the schedule.
                </p>
                <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm overflow-hidden">
                  <ul className="divide-y divide-slate-200 dark:divide-slate-700">
                    {schedule.dailySchedule.map((item, index) => (
                      <li key={index} className="px-4 py-3 flex justify-between items-center">
                        <span className="font-medium text-slate-700 dark:text-slate-300 w-28">{item.time}</span>
                        <span className="text-slate-600 dark:text-slate-400 text-right">{item.activity}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </section>
            )}

            {/* Pricing Section */}
            {(course.price?.shared || course.price?.private || course.price?.sharedTwin) && (
              <section className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-50 to-indigo-50 dark:from-slate-800 dark:to-slate-900 opacity-20 rounded-2xl -z-10"></div>
                <div className="relative">
                  <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-6 flex items-center">
                    <DollarSign className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                    Pricing Options
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {course.price?.shared && (
                    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm overflow-hidden border border-slate-200 dark:border-slate-700 flex flex-col h-full">
                      <div className="p-6 flex-1">
                        <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Shared Room</h3>
                        <p className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-4">${course.price.shared}</p>
                        <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>Dormitory style accommodation</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>Shared bathroom facilities</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>All course materials included</span>
                          </li>
                        </ul>
                      </div>
                      <div className="p-6 pt-0">
                        <Link
                          to={`/apply?course=${course.id}`}
                          className="block w-full py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium text-center transition-colors duration-300"
                        >
                          Book Your Spot
                        </Link>
                      </div>
                    </div>
                  )}

                  {course.price?.private && (
                    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm overflow-hidden border border-slate-200 dark:border-slate-700 flex flex-col h-full">
                      <div className="p-6 flex-1">
                        <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Private Room</h3>
                        <p className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-4">${course.price.private}</p>
                        <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>Private room with single bed</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>Attached bathroom</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>All course materials included</span>
                          </li>
                        </ul>
                      </div>
                      <div className="p-6 pt-0">
                        <Link
                          to={`/apply?course=${course.id}`}
                          className="block w-full py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium text-center transition-colors duration-300"
                        >
                          Book Your Spot
                        </Link>
                      </div>
                    </div>
                  )}

                  {course.price?.sharedTwin && (
                    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm overflow-hidden border border-slate-200 dark:border-slate-700 flex flex-col h-full">
                      <div className="p-6 flex-1">
                        <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">Shared Twin Room</h3>
                        <p className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-4">${course.price.sharedTwin}</p>
                        <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>Shared room for two people</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>Attached bathroom</span>
                          </li>
                          <li className="flex items-start">
                            <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                            <span>All course materials included</span>
                          </li>
                        </ul>
                      </div>
                      <div className="p-6 pt-0">
                        <Link
                          to={`/apply?course=${course.id}`}
                          className="block w-full py-2 px-4 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium text-center transition-colors duration-300"
                        >
                          Book Your Spot
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
                <div className="mt-8 p-4 bg-amber-50 dark:bg-slate-800 rounded-lg border border-amber-200 dark:border-slate-700">
                  <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-300 mb-2">Discount Offers<span className="font-mono bg-amber-100 dark:bg-slate-700 px-2 py-1 rounded">Upto 20%</span></h3>
                  <p className="text-slate-600 dark:text-slate-300">
                    <span className="font-medium">Early Bird Discount:</span> Get 5% off when you book 12 months in advance.
                  </p>
                  <p className="text-slate-600 dark:text-slate-300">
                    <span className="font-medium">Group  Discount:</span> Get 5% off when you book for 2 people or more at same time.
                  </p>
                  <p className="text-slate-600 dark:text-slate-300">
                    <span className="font-medium">Upfront Payment Discount:</span> Get 5% off when you pay 100% payment in advance at time of booking.
                  </p>
                  <p className="text-slate-600 dark:text-slate-300 mt-2">
                    <span className="font-medium">Group Discount:</span> Groups of 4+ receive 10% discount. Contact us for details.
                  </p>
                </div>
              </div>
            </section>
            )}

            {/* Prerequisites */}
            <section>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                <BookOpen className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                Prerequisites
              </h2>
              {course.prerequisites && course.prerequisites.length === 0 ? (
                <p className="text-slate-600 dark:text-slate-300">No prerequisites required for this course.</p>
              ) : (
                <ul className="list-disc list-inside space-y-2 text-slate-600 dark:text-slate-300 pl-5">
                  {course.prerequisites && course.prerequisites.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              )}
            </section>

            {/* Accommodation */}
            {accommodation && accommodation.type && accommodation.description && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <Home className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Accommodation
                </h2>
                <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">{accommodation.type}</h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">{accommodation.description}</p>
                  {accommodation.features && accommodation.features.length > 0 && (
                    <div>
                      <h4 className="text-md font-medium text-slate-700 dark:text-slate-300 mb-2">Features:</h4>
                      <ul className="list-disc list-inside space-y-1 text-slate-600 dark:text-slate-400">
                        {accommodation.features.map((feature: string, index: number) => (
                          <li key={index}>{feature}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </section>
            )}

            {/* Food */}
            {food && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <Utensils className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Food
                </h2>
                <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{food}</p>
              </section>
            )}

            {/* Location */}
            {location && location.description && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <MapPin className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Location
                </h2>
                <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-6">
                  <p className="text-slate-600 dark:text-slate-300 mb-3">{location.description}</p>
                  {location.address && (
                    <p className="text-sm text-slate-500 dark:text-slate-400 mb-3">{location.address}</p>
                  )}
                  {location.mapLink && (
                    <a
                      href={location.mapLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300 text-sm"
                    >
                      View on Map <ArrowLeft className="w-4 h-4 ml-1 transform rotate-180" />
                    </a>
                  )}
                </div>
              </section>
            )}

            {/* Instructors */}
            {course.instructors && course.instructors.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <UserCheck className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Instructors
                </h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {course.instructors.map((instructorId, index) => {
                    const teacher = teachers.find(t => t.id === instructorId);
                    return (
                      <div key={index} className="flex items-center space-x-3 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm">
                        {teacher && teacher.image ? (
                          <img
                            src={teacher.image}
                            alt={teacher.name}
                            className="w-10 h-10 rounded-full object-cover border-2 border-amber-400 dark:border-amber-500"
                          />
                        ) : (
                          <div className="flex-shrink-0 w-8 h-8 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400">
                            <UserCheck className="w-4 h-4" />
                          </div>
                        )}
                        <div>
                          <span className="block text-slate-900 dark:text-white font-semibold">
                            {teacher ? teacher.name : instructorId}
                          </span>
                          {teacher && teacher.role && (
                            <span className="block text-xs text-slate-500 dark:text-slate-300">{teacher.role}</span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
                {/* Consider linking to a dedicated Teachers page if available */}
              </section>
            )}

            {/* Included Excursions */}
            {excursionActivities && excursionActivities.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <MountainSnow className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Included Excursions
                </h2>
                <ul className="list-disc list-inside space-y-2 text-slate-600 dark:text-slate-300 pl-5">
                  {excursionActivities.map((activity: string, index: number) => (
                    <li key={index}>{activity}</li>
                  ))}
                </ul>
              </section>
            )}

            {/* Things To Do */}
            {thingsToDo && thingsToDo.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <Star className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  Things To Do (Optional)
                </h2>
                <ul className="list-disc list-inside space-y-2 text-slate-600 dark:text-slate-300 pl-5">
                  {thingsToDo.map((item: string, index: number) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </section>
            )}

            {/* What's Included / Not Included */}
            {(whatsIncluded || whatsNotIncluded) && (
              <section className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {whatsIncluded && whatsIncluded.length > 0 && (
                  <div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 flex items-center">
                      <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
                      What's Included
                    </h3>
                    <ul className="space-y-2">
                      {whatsIncluded.map((item: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="w-4 h-4 mr-2 mt-1 text-green-500 flex-shrink-0" />
                          <span className="text-slate-600 dark:text-slate-300">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                {whatsNotIncluded && whatsNotIncluded.length > 0 && (
                   <div>
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-3 flex items-center">
                      <XCircle className="w-5 h-5 mr-2 text-red-500" />
                      What's Not Included
                    </h3>
                    <ul className="space-y-2">
                      {whatsNotIncluded.map((item: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <XCircle className="w-4 h-4 mr-2 mt-1 text-red-500 flex-shrink-0" />
                          <span className="text-slate-600 dark:text-slate-300">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </section>
            )}

            {/* How to Get There */}
            {howToGetThere && (
              <section>
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4 flex items-center">
                  <Plane className="w-6 h-6 mr-3 text-amber-500 dark:text-amber-400" />
                  How to Get There
                </h2>
                <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{howToGetThere}</p>
              </section>
            )}

            {/* --- NEW SECTIONS END --- */}

          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-16 space-y-6">
              {/* Course Details Card */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">{course.title}</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center gap-2">
                      <span className="inline-flex items-center px-3 py-1.5 rounded-full bg-amber-100 dark:bg-amber-600/20 text-sm font-medium text-amber-700 dark:text-amber-300 space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{course.duration} Days</span>
                      </span>
                      <span className="inline-flex items-center px-3 py-1.5 rounded-full bg-indigo-100 dark:bg-indigo-600/20 text-sm font-medium text-indigo-700 dark:text-indigo-300 space-x-1">
                        <Award className="w-4 h-4" />
                        <span>
                          {course.certification
                            .map(certId => {
                              const cert = certifications.find(c => c.id === certId);
                              return cert ? cert.name : certId;
                            })
                            .join(', ')
                          }
                        </span>
                      </span>
                    </div>

                    <div className="space-y-1.5 mt-4">
                      {course.dates.slice(0,3).map((date, index) => {
                        // Parse DD/MM/YYYY
                        const [day, month, year] = date.split('/').map(Number);
                        const startDate = new Date(year, month - 1, day);
                        const endDate = new Date(startDate);
                        endDate.setDate(startDate.getDate() + course.duration - 1);

                        // Format: 05 January 2025 - 01 February 2025
                        const formatDate = (d: Date) =>
                          d.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'long',
                            year: 'numeric'
                          });

                        return (
                          <div key={index} className="flex items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                            <Calendar className="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2" />
                            <span className="flex-1 text-sm text-slate-600 dark:text-slate-300 text-left">{formatDate(startDate)}</span>
                            <span className="flex-none text-sm text-slate-600 dark:text-slate-300 mx-2">-</span>
                            <span className="flex-1 text-sm text-slate-600 dark:text-slate-300 text-right">{formatDate(endDate)}</span>
                          </div>
                        );
                      })}
                    </div>

                    <Link
                      to="/calendar"
                      className="block w-full mt-4 py-2 px-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium text-sm text-center transition-colors duration-300"
                    >
                      Check Calendar for More Dates
                    </Link>
                  </div>
                </div>
              </div>

              {/* Packages Card */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Packages</h3>
                  <div className="space-y-1.5">
                    {course.price?.shared && (
                      <div className="flex justify-between items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                          <span className="text-sm text-slate-600 dark:text-slate-300">1 Person Shared Twin Room</span>
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">$ {course.price.shared}</span>
                      </div>
                    )}
                    {course.price?.private && (
                      <div className="flex justify-between items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                          <span className="text-sm text-slate-600 dark:text-slate-300">1 Person Private Room</span>
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">$ {course.price.private}</span>
                      </div>
                    )}
                    {course.price?.sharedTwin && (
                      <div className="flex justify-between items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                          <span className="text-sm text-slate-600 dark:text-slate-300">2 Person Shared Twin Room</span>
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">$ {course.price.sharedTwin}</span>
                      </div>
                    )}
                  </div>

                  <Link
                    to={`/apply?course=${course.id}`}
                    className="block w-full mt-4 py-3 px-6 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium text-center transition-colors duration-300"
                  >
                    Apply Now
                  </Link>
                </div>
              </div>

              {/* Contact Info */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Have Questions?</h3>
                  <div className="space-y-4">
                    <a
                      href="mailto:<EMAIL>"
                      className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400"
                    >
                      <Mail className="w-5 h-5 mr-2" />
                      <EMAIL>
                    </a>
                    <a
                      href="tel:+919876543210"
                      className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400"
                    >
                      <Phone className="w-5 h-5 mr-2" />
                      +91 98765 43210
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleCoursePage;
