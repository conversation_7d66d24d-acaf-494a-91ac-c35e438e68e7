import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';

// Import test components that don't have dependency issues
import TestComponent from '../test/TestComponent';
import AboutArya from '../test/AboutArya';
import AboutArya2 from '../test/AboutArya2';
import AboutSchool from '../test/AboutSchool';
import Hero from '../test/Hero';
import Results from '../test/Results';
import ResultsSection from '../test/ResultsSection';
import SchoolOfferings from '../test/SchoolOfferings';
import StillHaveQuestions from '../test/StillHaveQuestions';
import TargetAudience from '../test/TargetAudience';
import WhoIsItFor from '../test/WhoIsItFor';

// Import new components
import YogaPhilosophy from '../test/YogaPhilosophy';
import DailySchedule from '../test/DailySchedule';
import TestimonialCarousel from '../test/TestimonialCarousel';
import PricingTable from '../test/PricingTable';
import LocationMap from '../test/LocationMap';
import YogaTeacherCard from '../test/YogaTeacherCard';
import CountdownTimer from '../test/CountdownTimer';
import FAQAccordion from '../test/FAQAccordion';
import GalleryMasonry from '../test/GalleryMasonry';

// Import new components
import MeditationTimer from '../test/MeditationTimer';
import YogaPoseLibrary from '../test/YogaPoseLibrary';
import EventCalendar from '../test/EventCalendar';
import TeacherSpotlight from '../test/TeacherSpotlight';
import BreathingExercise from '../test/BreathingExercise';

// Import newest components
import TestimonialWall from '../test/TestimonialWall';
import BlogPreview from '../test/BlogPreview';
import AyurvedicProfile from '../test/AyurvedicProfile';
import CommunityGallery from '../test/CommunityGallery';
import YogaStyleComparison from '../test/YogaStyleComparison';

const TestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 pt-20">
      <Helmet>
        <title>Test Components | Shanti Yog Peeth</title>
        <meta name="description" content="Test page for new components" />
      </Helmet>

      {/* Page Header */}
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl md:text-4xl font-bold text-center text-slate-900 dark:text-white mb-8">
          Test Components
        </h1>
        <p className="text-lg text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto mb-6">
          This page displays test components for review before adding them to the main site.
        </p>
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-3xl mx-auto mb-12">
          <h3 className="font-semibold text-blue-800 dark:text-blue-300 mb-2">How to Add Components</h3>
          <ol className="list-decimal list-inside text-blue-700 dark:text-blue-300 space-y-2">
            <li>Create your component in the <code className="bg-white dark:bg-slate-800 px-2 py-1 rounded">src/test</code> folder</li>
            <li>Import it at the top of this file: <code className="bg-white dark:bg-slate-800 px-2 py-1 rounded">import YourComponent from '../test/YourComponent';</code></li>
            <li>Add it to the component list below: <code className="bg-white dark:bg-slate-800 px-2 py-1 rounded">{'<ComponentSection title="Your Component" component={<YourComponent />} />'}</code></li>
          </ol>
        </div>
      </div>

      {/* Component Sections */}
      <div className="container mx-auto px-4 pb-20">
        {/* Each component is wrapped in a section with a title */}
        <ComponentSection title="Test Component" component={<TestComponent />} />
        <ComponentSection title="About Arya" component={<AboutArya />} />
        <ComponentSection title="About Arya 2" component={<AboutArya2 />} />
        <ComponentSection title="About School" component={<AboutSchool />} />
        <ComponentSection title="Hero" component={<Hero />} />
        <ComponentSection title="Results" component={<Results />} />
        <ComponentSection title="Results Section" component={<ResultsSection />} />
        <ComponentSection title="School Offerings" component={<SchoolOfferings />} />
        <ComponentSection title="Still Have Questions" component={<StillHaveQuestions />} />
        <ComponentSection title="Target Audience" component={<TargetAudience />} />
        <ComponentSection title="Who Is It For" component={<WhoIsItFor />} />

        {/* New Components */}
        <ComponentSection title="Yoga Philosophy" component={<YogaPhilosophy />} />
        <ComponentSection title="Daily Schedule" component={<DailySchedule />} />
        <ComponentSection title="Testimonial Carousel" component={<TestimonialCarousel />} />
        <ComponentSection title="Pricing Table" component={<PricingTable />} />
        <ComponentSection title="Location Map" component={<LocationMap />} />
        <ComponentSection title="Yoga Teacher Card" component={<YogaTeacherCard />} />
        <ComponentSection title="Countdown Timer" component={<CountdownTimer />} />
        <ComponentSection title="FAQ Accordion" component={<FAQAccordion />} />
        <ComponentSection title="Gallery Masonry" component={<GalleryMasonry />} />
        
        {/* Newly Added Components */}
        <ComponentSection title="Meditation Timer" component={<MeditationTimer />} />
        <ComponentSection title="Yoga Pose Library" component={<YogaPoseLibrary />} />
        <ComponentSection title="Event Calendar" component={<EventCalendar />} />
        <ComponentSection title="Teacher Spotlight" component={<TeacherSpotlight />} />
        <ComponentSection title="Breathing Exercise" component={<BreathingExercise />} />
        
        {/* Newest Added Components */}
        <ComponentSection title="Testimonial Wall" component={<TestimonialWall />} />
        <ComponentSection title="Blog Preview" component={<BlogPreview />} />
        <ComponentSection title="Ayurvedic Profile" component={<AyurvedicProfile />} />
        <ComponentSection title="Community Gallery" component={<CommunityGallery />} />
        <ComponentSection title="Yoga Style Comparison" component={<YogaStyleComparison />} />
      </div>
    </div>
  );
};

// Helper component to display each test component with just a title
const ComponentSection: React.FC<{ title: string; component: React.ReactNode }> = ({
  title,
  component
}) => {
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  return (
    <div className="mb-16">
      <h2 className="text-2xl font-semibold text-amber-600 dark:text-amber-400 mb-4 border-b border-amber-200 dark:border-amber-800 pb-2">{title}</h2>
      {hasError ? (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 mb-2">Error Rendering Component</h3>
          <p className="text-red-600 dark:text-red-300">{errorMessage}</p>
        </div>
      ) : (
        <ErrorBoundary
          onError={(error) => {
            setHasError(true);
            setErrorMessage(error.message);
          }}
        >
          {component}
        </ErrorBoundary>
      )}
    </div>
  );
};

// Error boundary to catch errors in components
class ErrorBoundary extends React.Component<{
  children: React.ReactNode;
  onError: (error: Error) => void;
}> {
  componentDidCatch(error: Error) {
    this.props.onError(error);
  }

  render() {
    return this.props.children;
  }
}

export default TestPage;
