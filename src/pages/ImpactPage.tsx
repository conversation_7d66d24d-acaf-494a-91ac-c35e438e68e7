import React from 'react';
import { Heart, Leaf, Users, GraduationCap, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Initiative {
  id: number;
  title: string;
  description: string;
  impact: string;
  image: string;
}

const initiatives: Initiative[] = [
  {
    id: 1,
    title: "Yoga for All",
    description: "Free weekly yoga classes for local community members who cannot afford regular classes.",
    impact: "Over 1,000 local residents benefited in 2024",
    image: "https://placehold.co/800x600/e2e8f0/1e293b?text=Community+Yoga"
  },
  {
    id: 2,
    title: "Green Campus Initiative",
    description: "Comprehensive sustainability program including solar power, rainwater harvesting, and organic gardening.",
    impact: "50% reduction in carbon footprint since 2023",
    image: "https://placehold.co/800x600/e2e8f0/1e293b?text=Green+Campus"
  },
  {
    id: 3,
    title: "Youth Empowerment Program",
    description: "Mentorship and skill development program for underprivileged youth in Rishikesh.",
    impact: "200 youth trained in various skills",
    image: "https://placehold.co/800x600/e2e8f0/1e293b?text=Youth+Program"
  }
];

const ImpactPage: React.FC = () => {
  return (
    <div className="pt-18">
      {/* Hero Section */}
      <div className="bg-amber-400/10 dark:bg-indigo-900/20 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Our <span className="text-amber-500 dark:text-amber-400">Impact</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Creating positive change through yoga, education, and community engagement.
          </p>
        </div>
      </div>

      {/* Impact Statistics */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 text-center shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <div className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-2">5,000+</div>
              <div className="text-slate-600 dark:text-slate-300">Students Trained</div>
            </div>
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 text-center shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <Heart className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <div className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-2">100+</div>
              <div className="text-slate-600 dark:text-slate-300">Community Programs</div>
            </div>
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 text-center shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <GraduationCap className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <div className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-2">50+</div>
              <div className="text-slate-600 dark:text-slate-300">Scholarships Awarded</div>
            </div>
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 text-center shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <Leaf className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <div className="text-3xl font-bold text-amber-500 dark:text-amber-400 mb-2">80%</div>
              <div className="text-slate-600 dark:text-slate-300">Waste Reduction</div>
            </div>
          </div>
        </div>
      </section>

      {/* Current Initiatives */}
      <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-slate-900 dark:text-white">
              Current <span className="text-amber-500 dark:text-amber-400">Initiatives</span>
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Our ongoing projects that create lasting positive impact in our community and environment.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {initiatives.map(initiative => (
              <div key={initiative.id} className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <img
                  src={initiative.image}
                  alt={initiative.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-3">{initiative.title}</h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">{initiative.description}</p>
                  <div className="bg-amber-50 dark:bg-slate-700 rounded-lg p-3">
                    <p className="text-sm text-amber-600 dark:text-amber-400">{initiative.impact}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Scholarship Programs */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Scholarships
                </span>
              </div>
              <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">
                Making Yoga Education Accessible
              </h2>
              <p className="text-slate-600 dark:text-slate-300 mb-8">
                Our scholarship programs aim to make yoga education accessible to dedicated students 
                from all backgrounds. We believe that financial constraints should not limit one's 
                spiritual and professional growth.
              </p>
              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="w-5 h-5 rounded-full bg-amber-400 mr-3 flex items-center justify-center text-white text-xs mt-1">✓</div>
                  <div>
                    <h3 className="font-medium text-slate-900 dark:text-white">Merit-Based Scholarships</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">For outstanding students with demonstrated excellence</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-5 h-5 rounded-full bg-amber-400 mr-3 flex items-center justify-center text-white text-xs mt-1">✓</div>
                  <div>
                    <h3 className="font-medium text-slate-900 dark:text-white">Need-Based Financial Aid</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Supporting students with financial constraints</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-5 h-5 rounded-full bg-amber-400 mr-3 flex items-center justify-center text-white text-xs mt-1">✓</div>
                  <div>
                    <h3 className="font-medium text-slate-900 dark:text-white">Local Community Grants</h3>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Special programs for Rishikesh residents</p>
                  </div>
                </div>
              </div>
              <Link 
                to="/apply"
                className="inline-flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300"
              >
                Apply for Scholarship
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
            <div className="relative">
              <img 
                src="https://placehold.co/600x400/e2e8f0/1e293b?text=Scholarship+Program" 
                alt="Scholarship recipients"
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-amber-400 dark:bg-indigo-600 rounded-lg -z-10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Environmental Initiatives */}
      <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="relative order-2 lg:order-1">
              <img 
                src="https://placehold.co/600x400/e2e8f0/1e293b?text=Environmental+Initiatives" 
                alt="Environmental initiatives"
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-amber-400 dark:bg-indigo-600 rounded-lg -z-10"></div>
            </div>
            <div className="order-1 lg:order-2">
              <div className="flex items-center mb-4">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                  Environmental Impact
                </span>
              </div>
              <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-6">
                Sustainable Practices
              </h2>
              <p className="text-slate-600 dark:text-slate-300 mb-8">
                We are committed to minimizing our environmental impact and promoting sustainable 
                practices throughout our operations and community.
              </p>
              <div className="space-y-4">
                <div className="bg-white dark:bg-slate-800 rounded-lg p-4">
                  <h3 className="font-medium text-slate-900 dark:text-white mb-2">Solar Power Initiative</h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">80% of our energy needs met through solar power</p>
                </div>
                <div className="bg-white dark:bg-slate-800 rounded-lg p-4">
                  <h3 className="font-medium text-slate-900 dark:text-white mb-2">Waste Management</h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">Comprehensive recycling and composting programs</p>
                </div>
                <div className="bg-white dark:bg-slate-800 rounded-lg p-4">
                  <h3 className="font-medium text-slate-900 dark:text-white mb-2">Water Conservation</h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm">Rainwater harvesting and water recycling systems</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ImpactPage;