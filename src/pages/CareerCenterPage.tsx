import React, { useState } from 'react';
import { Briefcase, GraduationCap, Book, Users, Search, MapPin, Calendar } from 'lucide-react';
import { Link } from 'react-router-dom';

interface JobListing {
  id: number;
  title: string;
  location: string;
  type: string;
  experience: string;
  description: string;
  requirements: string[];
  posted: string;
}

const jobListings: JobListing[] = [
  {
    id: 1,
    title: "Senior Yoga Teacher",
    location: "Rishikesh, India",
    type: "Full-time",
    experience: "5+ years",
    description: "Lead advanced yoga teacher training programs and mentor new teachers.",
    requirements: [
      "500-hour RYT certification",
      "5+ years teaching experience",
      "Strong knowledge of yoga philosophy",
      "Experience in teacher training"
    ],
    posted: "2 days ago"
  },
  {
    id: 2,
    title: "Ayurvedic Practitioner",
    location: "Rishikesh, India",
    type: "Full-time",
    experience: "3+ years",
    description: "Provide Ayurvedic consultations and treatments to guests.",
    requirements: [
      "BAMS degree",
      "3+ years clinical experience",
      "English proficiency",
      "Experience in wellness retreats"
    ],
    posted: "1 week ago"
  }
];

const CareerCenterPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  return (
    <div className="pt-18">
      {/* Hero Section */}
      <div className="bg-amber-400/10 dark:bg-indigo-900/20 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-5xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Career <span className="text-amber-500 dark:text-amber-400">Center</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Explore teaching opportunities, professional development resources, and continue your growth in yoga and wellness.
          </p>
        </div>
      </div>

      {/* Current Openings Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Job Listings */}
            <div className="lg:w-2/3">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Current Openings</h2>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search positions..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 rounded-lg border border-slate-300 dark:border-slate-600 focus:ring-2 focus:ring-amber-400 dark:focus:ring-indigo-500 focus:border-transparent bg-white dark:bg-slate-700 text-slate-900 dark:text-white"
                  />
                  <Search className="absolute left-3 top-2.5 w-4 h-4 text-slate-400" />
                </div>
              </div>

              <div className="space-y-6">
                {jobListings.map(job => (
                  <div key={job.id} className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">{job.title}</h3>
                        <div className="flex items-center gap-4 text-slate-600 dark:text-slate-300 text-sm">
                          <span className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {job.location}
                          </span>
                          <span className="flex items-center">
                            <Briefcase className="w-4 h-4 mr-1" />
                            {job.type}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {job.posted}
                          </span>
                        </div>
                      </div>
                      <button className="px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg text-sm font-medium transition-colors duration-300">
                        Apply Now
                      </button>
                    </div>
                    <p className="text-slate-600 dark:text-slate-300 mb-4">{job.description}</p>
                    <div className="space-y-2">
                      <h4 className="font-medium text-slate-900 dark:text-white">Requirements:</h4>
                      <ul className="list-disc list-inside text-slate-600 dark:text-slate-300 text-sm space-y-1">
                        {job.requirements.map((req, index) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Resources Sidebar */}
            <div className="lg:w-1/3">
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 mb-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Professional Development</h3>
                <ul className="space-y-4">
                  <li>
                    <Link to="/courses" className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400">
                      <Book className="w-5 h-5 mr-3" />
                      Advanced Teacher Training
                    </Link>
                  </li>
                  <li>
                    <Link to="/courses" className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400">
                      <GraduationCap className="w-5 h-5 mr-3" />
                      Specialized Certifications
                    </Link>
                  </li>
                  <li>
                    <Link to="/resources" className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400">
                      <Users className="w-5 h-5 mr-3" />
                      Mentorship Program
                    </Link>
                  </li>
                </ul>
              </div>

              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Continuing Education</h3>
                <div className="space-y-4">
                  <div className="p-4 bg-amber-50 dark:bg-slate-700 rounded-lg">
                    <h4 className="font-medium text-slate-900 dark:text-white mb-2">Upcoming Workshops</h4>
                    <p className="text-sm text-slate-600 dark:text-slate-300">Advanced Anatomy • Starting June 15</p>
                  </div>
                  <div className="p-4 bg-amber-50 dark:bg-slate-700 rounded-lg">
                    <h4 className="font-medium text-slate-900 dark:text-white mb-2">Online Courses</h4>
                    <p className="text-sm text-slate-600 dark:text-slate-300">Sanskrit Studies • Enrollment Open</p>
                  </div>
                  <div className="p-4 bg-amber-50 dark:bg-slate-700 rounded-lg">
                    <h4 className="font-medium text-slate-900 dark:text-white mb-2">Skill Development</h4>
                    <p className="text-sm text-slate-600 dark:text-slate-300">Business of Yoga • Monthly Webinars</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Professional Development Resources */}
      <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-slate-900 dark:text-white">
              Professional <span className="text-amber-500 dark:text-amber-400">Resources</span>
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Access tools and resources to help you grow in your yoga teaching career.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-lg flex items-center justify-center mb-4">
                <Book className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Teaching Resources</h3>
              <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                <li>• Sequence planning templates</li>
                <li>• Class themes and scripts</li>
                <li>• Meditation guides</li>
                <li>• Prop usage guides</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Community Support</h3>
              <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                <li>• Teacher forums</li>
                <li>• Mentorship matching</li>
                <li>• Study groups</li>
                <li>• Networking events</li>
              </ul>
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg">
              <div className="w-12 h-12 bg-amber-100 dark:bg-slate-700 rounded-lg flex items-center justify-center mb-4">
                <GraduationCap className="w-6 h-6 text-amber-500 dark:text-amber-400" />
              </div>
              <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Career Development</h3>
              <ul className="space-y-2 text-slate-600 dark:text-slate-300">
                <li>• Business coaching</li>
                <li>• Marketing workshops</li>
                <li>• Personal branding</li>
                <li>• Financial planning</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CareerCenterPage;