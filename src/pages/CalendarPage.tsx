import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Calendar, Clock, MapPin, ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import FilterBar from '../components/FilterBar';

// Month names
const monthNames = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
];

// Component for the month button in the calendar - price range is also not working lets keep two bars in same line one for min one for max
const MonthButton: React.FC<{
  month: number;
  year: number;
  selectedMonth: number | null;
  selectedYear: number;
  hasPrograms: boolean;
  onClick: () => void;
}> = ({ month, year, selectedMonth, selectedYear, hasPrograms, onClick }) => {
  const isSelected = selectedMonth === month && selectedYear === year;
  const today = new Date();
  const isCurrentMonth = today.getMonth() === month && today.getFullYear() === year;

  return (
    <button
      onClick={onClick}
      className={`
        p-1 rounded-md text-xs font-medium transition-all duration-200 relative
        ${isSelected
          ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white shadow-sm transform scale-105'
          : 'bg-white dark:bg-slate-800 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-700'}
        ${isCurrentMonth ? 'border border-amber-500 dark:border-indigo-500' : ''}
      `}
    >
      {monthNames[month].substring(0, 3)}
      {hasPrograms && (
        <span className="absolute top-0.5 right-0.5 w-1.5 h-1.5 bg-amber-500 dark:bg-indigo-400 rounded-full"></span>
      )}
    </button>
  );
};

interface Program {
  id: number;
  title: string;
  type: 'Teacher Training' | 'Retreat' | 'Online Course';
  dates: string[]; // Format: "YYYY-MM-DD"
  displayDates: string[]; // Format: "January 10-16, 2025"
  duration: string;
  description: string;
  image: string;
  price: string;
  location?: string;
  popularity: number; // For determining the most popular program
  slug?: string; // URL slug for the program
}

const CalendarPage: React.FC = () => {
  const today = new Date();
  const currentYear = today.getFullYear();

  const [programs, setPrograms] = useState<Program[]>([]);
const [selectedEventType, setSelectedEventType] = useState<string>('all');
const [selectedService, setSelectedService] = useState<string>('all');
const [selectedDuration, setSelectedDuration] = useState<string>('all');
const [selectedPriceRange, setSelectedPriceRange] = useState<[number, number]>([0, 5000]);
const [currentPage, setCurrentPage] = useState<number>(1);

// Ref for program list to scroll to when changing pages
const programListRef = useRef<HTMLDivElement>(null);

  const [selectedYear, setSelectedYear] = useState<number>(currentYear);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [filteredPrograms, setFilteredPrograms] = useState<Program[]>([]);

  const eventTypes = ['All Services', 'Courses', 'Retreats', 'Wellness Programs', 'Other Events'];
  const services = useMemo(() => {
    return {
      courses: programs
        .filter(p => (p.type === 'Teacher Training' || p.type === 'Online Course') && (p as any).active !== false)
        .map(p => ({ id: p.id, title: p.title })),
      retreats: programs
        .filter(p => p.type === 'Retreat' && (p as any).active !== false)
        .map(p => ({ id: p.id, title: p.title }))
    };
  }, [programs]);

  useEffect(() => {
    const loadPrograms = async () => {
      try {
        // Import data directly from data.ts
        const { courses, retreats } = await import('../data/data');

        // Helper to parse "DD/MM/YYYY" to Date (handling timezone correctly)
        const parseDate = (dateStr: string): Date => {
          const [day, month, year] = dateStr.split('/').map(Number);
          // Create date in local timezone without time component
          return new Date(Date.UTC(year, month - 1, day));
        };

        // Helper to format display date (e.g., "March 6, 2025")
        const formatDisplayDates = (dates: string[]): string[] => {
          if (dates.length === 0) return [];
          const dateObjs = dates.map(parseDate).sort((a, b) => a.getTime() - b.getTime());
          return dateObjs.map((d: Date) =>
            `${monthNames[d.getMonth()]} ${d.getDate()}, ${d.getFullYear()}`
          );
        };

        // Normalize courses
        const normalizedCourses = (courses || [])
          .filter((course) => course.active !== false)
          .map((course) => ({
          id: course.id,
          title: course.title,
          slug: course.slug,
          type: 'Teacher Training' as const,
          dates: (course.dates || []).map(parseDate).map((d: Date) =>
            d.toISOString().slice(0, 10)
          ),
          displayDates: formatDisplayDates(course.dates || []),
          duration: course.duration ? `${course.duration} Days` : '',
          description: course.description || '',
          image: course.image,
          price: course.price
            ? `$${Math.min(...Object.values(course.price as Record<string, number>))}`
            : '',
          location: 'Rishikesh, India',
          popularity: course.featured ? 100 : 80 // Use featured as a proxy for popularity
        }));

        // Normalize retreats
        const normalizedRetreats = (retreats || [])
          .filter((retreat) => retreat.active !== false)
          .map((retreat) => ({
          id: retreat.id + 1000, // Offset to avoid ID collision
          title: retreat.title,
          slug: retreat.slug,
          type: 'Retreat' as const,
          dates: (retreat.dates || []).map(parseDate).map((d: Date) =>
            d.toISOString().slice(0, 10)
          ),
          displayDates: formatDisplayDates(retreat.dates || []),
          duration: retreat.duration ? `${retreat.duration} Days` : '',
          description: retreat.description || '',
          image: retreat.image,
          price: retreat.price
            ? `$${typeof retreat.price === 'object'
                ? Math.min(...Object.values(retreat.price as Record<string, number>))
                : retreat.price
              }`
            : '',
          location: retreat.location || 'Rishikesh, India',
          popularity: 90 // Default popularity for retreats
        }));

        // Combine and sort by first date
        const allPrograms = [...normalizedCourses, ...normalizedRetreats].sort((a, b) => {
          const dateA = new Date(a.dates[0]);
          const dateB = new Date(b.dates[0]);
          return dateA.getTime() - dateB.getTime();
        });

        setPrograms(allPrograms);
      } catch (error) {
        console.error('Error loading programs:', error);
        setPrograms([]);
      }
    };

    loadPrograms();
  }, []);

  // Most popular program section removed

  // Function to check if a month has upcoming programs
  const monthHasPrograms = (month: number, year: number): boolean => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return programs.some((program: Program) => {
      return program.dates.some((dateStr: string) => {
        const date = new Date(dateStr);
        return date.getMonth() === month &&
               date.getFullYear() === year &&
               date >= today;
      });
    });
  };

  // Function to filter and transform programs into date-based entries
  const filterPrograms = useCallback(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let filtered = programs.flatMap((program) => {
      // Filter to only upcoming dates
      const upcomingDates = program.dates
        .map(dateStr => new Date(dateStr))
        .filter(date => date >= today)
        .sort((a, b) => a.getTime() - b.getTime());

      // Create one entry per upcoming date
      return upcomingDates.map((date) => ({
        ...program,
        dates: [date.toISOString().slice(0, 10)],
        displayDates: [
          `${monthNames[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
        ]
      }));
    });

    // Filter by event type
    if (selectedEventType !== 'all') {
      filtered = filtered.filter(program => {
        if (selectedEventType === 'courses') {
          return program.type === 'Teacher Training' || program.type === 'Online Course';
        } else if (selectedEventType === 'retreats') {
          return program.type === 'Retreat';
        }
        return true;
      });
    }

    // Filter by specific service
    if (selectedService !== 'all') {
      const serviceId = parseInt(selectedService);
      filtered = filtered.filter(program => program.id === serviceId);
    }

    // Filter by duration
    if (selectedDuration !== 'all') {
      filtered = filtered.filter(program => {
        // Extract number from duration string (e.g., "28 Days" -> 28)
        const durationDays = parseInt(program.duration.split(' ')[0]) || 0;

        switch (selectedDuration) {
          case 'short':
            return durationDays >= 1 && durationDays <= 7;
          case 'medium':
            return durationDays >= 7 && durationDays <= 21; // 7-day programs appear in both short and medium
          case 'long':
            return durationDays >= 21; // 21-day programs appear in both medium and long
          default:
            return true;
        }
      });
    }

    // Filter by price range (min and max)
    if (selectedPriceRange[0] > 0 || selectedPriceRange[1] < 5000) {
      filtered = filtered.filter(program => {
        // Extract price value (e.g., "$1800" -> 1800)
        const price = parseInt(program.price.replace(/[^0-9]/g, '')) || 0;
        return price >= selectedPriceRange[0] && price <= selectedPriceRange[1];
      });
    }

    // Note: Location filter removed as requested
    // All program locations are set to "Shanti Yog Peeth, Rishikesh" in the display

    // Filter by month/year if selected (show programs from selected month onward)
    if (selectedMonth !== null) {
      filtered = filtered.filter((program: Program) => {
        return program.dates.some((dateStr: string) => {
          const date = new Date(dateStr);
          return (date.getMonth() >= selectedMonth && date.getFullYear() === selectedYear) ||
                 (date.getFullYear() > selectedYear);
        });
      });
    }

    // Sort by date
    const sorted = [...filtered].sort((a, b) => {
      const dateA = new Date(a.dates[0]);
      const dateB = new Date(b.dates[0]);
      return dateA.getTime() - dateB.getTime();
    });

    setFilteredPrograms(sorted);
    setCurrentPage(1); // Reset pagination on filter change
  }, [programs, selectedMonth, selectedYear, selectedEventType, selectedService, selectedDuration, selectedPriceRange]);

  // Update filtered programs when filters change
  useEffect(() => {
    filterPrograms();
  }, [filterPrograms]);



  // Handle month selection
  const handleMonthClick = (month: number) => {
    if (selectedMonth === month) {
      // If the same month is clicked again, deselect it
      setSelectedMonth(null);
    } else {
      setSelectedMonth(month);
    }

    // Scroll to the program list
    setTimeout(() => {
      if (programListRef.current) {
        programListRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100); // Small delay to ensure state updates first
  };

  // Handle year navigation
  const handlePreviousYear = () => {
    const newYear = selectedYear - 1;
    setSelectedYear(newYear);


  };

  const handleNextYear = () => {
    const newYear = selectedYear + 1;
    setSelectedYear(newYear);


  };

  // Get the display date for a program (now each program has exactly one date)
  const getDisplayDate = (program: Program): string => {
    return program.displayDates[0];
  };

  // Pagination logic
  const itemsPerPage = 10;
  const pageCount = Math.ceil(filteredPrograms.length / itemsPerPage);
  const paginatedPrograms = filteredPrograms.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);

    // First scroll to the element
    if (programListRef.current) {
      programListRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  return (
    <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Header Section */}
      <div className="bg-gradient-to-b from-amber-400/10 to-transparent dark:from-indigo-900/20 dark:to-transparent py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-6xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Upcoming <span className="text-amber-500 dark:text-amber-400">Programs</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Discover our upcoming teacher trainings, retreats, and online courses. Join us on the journey of transformation.
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-1/4 flex-shrink-0">
            <div className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg sticky top-16">
              <FilterBar
                eventTypes={eventTypes}
                services={services}
                selectedEventType={selectedEventType}
                selectedService={selectedService}
                onEventTypeChange={(type) => {
                  setSelectedEventType(type.toLowerCase().replace(' ', '-'));
                }}
                onServiceChange={setSelectedService}
                onDurationChange={setSelectedDuration}
                onPriceRangeChange={setSelectedPriceRange}
                selectedDuration={selectedDuration}
                selectedPriceRange={selectedPriceRange}
              />

              {/* Calendar Section */}
              <div className="mt-4 border-t border-slate-200 dark:border-slate-700 pt-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-base font-bold text-slate-900 dark:text-white flex items-center gap-1.5">
                    <Calendar className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                    Date Selection
                  </h3>

                  {selectedMonth !== null && (
                    <button
                      onClick={() => setSelectedMonth(null)}
                      className="text-xs text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300 font-medium"
                    >
                      Clear
                    </button>
                  )}
                </div>

                {/* Year Selector */}
                <div className="flex items-center justify-between mb-3 bg-amber-50 dark:bg-slate-700 rounded-md p-1.5">
                  <button
                    onClick={handlePreviousYear}
                    className="p-1 rounded-md hover:bg-amber-100 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 transition-colors"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </button>
                  <span className="text-base font-bold text-slate-900 dark:text-white">{selectedYear}</span>
                  <button
                    onClick={handleNextYear}
                    className="p-1 rounded-md hover:bg-amber-100 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 transition-colors"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </button>
                </div>

                {/* Month Grid */}
                <div className="grid grid-cols-4 gap-1 mb-3">
                  {Array.from({ length: 12 }, (_, i) => (
                    <MonthButton
                      key={i}
                      month={i}
                      year={selectedYear}
                      selectedMonth={selectedMonth}
                      selectedYear={selectedYear}
                      hasPrograms={monthHasPrograms(i, selectedYear)}
                      onClick={() => handleMonthClick(i)}
                    />
                  ))}
                </div>

                {selectedMonth !== null && (
                  <div className="mt-1 text-xs text-slate-600 dark:text-slate-400 italic">
                    From {monthNames[selectedMonth]} {selectedYear} onward
                  </div>
                )}
              </div>

              {/* Popular Program Card removed as requested */}
            </div>
          </div>

          {/* Program List */}
          <div className="lg:w-3/4">
            <div ref={programListRef} className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
              <div className="p-4 border-b border-slate-100 dark:border-slate-700">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">
                  {selectedMonth !== null
                    ? `Programs from ${monthNames[selectedMonth]} ${selectedYear} onward`
                    : 'All Upcoming Programs'}
                </h2>
              </div>

              {paginatedPrograms.length === 0 ? (
                <div className="p-6 text-center">
                  <p className="text-slate-600 dark:text-slate-400">
                    No programs found for the selected period. Please try another month or year.
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-slate-100 dark:divide-slate-700">
                  {paginatedPrograms.map((program) => (
  <div
    key={`${program.id}-${program.dates[0]}`}
    className="flex flex-col gap-4 md:gap-0 md:flex-row px-2 md:px-4 py-4 md:py-2 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors cursor-pointer rounded-lg mb-4 md:mb-0"
    onClick={(e) => {
      if (!(e.target instanceof HTMLAnchorElement || e.target instanceof HTMLButtonElement)) {
        window.location.href = `/${program.type === 'Teacher Training' || program.type === 'Online Course' ? 'courses' : 'retreats'}/${program.slug || program.id}`;
      }
    }}
  >
    {/* Program Image */}
    <div className="w-full md:w-1/5 flex-shrink-0 mb-2 md:mb-0 md:mr-4">
      <img
        src={program.image}
        alt={program.title}
        className="w-full h-48 md:h-auto aspect-square object-cover rounded-lg"
      />
    </div>
    {/* Program Details */}
    <div className="w-full md:w-2/4 flex-grow">
      <div className="flex items-center text-amber-500 dark:text-amber-400 mb-1">
        <Calendar className="w-4 h-4 mr-2" />
        <span className="font-bold">{getDisplayDate(program)}</span>
      </div>
      <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-1">
        {program.title}
      </h3>
      <div className="flex flex-wrap gap-2 mb-2">
        <span className="px-2 py-0.5 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-xs font-medium rounded-full">
          {program.type}
        </span>
        <span className="flex items-center px-2 py-0.5 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-xs font-medium rounded-full">
          <Clock className="w-3 h-3 mr-1" />
          {program.duration}
        </span>
        <span className="flex items-center px-2 py-0.5 bg-amber-100 dark:bg-slate-700 text-amber-800 dark:text-amber-300 text-xs font-medium rounded-full">
          <MapPin className="w-3 h-3 mr-1" />
          Shanti Yog Peeth, Rishikesh
        </span>
      </div>
      {/* Description removed for compact card */}
    </div>
    {/* Price and Action */}
    <div className="w-full md:w-1/4 flex flex-row md:flex-col justify-between md:items-end items-stretch gap-2 md:gap-0 mt-2 md:mt-0">
      <div className="text-lg font-bold text-amber-500 dark:text-amber-400 mb-0 md:mb-2 w-full md:w-auto text-right md:text-left">
        {program.price}
      </div>
      <div className="flex flex-row md:flex-col gap-2 w-full md:w-auto">
        <Link
          to={`/${program.type === 'Teacher Training' || program.type === 'Online Course' ? 'courses' : 'retreats'}/${program.slug || program.id}`}
          className="px-3 py-1.5 bg-white hover:bg-slate-50 dark:bg-slate-700 dark:hover:bg-slate-600 text-amber-500 dark:text-amber-400 border border-amber-500 dark:border-amber-400 rounded-lg font-medium transition-colors duration-300 text-sm text-center"
        >
          Learn More
        </Link>
        <Link
          to="/apply"
          className="px-3 py-1.5 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm text-center"
        >
          Apply Now
        </Link>
      </div>
    </div>
  </div>
))}
                </div>
              )}

              {/* Pagination Controls */}
              {pageCount > 1 && (
                <div className="flex justify-center p-4 border-t border-slate-100 dark:border-slate-700">
                  <div className="flex gap-2">
                    {Array.from({ length: pageCount }, (_, i) => (
                      <button
                        key={i + 1}
                        onClick={() => handlePageChange(i + 1)}
                        className={`px-3 py-1 rounded-lg ${
                          currentPage === i + 1
                            ? 'bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white'
                            : 'bg-white dark:bg-slate-700 text-slate-700 dark:text-slate-300 hover:bg-amber-100 dark:hover:bg-slate-600'
                        }`}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalendarPage;
