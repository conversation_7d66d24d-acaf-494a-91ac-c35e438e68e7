import React from 'react';
import { Mail, Calendar, MapPin, Flower2, Heart, Award, Book, Scroll, Star } from 'lucide-react';

interface Teacher {
  id: number;
  name: string;
  role: string;
  bio: string;
  image: string;
  specialties: string[];
  experience: string;
  schedule?: {
    day: string;
    classes: { time: string; name: string; level: string }[];
  }[];
}

const TeachersPage: React.FC = () => {
  const teachers: Teacher[] = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      role: 'Founder & Master Yogi',
      bio: 'With over 30 years of practice and study, <PERSON><PERSON><PERSON> brings profound wisdom and authenticity to his teachings. Trained in traditional Hatha and Kundalini yoga in Rishikesh, he has dedicated his life to sharing the transformative power of yoga with practitioners from around the world.',
      image: 'https://placehold.co/400x400/e2e8f0/1e293b?text=<PERSON><PERSON><PERSON>+<PERSON>',
      specialties: ['Hatha Yoga', 'Kundalini Yoga', 'Meditation', 'Philosophy'],
      experience: '30+ years',
      schedule: [
        {
          day: 'Monday',
          classes: [
            { time: '6:00 AM', name: 'Traditional Hatha', level: 'All Levels' },
            { time: '5:00 PM', name: 'Kunda<PERSON> Yoga', level: 'Intermediate' }
          ]
        },
        {
          day: 'Wednesday',
          classes: [
            { time: '6:00 AM', name: 'Meditation', level: 'All Levels' },
            { time: '5:00 PM', name: 'Philosophy Session', level: 'All Levels' }
          ]
        }
      ]
    },
    {
      id: 2,
      name: 'Priya Patel',
      role: 'Senior Yoga Instructor',
      bio: 'Priya combines her background in dance and movement therapy with traditional yoga practices to create flowing, mindful sequences. Her classes focus on alignment, breath awareness, and finding joy in movement. She specializes in Vinyasa and Yin yoga.',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Priya+Patel',
      specialties: ['Vinyasa Flow', 'Yin Yoga', 'Prenatal Yoga'],
      experience: '15 years'
    },
    {
      id: 3,
      name: 'David Chen',
      role: 'Ashtanga Specialist',
      bio: 'David discovered Ashtanga yoga during a spiritual journey to India and has been devoted to the practice ever since. His teaching style is dynamic yet compassionate, helping students build strength and flexibility while honoring their bodies\' limitations.',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=David+Chen',
      specialties: ['Ashtanga Yoga', 'Power Yoga', 'Handstands'],
      experience: '12 years'
    },
    {
      id: 4,
      name: 'Lakshmi Nair',
      role: 'Meditation & Pranayama Guide',
      bio: 'Lakshmi has studied with meditation masters throughout India and Tibet. Her gentle approach to breathwork and meditation helps students quiet their minds and connect with their inner wisdom. She leads our meditation retreats and specialized pranayama workshops.',
      image: 'https://placehold.co/1000x1000/e2e8f0/1e293b?text=Lakshmi+Nair',
      specialties: ['Meditation', 'Pranayama', 'Yoga Nidra', 'Sound Healing'],
      experience: '20 years'
    }
  ];

  const teachingPhilosophies = [
    {
      title: "Traditional Wisdom",
      description: "Our teaching is deeply rooted in ancient yogic traditions, passed down through generations of practitioners.",
      icon: <Scroll className="w-8 h-8 text-amber-400 dark:text-amber-300" />
    },
    {
      title: "Modern Understanding",
      description: "We bridge traditional practices with contemporary science and psychology for a comprehensive approach.",
      icon: <Book className="w-8 h-8 text-amber-400 dark:text-amber-300" />
    },
    {
      title: "Individual Growth",
      description: "Each student's journey is unique. We provide personalized guidance to support your evolution.",
      icon: <Star className="w-8 h-8 text-amber-400 dark:text-amber-300" />
    }
  ];

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Hero Section with Sanskrit-inspired background pattern */}
      <div className="relative bg-[url('/images/pattern-bg.jpg')] bg-cover bg-center bg-no-repeat">
        <div className="absolute inset-0 bg-gradient-to-b from-amber-400/20 to-slate-900/90 dark:from-indigo-900/40 dark:to-slate-900/90"></div>
        <div className="relative container mx-auto px-4 md:px-6 pt-32 pb-24 text-center">
          <div className="max-w-3xl mx-auto">
            <div className="inline-block mb-6">
              <Flower2 className="w-16 h-16 text-amber-400 dark:text-amber-300" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Meet Our <span className="text-amber-400 dark:text-amber-300">Sacred Guides</span>
            </h1>
            <p className="text-xl text-white/90 mb-8">
              Our teachers carry forward the ancient wisdom of yoga, offering guidance on your journey 
              to self-discovery and inner transformation.
            </p>
            <div className="flex flex-wrap justify-center gap-8 mt-12">
              <div className="flex items-center text-white/90">
                <MapPin className="w-5 h-5 mr-2 text-amber-400" />
                <span>Rishikesh, India</span>
              </div>
              <div className="flex items-center text-white/90">
                <Award className="w-5 h-5 mr-2 text-amber-400" />
                <span>Yoga Alliance Certified</span>
              </div>
              <div className="flex items-center text-white/90">
                <Heart className="w-5 h-5 mr-2 text-amber-400" />
                <span>Traditional Teachings</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Teaching Philosophy Section */}
      <section className="py-20 bg-white dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
              Our Teaching <span className="text-amber-500 dark:text-amber-400">Philosophy</span>
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              At Shanti Yog Peeth, our approach to teaching combines time-honored traditions 
              with modern understanding, creating a transformative learning experience.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {teachingPhilosophies.map((philosophy, index) => (
              <div key={index} className="bg-slate-50 dark:bg-slate-800 p-8 rounded-xl text-center">
                <div className="inline-block p-4 bg-amber-100 dark:bg-amber-900/20 rounded-full mb-6">
                  {philosophy.icon}
                </div>
                <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">
                  {philosophy.title}
                </h3>
                <p className="text-slate-600 dark:text-slate-300">
                  {philosophy.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Teacher Profiles */}
      <div className="container mx-auto px-4 md:px-6 py-20">
        {/* Featured Teacher - Founder */}
        <div className="max-w-7xl mx-auto mb-24">
          <div className="bg-white dark:bg-slate-800/50 rounded-2xl overflow-hidden backdrop-blur-sm">
            <div className="grid md:grid-cols-2 gap-0">
              <div className="relative aspect-[4/3] md:aspect-auto">
                <img 
                  src={teachers[0].image}
                  alt={teachers[0].name}
                  className="absolute inset-0 w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/50 to-transparent"></div>
                <div className="absolute bottom-0 left-0 p-8 text-white">
                  <h2 className="text-3xl font-bold mb-2">{teachers[0].name}</h2>
                  <p className="text-amber-400 text-lg mb-4">{teachers[0].role}</p>
                  <div className="flex items-center text-white/80">
                    <Calendar className="w-5 h-5 mr-2" />
                    <span>{teachers[0].experience} of Teaching</span>
                  </div>
                </div>
              </div>
              <div className="p-8 md:p-12">
                <div className="prose prose-lg dark:prose-invert max-w-none">
                  <p className="text-slate-600 dark:text-slate-300 leading-relaxed mb-6">
                    {teachers[0].bio}
                  </p>
                  <div className="mb-8">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Areas of Expertise</h3>
                    <div className="flex flex-wrap gap-2">
                      {teachers[0].specialties.map((specialty, index) => (
                        <span 
                          key={index}
                          className="px-4 py-1.5 bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 rounded-full text-sm"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <a 
                      href={`mailto:${teachers[0].name.toLowerCase().replace(' ', '.')}@yogacapital.org`}
                      className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-400/10 dark:hover:bg-amber-400/20 text-slate-900 dark:text-amber-300 rounded-lg transition-colors duration-300"
                    >
                      <Mail className="w-5 h-5" />
                      <span>Connect</span>
                    </a>
                    <button 
                      className="inline-flex items-center justify-center gap-2 px-6 py-3 border border-amber-400 dark:border-amber-400/50 text-amber-600 dark:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-400/10 rounded-lg transition-colors duration-300"
                    >
                      <Calendar className="w-5 h-5" />
                      <span>Book Private Session</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Teaching Statistics */}
        <section className="py-16 bg-amber-50/50 dark:bg-slate-800/30">
          <div className="container mx-auto px-4 md:px-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="text-4xl font-bold text-amber-500 dark:text-amber-400 mb-2">5000+</div>
                <p className="text-slate-600 dark:text-slate-400">Students Trained</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-amber-500 dark:text-amber-400 mb-2">15+</div>
                <p className="text-slate-600 dark:text-slate-400">Years Experience</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-amber-500 dark:text-amber-400 mb-2">12</div>
                <p className="text-slate-600 dark:text-slate-400">Expert Teachers</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold text-amber-500 dark:text-amber-400 mb-2">8</div>
                <p className="text-slate-600 dark:text-slate-400">Yoga Styles</p>
              </div>
            </div>
          </div>
        </section>

        {/* Other Teachers Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teachers.slice(1).map((teacher) => (
            <div 
              key={teacher.id}
              className="group bg-white dark:bg-slate-800/50 rounded-xl overflow-hidden backdrop-blur-sm hover:shadow-xl transition-all duration-500"
            >
              <div className="relative aspect-square">
                <img 
                  src={teacher.image}
                  alt={teacher.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-slate-900/50 to-transparent"></div>
                <div className="absolute bottom-0 left-0 p-6">
                  <h3 className="text-2xl font-bold text-white mb-2">{teacher.name}</h3>
                  <p className="text-amber-400 mb-3">{teacher.role}</p>
                  <div className="flex items-center text-white/80">
                    <Calendar className="w-4 h-4 mr-2" />
                    <span>{teacher.experience} of Teaching</span>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2 mb-4">
                    {teacher.specialties.map((specialty, index) => (
                      <span 
                        key={index}
                        className="px-3 py-1 bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 rounded-full text-sm"
                      >
                        {specialty}
                      </span>
                    ))}
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 line-clamp-3">
                    {teacher.bio}
                  </p>
                </div>
                <div className="flex gap-3">
                  <a 
                    href={`mailto:${teacher.name.toLowerCase().replace(' ', '.')}@yogacapital.org`}
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-amber-400/10 dark:hover:bg-amber-400/20 text-slate-900 dark:text-amber-300 rounded-lg transition-colors duration-300 text-sm"
                  >
                    <Mail className="w-4 h-4" />
                    <span>Connect</span>
                  </a>
                  <button 
                    className="flex-1 flex items-center justify-center gap-2 px-4 py-2 border border-amber-400 dark:border-amber-400/50 text-amber-600 dark:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-400/10 rounded-lg transition-colors duration-300 text-sm"
                  >
                    <Calendar className="w-4 h-4" />
                    <span>Book Session</span>
                  </button>
                </div>
                {teacher.schedule && (
                  <div className="mt-4 p-4 border-t border-slate-100 dark:border-slate-700">
                    <h4 className="text-sm font-semibold text-slate-900 dark:text-white mb-3">
                      Upcoming Classes
                    </h4>
                    <div className="space-y-2">
                      {teacher.schedule[0].classes.map((classItem, index) => (
                        <div 
                          key={index} 
                          className="flex items-center justify-between text-sm"
                        >
                          <span className="text-slate-600 dark:text-slate-300">{classItem.name}</span>
                          <span className="text-amber-500 dark:text-amber-400">{classItem.time}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Join Our Team Section */}
        <section className="py-20 bg-gradient-to-b from-amber-50/50 to-white dark:from-slate-800/30 dark:to-slate-900">
          <div className="container mx-auto px-4 md:px-6">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
                Join Our <span className="text-amber-500 dark:text-amber-400">Teaching Family</span>
              </h2>
              <p className="text-slate-600 dark:text-slate-300 mb-8">
                We're always looking for experienced yoga teachers who share our passion for authentic yoga
                and commitment to transformative teaching. If you resonate with our philosophy, we'd love to hear from you.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center gap-2 px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-400/10 dark:hover:bg-amber-400/20 text-slate-900 dark:text-amber-300 rounded-lg transition-colors duration-300"
                >
                  <Mail className="w-5 h-5" />
                  <span>Send Your Application</span>
                </a>
                <a 
                  href="/teaching-positions"
                  className="inline-flex items-center justify-center gap-2 px-6 py-3 border border-amber-400 dark:border-amber-400/50 text-amber-600 dark:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-400/10 rounded-lg transition-colors duration-300"
                >
                  <Book className="w-5 h-5" />
                  <span>View Open Positions</span>
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Quote Section */}
        <div className="max-w-4xl mx-auto mt-24 text-center">
          <div className="relative">
            <div className="absolute -top-6 left-1/2 -translate-x-1/2">
              <Flower2 className="w-12 h-12 text-amber-400 dark:text-amber-300 opacity-50" />
            </div>
            <blockquote className="text-2xl text-slate-700 dark:text-slate-300 italic mt-8">
              "In learning you will teach, and in teaching you will learn."
            </blockquote>
            <p className="text-amber-500 dark:text-amber-400 mt-4">- Phil Collins</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeachersPage;