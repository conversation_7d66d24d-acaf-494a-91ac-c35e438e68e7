import React from 'react';
import { Helmet } from 'react-helmet-async';
import Hero from '../components/Hero';
import About from '../components/About';
import YogaStyles from '../components/YogaStyles';
import Teachers from '../components/Teachers';
import Testimonials from '../components/Testimonials';
import FeaturedCourses from '../components/FeaturedCourses';
import Gallery from '../components/Gallery';

const HomePage: React.FC = () => {
  return (
    <div>
      <Helmet>
        <title><PERSON><PERSON> | Yoga Teacher Training in Rishikesh</title>
        <meta name="description" content="Join <PERSON> for authentic yoga teacher training in Rishikesh. Deepen your practice, become a certified yoga teacher, and experience holistic wellness." />
        <meta property="og:title" content="<PERSON><PERSON> | Yoga Teacher Training in Rishikesh" />
        <meta property="og:description" content="Join <PERSON>th for authentic yoga teacher training in Rishikesh. Deepen your practice, become a certified yoga teacher, and experience holistic wellness." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://yourdomain.com/" />
        <script type="application/ld+json">
          {`
            {
              "@context": "https://schema.org",
              "@type": "Course",
              "name": "200 Hour Yoga Teacher Training",
              "description": "Join our 200 hour YTT in Rishikesh, deepen your practice and become a certified teacher.",
              "provider": {
                "@type": "Organization",
                "name": "Shanti Yog Peeth",
                "url": "https://yourdomain.com"
              }
            }
          `}
        </script>
      </Helmet>
      <Hero />
      <FeaturedCourses />
      <About />
      <YogaStyles />
      <Teachers />
      <Testimonials />
      <Gallery />
    </div>
  );
};

export default HomePage;
