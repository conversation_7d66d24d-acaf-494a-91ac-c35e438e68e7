import React, { useState } from 'react';
import { Clock, Users, DollarSign, Calendar, BookOpen, Video, Star, Award, ArrowRight, X } from 'lucide-react';
import { Link } from 'react-router-dom';

interface TherapyClass {
  id: number;
  name: string;
  duration: string;
  price: string;
  description: string;
  image: string;
  benefits: string[];
}

interface Program {
  id: number;
  name: string;
  duration: string;
  price: string;
  description: string;
  image: string;
  features: string[];
}

const OnlineServicesPage: React.FC = () => {
  const [selectedTherapyClass, setSelectedTherapyClass] = useState<TherapyClass | null>(null);
  const [selectedProgram, setSelectedProgram] = useState<Program | null>(null);

  const therapyClasses: TherapyClass[] = [
    {
      id: 1,
      name: "Yoga for Stress Relief",
      duration: "45 min",
      price: "$15",
      description: "A gentle yoga practice designed to calm the nervous system, reduce anxiety, and promote relaxation. Suitable for all levels.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Stress+Relief",
      benefits: [
        "Reduces cortisol levels",
        "Improves sleep quality",
        "Decreases anxiety symptoms",
        "Enhances mood and mental clarity"
      ]
    },
    {
      id: 2,
      name: "Face Yoga",
      duration: "30 min",
      price: "$12",
      description: "Targeted facial exercises that tone muscles, reduce tension, and promote a natural facelift. Learn techniques to maintain a youthful appearance.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Face+Yoga",
      benefits: [
        "Tones facial muscles",
        "Reduces fine lines",
        "Improves blood circulation",
        "Releases tension in jaw and forehead"
      ]
    },
    {
      id: 3,
      name: "Yoga for Fat Loss",
      duration: "60 min",
      price: "$18",
      description: "Dynamic sequence combining strength-building poses, cardio elements, and metabolic boosters. Designed to burn calories and build lean muscle.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Fat+Loss+Yoga",
      benefits: [
        "Increases metabolism",
        "Burns calories efficiently",
        "Builds lean muscle",
        "Improves body composition"
      ]
    },
    {
      id: 4,
      name: "Immunity Building Yoga",
      duration: "45 min",
      price: "$15",
      description: "Strengthen your immune system through specific yoga poses, breathing techniques, and relaxation practices that enhance overall health.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Immunity+Yoga",
      benefits: [
        "Boosts immune function",
        "Reduces inflammation",
        "Improves lymphatic flow",
        "Enhances respiratory health"
      ]
    },
    {
      id: 5,
      name: "Yoga for Strength Building",
      duration: "60 min",
      price: "$18",
      description: "Build functional strength through power yoga sequences. Focus on core stability, upper body strength, and lower body power.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Strength+Yoga",
      benefits: [
        "Builds functional strength",
        "Improves core stability",
        "Enhances muscle definition",
        "Increases physical endurance"
      ]
    },
    {
      id: 6,
      name: "Yoga for Back Pain",
      duration: "45 min",
      price: "$15",
      description: "Therapeutic yoga sequence designed to relieve back pain, improve spinal alignment, and strengthen supportive muscles.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Back+Pain+Relief",
      benefits: [
        "Relieves chronic back pain",
        "Improves spinal alignment",
        "Strengthens core muscles",
        "Increases back flexibility"
      ]
    },
    {
      id: 7,
      name: "Yoga for Flexibility",
      duration: "60 min",
      price: "$18",
      description: "Comprehensive practice focusing on safe, effective stretching techniques to increase range of motion throughout the body.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Flexibility+Yoga",
      benefits: [
        "Increases joint mobility",
        "Improves overall flexibility",
        "Prevents injuries",
        "Enhances athletic performance"
      ]
    },
    {
      id: 8,
      name: "Yoga for Better Sleep",
      duration: "30 min",
      price: "$12",
      description: "Evening practice designed to calm the mind, release physical tension, and prepare the body for restful sleep.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Sleep+Yoga",
      benefits: [
        "Reduces insomnia symptoms",
        "Improves sleep quality",
        "Calms an overactive mind",
        "Establishes healthy sleep routine"
      ]
    },
    {
      id: 9,
      name: "Yoga for Digestive Health",
      duration: "45 min",
      price: "$15",
      description: "Targeted practice to stimulate digestive organs, improve gut function, and relieve digestive discomfort.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=Digestive+Yoga",
      benefits: [
        "Improves digestion",
        "Reduces bloating",
        "Relieves constipation",
        "Balances digestive system"
      ]
    }
  ];

  const programs: Program[] = [
    {
      id: 1,
      name: "15-Day Mind-Body Reset",
      duration: "15 Days",
      price: "$120",
      description: "A comprehensive program designed to reset your mental and physical health. Includes daily yoga, meditation, and mindfulness practices.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=15-Day+Reset",
      features: [
        "15 daily yoga sessions (30-45 min)",
        "Guided meditation practices",
        "Stress management techniques",
        "Nutritional guidance",
        "Progress tracking tools"
      ]
    },
    {
      id: 2,
      name: "30-Day Holistic Wellness Journey",
      duration: "30 Days",
      price: "$225",
      description: "Transform your wellness routine with this month-long program addressing physical, mental, and spiritual dimensions of health.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=30-Day+Wellness",
      features: [
        "30 progressive yoga sessions",
        "Weekly live Q&A",
        "Personalized feedback",
        "Wellness habit building",
        "Community support group"
      ]
    },
    {
      id: 3,
      name: "45-Day Strength & Serenity Program",
      duration: "45 Days",
      price: "$320",
      description: "Balance building physical strength with mental calm in this comprehensive 45-day program suitable for intermediate practitioners.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=45-Day+Strength",
      features: [
        "Progressive strength-building sequences",
        "Advanced meditation techniques",
        "Breath work for mental clarity",
        "Two personal coaching sessions",
        "Custom practice recommendations"
      ]
    },
    {
      id: 4,
      name: "60-Day Total Transformation",
      duration: "60 Days",
      price: "$410",
      description: "Our most comprehensive transformation program addressing all aspects of physical and mental wellbeing for lasting change.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=60-Day+Transformation",
      features: [
        "Daily yoga practices (varied styles)",
        "Personalized nutrition plan",
        "Weekly progress assessments",
        "Three one-on-one coaching calls",
        "Lifestyle redesign guidance"
      ]
    },
    {
      id: 5,
      name: "90-Day Ultimate Wellness Challenge",
      duration: "90 Days",
      price: "$590",
      description: "The ultimate commitment to transformation with three months of structured, progressive practice and comprehensive support.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=90-Day+Challenge",
      features: [
        "Complete yoga system immersion",
        "Monthly personalized routines",
        "Biweekly one-on-one coaching",
        "Advanced practice techniques",
        "Lifetime access to program materials"
      ]
    },
    {
      id: 6,
      name: "21-Day Immunity Boost Camp",
      duration: "21 Days",
      price: "$170",
      description: "Strengthen your immune system through targeted yoga practices, breathing techniques, and lifestyle recommendations.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=21-Day+Immunity",
      features: [
        "Immune-enhancing yoga sequences",
        "Pranayama for respiratory health",
        "Ayurvedic immunity protocols",
        "Stress reduction techniques",
        "Anti-inflammatory nutrition guide"
      ]
    },
    {
      id: 7,
      name: "30-Day Stress Detox",
      duration: "30 Days",
      price: "$230",
      description: "Systematic approach to reducing stress, anxiety, and tension while building resilience and emotional balance.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=30-Day+Stress+Detox",
      features: [
        "Stress-releasing yoga practices",
        "Progressive relaxation techniques",
        "Mindfulness training",
        "Sleep enhancement protocols",
        "Digital detox guidance"
      ]
    },
    {
      id: 8,
      name: "45-Day Weight Management Plan",
      duration: "45 Days",
      price: "$330",
      description: "Balanced approach to weight management combining energetic yoga, mindful eating, and sustainable lifestyle changes.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=45-Day+Weight+Management",
      features: [
        "Metabolism-boosting yoga sequences",
        "Portion awareness training",
        "Emotional eating workshop",
        "Custom exercise progression",
        "Long-term habit formation"
      ]
    },
    {
      id: 9,
      name: "60-Day Flexibility & Balance Course",
      duration: "60 Days",
      price: "$420",
      description: "Systematic progression to dramatically improve flexibility, balance, and functional movement patterns.",
      image: "https://placehold.co/600x400/e2e8f0/1e293b?text=60-Day+Flexibility",
      features: [
        "Progressive flexibility training",
        "Joint mobility protocols",
        "Balance and stability practices",
        "Posture correction techniques",
        "Sports performance enhancement"
      ]
    }
  ];

  return (
    <div className="pt-18 min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-b from-amber-400/20 to-transparent dark:from-indigo-900/30 dark:to-transparent py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-6">
          <h1 className="text-4xl md:text-6xl font-bold text-center text-slate-900 dark:text-white mb-6">
            Online <span className="text-amber-500 dark:text-amber-400">Services</span>
          </h1>
          <p className="text-xl text-center text-slate-700 dark:text-slate-300 max-w-3xl mx-auto">
            Experience authentic yoga guidance from anywhere in the world with our comprehensive online programs and live classes.
          </p>
          <div className="flex justify-center mt-8">
            <div className="inline-flex space-x-2 items-center rounded-full px-4 py-2 bg-white/30 dark:bg-slate-800/30 backdrop-blur-sm">
              <Video className="w-5 h-5 text-amber-600 dark:text-amber-400" />
              <span className="text-slate-700 dark:text-slate-300 text-sm">Live & On-Demand</span>
              <span className="text-slate-400 dark:text-slate-600">|</span>
              <Calendar className="w-5 h-5 text-amber-600 dark:text-amber-400" />
              <span className="text-slate-700 dark:text-slate-300 text-sm">Flexible Scheduling</span>
              <span className="text-slate-400 dark:text-slate-600">|</span>
              <Users className="w-5 h-5 text-amber-600 dark:text-amber-400" />
              <span className="text-slate-700 dark:text-slate-300 text-sm">Expert Guidance</span>
            </div>
          </div>
        </div>
      </div>

      {/* Issue-Specific Yoga Therapy Classes */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center">
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-10 mr-4"></div>
              <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                Online Classes
              </span>
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-10 ml-4"></div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 mt-2 text-slate-900 dark:text-white">
              Issue-Specific <span className="text-amber-500 dark:text-amber-400">Yoga Therapy</span> Classes
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Targeted yoga sessions designed to address specific health concerns and wellness goals.
              Join us for live sessions or access our on-demand library.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {therapyClasses.map((therapyClass) => (
              <div 
                key={therapyClass.id}
                className="group bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => setSelectedTherapyClass(therapyClass)}
              >
                <div className="relative">
                  <img 
                    src={therapyClass.image} 
                    alt={therapyClass.name} 
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white text-xs font-bold px-2 py-1 rounded-full">
                    {therapyClass.duration}
                  </div>
                  <div className="absolute top-4 right-4 bg-white dark:bg-slate-800 text-amber-500 dark:text-amber-400 text-xs font-bold px-2 py-1 rounded-full">
                    {therapyClass.price} / session
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-2">
                    {therapyClass.name}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2">
                    {therapyClass.description}
                  </p>
                  <div className="flex justify-between items-center space-x-4">
                    <button 
                      className="flex-1 px-4 py-2 bg-white hover:bg-slate-50 dark:bg-slate-700 dark:hover:bg-slate-600 text-amber-500 dark:text-amber-400 border border-amber-500 dark:border-amber-400 rounded-lg font-medium transition-colors duration-300 text-sm text-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedTherapyClass(therapyClass);
                      }}
                    >
                      Learn More
                    </button>
                    <button 
                      className="flex-1 px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm text-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Book Now logic
                      }}
                    >
                      Book Now
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Day-wise Multi-Issue Programs */}
      <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center">
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-10 mr-4"></div>
              <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">
                Comprehensive Programs
              </span>
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-10 ml-4"></div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 mt-2 text-slate-900 dark:text-white">
              Day-wise <span className="text-amber-500 dark:text-amber-400">Multi-Issue</span> Programs
            </h2>
            <p className="text-slate-600 dark:text-slate-300 max-w-3xl mx-auto">
              Structured programs designed to address multiple aspects of your wellness journey
              with daily practices, guidance, and support.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {programs.map((program) => (
              <div 
                key={program.id}
                className="group bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                onClick={() => setSelectedProgram(program)}
              >
                <div className="relative">
                  <img 
                    src={program.image} 
                    alt={program.name} 
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white text-xs font-bold px-2 py-1 rounded-full">
                    {program.duration}
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                      {program.name}
                    </h3>
                    <span className="text-amber-500 dark:text-amber-400 font-bold">
                      {program.price}
                    </span>
                  </div>
                  <p className="text-slate-600 dark:text-slate-300 text-sm mb-4 line-clamp-2">
                    {program.description}
                  </p>
                  <div className="flex justify-between items-center mt-6 space-x-4">
                    <button 
                      className="flex-1 px-4 py-2 bg-white hover:bg-slate-50 dark:bg-slate-700 dark:hover:bg-slate-600 text-amber-500 dark:text-amber-400 border border-amber-500 dark:border-amber-400 rounded-lg font-medium transition-colors duration-300 text-sm text-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedProgram(program);
                      }}
                    >
                      Learn More
                    </button>
                    <button 
                      className="flex-1 px-4 py-2 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-sm text-center"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Book Now logic
                      }}
                    >
                      Book Now
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Virtual Teacher Training Section */}
      <section className="py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="bg-gradient-to-br from-amber-400/30 to-amber-600/20 dark:from-indigo-800/30 dark:to-purple-800/20 rounded-3xl p-8 md:p-12 relative overflow-hidden">
            {/* Decorative Elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
              <div className="absolute -top-16 -right-16 w-40 h-40 bg-amber-400/20 dark:bg-indigo-600/20 rounded-full"></div>
              <div className="absolute top-40 -right-20 w-60 h-60 bg-amber-500/10 dark:bg-indigo-700/10 rounded-full"></div>
              <div className="absolute -bottom-20 -left-20 w-60 h-60 bg-amber-300/20 dark:bg-purple-600/20 rounded-full"></div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative z-10">
              {/* Left Content */}
              <div>
                <div className="flex items-center mb-6">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-amber-400 dark:bg-indigo-600">
                    <Video className="h-6 w-6 text-white" />
                  </div>
                  <div className="ml-4">
                    <span className="block text-sm font-semibold text-amber-600 dark:text-amber-400 uppercase tracking-wider">
                      Flagship Online Program
                    </span>
                  </div>
                </div>
                
                <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-6">
                  200-Hour Virtual<br />
                  <span className="text-amber-500 dark:text-amber-400">Yoga Teacher Training</span>
                </h2>
                
                <p className="text-slate-700 dark:text-slate-300 mb-8">
                  Begin your journey to becoming a certified yoga teacher from anywhere in the world. 
                  Our comprehensive virtual program offers the same depth and quality as our in-person 
                  training with the flexibility of online learning.
                </p>
                
                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <Clock className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">120 Days Program</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">
                        Approximately 2 hours of daily commitment
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Award className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">Yoga Alliance Certified</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">
                        Internationally recognized certification
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Users className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">Interactive Learning</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">
                        Live sessions, forum discussions, and personalized feedback
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Star className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-3 mt-1" />
                    <div>
                      <span className="font-medium text-slate-900 dark:text-white">Comprehensive Curriculum</span>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">
                        Asana, philosophy, anatomy, teaching methodology, and business of yoga
                      </p>
                    </div>
                  </li>
                </ul>
                
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <Link 
                    to="/courses/virtual-teacher-training"
                    className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300 text-center"
                  >
                    Learn More
                  </Link>
                  <Link 
                    to="/apply"
                    className="px-6 py-3 bg-white hover:bg-slate-50 dark:bg-slate-800 dark:hover:bg-slate-700 text-amber-500 dark:text-amber-400 border border-amber-400 dark:border-indigo-600 rounded-lg font-medium transition-colors duration-300 text-center"
                  >
                    Apply Now
                  </Link>
                </div>
              </div>
              
              {/* Right Image */}
              <div className="relative">
                <div className="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden shadow-xl">
                  <img 
                    src="https://placehold.co/800x600/e2e8f0/1e293b?text=Virtual+YTT" 
                    alt="Virtual Yoga Teacher Training"
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Price Badge */}
                <div className="absolute -bottom-6 -right-6 bg-amber-400 dark:bg-indigo-600 text-slate-900 dark:text-white px-6 py-4 rounded-lg shadow-lg">
                  <div className="text-sm font-medium">Starting at</div>
                  <div className="text-2xl font-bold">$1,999</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call-to-Action Section */}
      <section className="py-16 bg-slate-50 dark:bg-slate-800/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="bg-gradient-to-r from-amber-500 to-amber-400 dark:from-indigo-700 dark:to-indigo-600 rounded-xl p-8 md:p-12 shadow-lg">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Begin Your Transformation Today
              </h2>
              <p className="text-white/90 text-lg mb-8">
                Whether you're looking to deepen your practice, address specific health concerns, 
                or become a certified teacher, our online programs provide the guidance and support you need on your journey.
              </p>
              <Link 
                to="/apply"
                className="inline-flex items-center px-8 py-4 bg-white text-amber-500 dark:text-indigo-600 rounded-lg font-medium text-lg shadow-md hover:shadow-lg transition-all duration-300"
              >
                Apply Now
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Therapy Class Details Modal */}
      {selectedTherapyClass && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-slate-800 rounded-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">{selectedTherapyClass.name}</h2>
                <button
                  onClick={() => setSelectedTherapyClass(null)}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              
              <div className="space-y-6">
                <div className="aspect-w-16 aspect-h-9">
                  <img 
                    src={selectedTherapyClass.image} 
                    alt={selectedTherapyClass.name} 
                    className="w-full h-full object-cover rounded-lg"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                    <Clock className="w-5 h-5 text-amber-500 dark:text-amber-400 mb-2" />
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedTherapyClass.duration}</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">Duration</div>
                  </div>
                  <div className="p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                    <DollarSign className="w-5 h-5 text-amber-500 dark:text-amber-400 mb-2" />
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedTherapyClass.price}</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">Per Session</div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Description</h3>
                  <p className="text-slate-600 dark:text-slate-300">{selectedTherapyClass.description}</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Benefits</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {selectedTherapyClass.benefits.map((benefit, index) => (
                      <div 
                        key={index}
                        className="flex items-center space-x-2 text-slate-600 dark:text-slate-300"
                      >
                        <Star className="w-5 h-5 text-amber-500 dark:text-amber-400" />
                        <span>{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-end space-x-4">
                <button
                  onClick={() => setSelectedTherapyClass(null)}
                  className="px-6 py-3 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-colors duration-300 hover:bg-slate-50 dark:hover:bg-slate-700"
                >
                  Cancel
                </button>
                <button 
                  className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300"
                >
                  Book This Class
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Program Details Modal */}
      {selectedProgram && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-slate-800 rounded-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-6">
                <h2 className="text-2xl font-bold text-slate-900 dark:text-white">{selectedProgram.name}</h2>
                <button
                  onClick={() => setSelectedProgram(null)}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              
              <div className="space-y-6">
                <div className="aspect-w-16 aspect-h-9">
                  <img 
                    src={selectedProgram.image} 
                    alt={selectedProgram.name} 
                    className="w-full h-full object-cover rounded-lg"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                    <Calendar className="w-5 h-5 text-amber-500 dark:text-amber-400 mb-2" />
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedProgram.duration}</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">Program Length</div>
                  </div>
                  <div className="p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                    <DollarSign className="w-5 h-5 text-amber-500 dark:text-amber-400 mb-2" />
                    <div className="text-sm font-medium text-slate-900 dark:text-white">{selectedProgram.price}</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">Total Investment</div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Description</h3>
                  <p className="text-slate-600 dark:text-slate-300">{selectedProgram.description}</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-3">Program Features</h3>
                  <div className="grid grid-cols-1 gap-3">
                    {selectedProgram.features.map((feature, index) => (
                      <div 
                        key={index}
                        className="flex items-center space-x-2 text-slate-600 dark:text-slate-300"
                      >
                        <BookOpen className="w-5 h-5 text-amber-500 dark:text-amber-400" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="mt-8 flex justify-end space-x-4">
                <button
                  onClick={() => setSelectedProgram(null)}
                  className="px-6 py-3 border border-slate-300 dark:border-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-colors duration-300 hover:bg-slate-50 dark:hover:bg-slate-700"
                >
                  Cancel
                </button>
                <button 
                  className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium transition-colors duration-300"
                >
                  Enroll Now
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OnlineServicesPage;