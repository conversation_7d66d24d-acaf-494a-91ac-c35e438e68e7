import React from 'react';
import { Helmet } from 'react-helmet-async';
import About from '../components/About';
import Gallery from '../components/Gallery';

const AboutPage: React.FC = () => {
  return (
    <div>
      <Helmet>
        <title>About <PERSON><PERSON> | Our Journey & Philosophy</title>
        <meta name="description" content="Learn about <PERSON><PERSON>'s journey, philosophy, and commitment to authentic yoga in Rishikesh. Discover our story and values." />
        <meta property="og:title" content="About <PERSON><PERSON>g <PERSON> | Our Journey & Philosophy" />
        <meta property="og:description" content="Learn about <PERSON><PERSON>'s journey, philosophy, and commitment to authentic yoga in Rishikesh. Discover our story and values." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://yourdomain.com/about" />
      </Helmet>
      {/* Hero Section */}
      <div className="relative bg-[url('/images/hero.webp')] bg-cover bg-center bg-no-repeat">
        <div className="absolute inset-0 bg-gradient-to-b from-amber-400/20 to-slate-900/90 dark:from-indigo-900/40 dark:to-slate-900/90"></div>
        <div className="relative container mx-auto px-4 md:px-6 pt-32 pb-24 text-center">
          <div className="max-w-3xl mx-auto">
            <div className="inline-block mb-6">
              {/* Flower2 icon from lucide-react */}
              {/* If not imported, add: import { Flower2 } from 'lucide-react'; */}
              <svg xmlns="http://www.w3.org/2000/svg" className="w-16 h-16 text-amber-400 dark:text-amber-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path d="M12 2v2m0 16v2m10-10h-2M4 12H2m15.07-7.07l-1.41 1.41M6.34 17.66l-1.41 1.41m12.02 0l-1.41-1.41M6.34 6.34L4.93 4.93" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              About <span className="text-amber-400 dark:text-amber-300">Shanti Yog Peeth</span>
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Discover our journey, philosophy, and commitment to authentic yoga practices in the spiritual heart of Rishikesh.
            </p>
          </div>
        </div>
      </div>
      
      <About />
      
      <section className="py-20 md:py-32">
        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="flex items-center mb-4">
                <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
                <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Our Philosophy</span>
              </div>
              
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
                Embracing the <span className="text-amber-500 dark:text-amber-400">Eight Limbs</span> of Yoga
              </h2>
              
              <p className="text-slate-700 dark:text-slate-300 mb-6 leading-relaxed">
                At Shanti Yog Peeth, we believe in the holistic approach to yoga as outlined in Patanjali's Yoga Sutras. Our teaching methodology encompasses all eight limbs of yoga, creating a comprehensive path for spiritual growth and self-discovery.
              </p>
              
              <p className="text-slate-700 dark:text-slate-300 mb-8 leading-relaxed">
                We honor the ancient traditions while making these practices accessible to modern practitioners. Our approach balances physical postures with breath control, meditation, ethical principles, and the pursuit of higher consciousness.
              </p>
              
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 text-white flex items-center justify-center mr-3 mt-0.5">1</span>
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">Yama</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Ethical standards and sense of integrity</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 text-white flex items-center justify-center mr-3 mt-0.5">2</span>
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">Niyama</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Self-discipline and spiritual observances</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 text-white flex items-center justify-center mr-3 mt-0.5">3</span>
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">Asana</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Physical postures and body awareness</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 text-white flex items-center justify-center mr-3 mt-0.5">4</span>
                  <div>
                    <span className="font-medium text-slate-900 dark:text-white">Pranayama</span>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">Breath control and energy regulation</p>
                  </div>
                </li>
              </ul>
            </div>
            
            <div className="relative">
              <img 
                src="../images/about.webp" 
                alt="Yoga philosophy in practice" 
                className="rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-amber-400 dark:bg-indigo-600 rounded-lg -z-10"></div>
            </div>
          </div>
        </div>
      </section>
      
      <section className="py-20 md:py-32 bg-amber-50/50 dark:bg-slate-800/30">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-4">
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 mr-4"></div>
              <span className="text-amber-500 dark:text-amber-400 uppercase tracking-wider text-sm font-semibold">Our History</span>
              <div className="h-px bg-amber-400 dark:bg-indigo-500 w-16 ml-4"></div>
            </div>
            
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-slate-900 dark:text-white">
              The <span className="text-amber-500 dark:text-amber-400">Journey</span> of Shanti Yog Peeth
            </h2>
          </div>
          
          <div className="relative">
            {/* Timeline */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-amber-200 dark:bg-slate-700"></div>
            
            <div className="space-y-20">
              <div className="relative">
                <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 border-4 border-white dark:border-slate-900"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="md:text-right md:pr-12">
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">2010</h3>
                    <h4 className="text-xl text-amber-500 dark:text-amber-400 mb-4">The Beginning</h4>
                    <p className="text-slate-700 dark:text-slate-300">
                      Founded by Yogini Swati with a vision to create an authentic haven for yoga practitioners from around the world.
                    </p>
                  </div>
                  <div className="md:pl-12">
                    <img 
                      src="../images/misc/3.jpg" 
                      alt="Founding of Shanti Yog Peeth" 
                      className="rounded-lg shadow-md"
                    />
                  </div>
                </div>
              </div>
              
              <div className="relative">
                <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 border-4 border-white dark:border-slate-900"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="md:order-2 md:text-left md:pl-12">
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">2015</h3>
                    <h4 className="text-xl text-amber-500 dark:text-amber-400 mb-4">International Recognition</h4>
                    <p className="text-slate-700 dark:text-slate-300">
                      Shanti Yog Peeth received international accreditation and began welcoming students from over 50 countries, establishing itself as a premier yoga destination.
                    </p>
                  </div>
                  <div className="md:order-1 md:pr-12">
                    <img 
                      src="../images/TTC-Banner.webp" 
                      alt="International recognition" 
                      className="rounded-lg shadow-md"
                    />
                  </div>
                </div>
              </div>
              
              <div className="relative">
                <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 border-4 border-white dark:border-slate-900"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="md:text-right md:pr-12">
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">2020</h3>
                    <h4 className="text-xl text-amber-500 dark:text-amber-400 mb-4">Expansion & Innovation</h4>
                    <p className="text-slate-700 dark:text-slate-300">
                      Expanded our facilities and introduced innovative programs combining traditional yoga with modern wellness approaches, including specialized therapeutic yoga courses.
                    </p>
                  </div>
                  <div className="md:pl-12">
                    <img 
                      src="../images/misc/11.jpg" 
                      alt="Expansion of facilities" 
                      className="rounded-lg shadow-md"
                    />
                  </div>
                </div>
              </div>
              
              <div className="relative">
                <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 rounded-full bg-amber-400 dark:bg-indigo-600 border-4 border-white dark:border-slate-900"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="md:order-2 md:text-left md:pl-12">
                    <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-2">Today</h3>
                    <h4 className="text-xl text-amber-500 dark:text-amber-400 mb-4">Global Community</h4>
                    <p className="text-slate-700 dark:text-slate-300">
                      Today, Shanti Yog Peeth stands as a global community with over 5,000 graduates from our teacher training programs and countless lives transformed through our retreats and classes.
                    </p>
                  </div>
                  <div className="md:order-1 md:pr-12">
                    <img 
                      src="../images/hero.webp" 
                      alt="Global yoga community" 
                      className="rounded-lg shadow-md"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Gallery />
    </div>
  );
};

export default AboutPage;
