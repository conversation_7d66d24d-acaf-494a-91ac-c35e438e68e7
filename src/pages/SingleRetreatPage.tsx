import React, { useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Clock, MapPin, Users, ArrowLeft, Mail, Phone, Heart, Leaf, DollarSign } from 'lucide-react';
import ImageGallery from '../components/ImageGallery';
import { Retreat } from '../data/data';



const SingleRetreatPage: React.FC = () => {
  const { retreatId } = useParams<{ retreatId: string }>();
  const [retreats, setRetreats] = React.useState<Retreat[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  useEffect(() => {
    try {
      // Import data directly from data.ts
      import('../data/data').then(({ retreats }) => {
        setRetreats(retreats);
      });
    } catch (err: any) {
      setError(err.message);
    }
  }, []);

  // Support both id and slug for routing
  const retreat = React.useMemo(() => {
    if (!retreatId) return undefined;
    // Try to find by slug first, then by id (for backward compatibility)
    return (
      retreats.find(r => r.slug === retreatId) ||
      retreats.find(r => r.id === parseInt(retreatId, 10))
    );
  }, [retreatId, retreats]);

  // Format duration
  const durationDisplay = retreat ? `${retreat.duration} Days` : '';


  if (loading) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-slate-600 dark:text-slate-300">Loading...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="pt-18 min-h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <p className="text-red-600 dark:text-red-400">Error: {error}</p>
      </div>
    );
  }

  if (!retreat) {
    return (
      <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
        <div className="container mx-auto px-4 md:px-6 py-12 text-center">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Retreat Not Found</h1>
          <p className="text-slate-600 dark:text-slate-300 mb-8">The retreat you're looking for doesn't exist.</p>
          <Link
            to="/retreats"
            className="inline-flex items-center text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Retreats
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-18 min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Hero Section */}
      <div className="relative h-[60vh] min-h-[500px] overflow-hidden">
        <img
          src={retreat.image}
          alt={retreat.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/50 flex items-center">
          <div className="container mx-auto px-4 md:px-6">
            <Link
              to="/retreats"
              className="inline-flex items-center text-white hover:text-amber-400 mb-6 transition-colors"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Retreats
            </Link>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">{retreat.title}</h1>
            <div className="flex flex-wrap items-center gap-6 text-white">
              <span className="flex items-center">
                <Clock className="w-5 h-5 mr-2" />
                {durationDisplay}
              </span>
              <span className="flex items-center">
                <MapPin className="w-5 h-5 mr-2" />
                {retreat.location}
              </span>
              <span className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Max {retreat.capacity} participants
              </span>
              <span className="flex items-center">
                <Heart className="w-5 h-5 mr-2" />
                {retreat.theme}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Gallery Section */}
      {retreat.gallery && retreat.gallery.length > 0 && (
        <div className="bg-transparent -mt-20">
          <ImageGallery images={retreat.gallery} height="h-40" />
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-4 md:px-6 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-12">
            {/* Description */}
            <section>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Retreat Description</h2>
              <p className="text-slate-600 dark:text-slate-300 leading-relaxed">{retreat.description}</p>
            </section>

            {/* Highlights */}
            <section>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Retreat Highlights</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {retreat.highlights.map((highlight, index) => (
                  <div
                    key={index}
                    className="flex items-start space-x-3 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm"
                  >
                    <div className="flex-shrink-0 w-8 h-8 bg-amber-100 dark:bg-slate-700 rounded-full flex items-center justify-center text-amber-500 dark:text-amber-400">
                      <Leaf className="w-5 h-5" />
                    </div>
                    <span className="text-slate-600 dark:text-slate-300">{highlight}</span>
                  </div>
                ))}
              </div>
            </section>

            {/* What's Included */}
            <section>
              <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">What's Included</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {retreat.includes.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm"
                  >
                    <Heart className="w-5 h-5 text-amber-500 dark:text-amber-400" />
                    <span className="text-slate-600 dark:text-slate-300">{item}</span>
                  </div>
                ))}
              </div>
            </section>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-16 space-y-6">
              {/* Retreat Details Card */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">{retreat.title}</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center gap-2">
                      <span className="inline-flex items-center px-3 py-1.5 rounded-full bg-amber-100 dark:bg-amber-600/20 text-sm font-medium text-amber-700 dark:text-amber-300 space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{durationDisplay}</span>
                      </span>
                      <span className="inline-flex items-center px-3 py-1.5 rounded-full bg-indigo-100 dark:bg-indigo-600/20 text-sm font-medium text-indigo-700 dark:text-indigo-300 space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{retreat.location}</span>
                      </span>
                    </div>
                    <div className="flex justify-between items-center gap-2">
                      <span className="inline-flex items-center px-3 py-1.5 rounded-full bg-green-100 dark:bg-green-600/20 text-sm font-medium text-green-700 dark:text-green-300 space-x-1">
                        <Users className="w-4 h-4" />
                        <span>Max {retreat.capacity}</span>
                      </span>
                      <span className="inline-flex items-center px-3 py-1.5 rounded-full bg-pink-100 dark:bg-pink-600/20 text-sm font-medium text-pink-700 dark:text-pink-300 space-x-1">
                        <Heart className="w-4 h-4" />
                        <span>{retreat.theme}</span>
                      </span>
                    </div>
                    {/* Dates List */}
                    <div className="space-y-2 mt-4">
                      {retreat.dates.map((date, index) => {
                        const [day, month, year] = date.split('/').map(Number);
                        const startDate = new Date(year, month - 1, day);
                        const endDate = new Date(startDate);
                        endDate.setDate(startDate.getDate() + (retreat.duration ? retreat.duration - 1 : 0));

                        const formatDate = (d: Date) =>
                          d.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'long',
                            year: 'numeric'
                          });

                        return (
                          <div key={index} className="flex items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                            <Clock className="w-4 h-4 text-amber-500 dark:text-amber-400 mr-2" />
                            <span className="flex-1 text-sm text-slate-600 dark:text-slate-300 text-left">{formatDate(startDate)}</span>
                            <span className="flex-none text-sm text-slate-600 dark:text-slate-300 mx-2">-</span>
                            <span className="flex-1 text-sm text-slate-600 dark:text-slate-300 text-right">{formatDate(endDate)}</span>
                          </div>
                        );
                      })}
                    </div>
                    {/* Calendar Button */}
                    <Link
                      to="/calendar"
                      className="block w-full mt-2 py-2 px-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium text-sm text-center transition-colors duration-300"
                    >
                      Check Calendar for More Dates
                    </Link>
                  </div>
                </div>
              </div>

              {/* Packages Card */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Packages</h3>
                  <div className="space-y-1.5">
                    {retreat.price?.shared && (
                      <div className="flex justify-between items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                          <span className="text-sm text-slate-600 dark:text-slate-300">Shared Room</span>
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">$ {retreat.price.shared}</span>
                      </div>
                    )}
                    {retreat.price?.private && (
                      <div className="flex justify-between items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                          <span className="text-sm text-slate-600 dark:text-slate-300">Private Room</span>
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">$ {retreat.price.private}</span>
                      </div>
                    )}
                    {retreat.price?.sharedTwin && (
                      <div className="flex justify-between items-center p-2 bg-slate-50 dark:bg-slate-700/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-amber-500 dark:text-amber-400" />
                          <span className="text-sm text-slate-600 dark:text-slate-300">Shared Twin Room</span>
                        </div>
                        <span className="text-sm font-medium text-slate-700 dark:text-slate-200">$ {retreat.price.sharedTwin}</span>
                      </div>
                    )}
                  </div>
                  {/* Book Now Button */}
                  <Link
                    to={`/apply?retreat=${retreat.id}`}
                    className="block w-full mt-4 py-3 px-6 bg-amber-400 hover:bg-amber-500 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-slate-900 dark:text-white rounded-lg font-medium text-center transition-colors duration-300"
                  >
                    Book Now
                  </Link>
                </div>
              </div>

              {/* Contact Info */}
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg overflow-hidden">
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 dark:text-white mb-4">Have Questions?</h3>
                  <div className="space-y-4">
                    <a
                      href="mailto:<EMAIL>"
                      className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400"
                    >
                      <Mail className="w-5 h-5 mr-2" />
                      <EMAIL>
                    </a>
                    <a
                      href="tel:+919876543210"
                      className="flex items-center text-slate-600 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400"
                    >
                      <Phone className="w-5 h-5 mr-2" />
                      +91 98765 43210
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleRetreatPage;
