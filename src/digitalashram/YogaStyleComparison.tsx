import React, { useState, useRef, useEffect } from 'react';

interface YogaStyle {
  id: string;
  name: string;
  sanskrit?: string;
  origin: string;
  founded: string;
  intensity: 1 | 2 | 3 | 4 | 5; // 1 = gentle, 5 = intense
  focus: string[];
  benefits: string[];
  suitableFor: string[];
  notRecommendedFor: string[];
  description: string;
  image: string;
}

const YogaStyleComparison: React.FC = () => {
  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);
  const [showAllStyles, setShowAllStyles] = useState<boolean>(false);

  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Yoga styles data
  const yogaStyles: YogaStyle[] = [
    {
      id: 'hatha',
      name: 'Hatha Yoga',
      sanskrit: 'हठ योग',
      origin: 'India',
      founded: '15th century',
      intensity: 2,
      focus: ['Physical postures', 'Breath control', 'Balance', 'Flexibility'],
      benefits: [
        'Improved flexibility and balance',
        'Stress reduction',
        'Better posture',
        'Gentle strength building',
        'Mindfulness development'
      ],
      suitableFor: [
        'Beginners',
        'Those seeking a gentle practice',
        'People recovering from injury',
        'Seniors',
        'Those wanting to learn foundational poses'
      ],
      notRecommendedFor: [
        'Those seeking intense cardio workout',
        'People looking for rapid weight loss'
      ],
      description: 'Hatha yoga is a traditional form that emphasizes physical postures (asanas) and breath control (pranayama). It\'s a gentle, slower-paced practice that focuses on proper alignment and holding poses for several breaths.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Hatha+Yoga'
    },
    {
      id: 'vinyasa',
      name: 'Vinyasa Yoga',
      sanskrit: 'विन्यास योग',
      origin: 'India',
      founded: '20th century',
      intensity: 4,
      focus: ['Flow', 'Breath-movement coordination', 'Strength', 'Endurance'],
      benefits: [
        'Cardiovascular health',
        'Strength building',
        'Improved flexibility',
        'Enhanced mind-body connection',
        'Stress reduction'
      ],
      suitableFor: [
        'Those seeking a more dynamic practice',
        'People with some yoga experience',
        'Athletes',
        'Those wanting a workout with their yoga'
      ],
      notRecommendedFor: [
        'Complete beginners without guidance',
        'People with certain injuries',
        'Those who prefer a slow, gentle practice'
      ],
      description: 'Vinyasa yoga connects movement with breath in a flowing sequence of poses. No two vinyasa classes are exactly alike, but they typically involve moving from pose to pose with each inhale and exhale.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Vinyasa+Yoga'
    },
    {
      id: 'ashtanga',
      name: 'Ashtanga Yoga',
      sanskrit: 'अष्टांग योग',
      origin: 'India',
      founded: 'Ancient, modernized in the 20th century',
      intensity: 5,
      focus: ['Set sequence', 'Strength', 'Flexibility', 'Internal heat'],
      benefits: [
        'Builds significant strength and stamina',
        'Improves flexibility',
        'Detoxifies through sweat',
        'Develops discipline',
        'Calms the mind through consistent practice'
      ],
      suitableFor: [
        'Dedicated practitioners',
        'Athletes',
        'Those who enjoy routine',
        'People seeking physical challenge'
      ],
      notRecommendedFor: [
        'Beginners without proper guidance',
        'Those with certain injuries',
        'People who prefer variety in their practice',
        'Those with limited mobility'
      ],
      description: 'Ashtanga yoga follows a specific sequence of poses performed in the same order with a focus on breath, gaze points (drishti), and energy locks (bandhas). It\'s a rigorous, physically demanding practice.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Ashtanga+Yoga'
    },
    {
      id: 'iyengar',
      name: 'Iyengar Yoga',
      sanskrit: 'आयंगर योग',
      origin: 'India',
      founded: '20th century by B.K.S. Iyengar',
      intensity: 3,
      focus: ['Precise alignment', 'Props', 'Holding poses', 'Detailed instruction'],
      benefits: [
        'Improved alignment and posture',
        'Therapeutic benefits for various conditions',
        'Accessible to many body types',
        'Builds strength through precision',
        'Develops body awareness'
      ],
      suitableFor: [
        'Detail-oriented practitioners',
        'Those with injuries or limitations',
        'People who want to understand alignment',
        'Those who prefer a methodical approach'
      ],
      notRecommendedFor: [
        'Those seeking a fast-paced workout',
        'People who dislike using props',
        'Practitioners who prefer flowing movements'
      ],
      description: 'Iyengar yoga emphasizes precise alignment and the use of props (blocks, straps, bolsters) to help students achieve proper form. Poses are typically held longer while adjustments are made.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Iyengar+Yoga'
    },
    {
      id: 'kundalini',
      name: 'Kundalini Yoga',
      sanskrit: 'कुण्डलिनी योग',
      origin: 'India',
      founded: 'Ancient, popularized in the West in the 1960s',
      intensity: 3,
      focus: ['Energy awakening', 'Breathwork', 'Chanting', 'Meditation'],
      benefits: [
        'Spiritual growth',
        'Stress relief',
        'Increased energy',
        'Enhanced awareness',
        'Strengthened nervous system'
      ],
      suitableFor: [
        'Those interested in spiritual aspects of yoga',
        'People seeking transformation',
        'Practitioners looking for more than physical exercise',
        'Those interested in breathwork and meditation'
      ],
      notRecommendedFor: [
        'Those uncomfortable with chanting',
        'People seeking purely physical practice',
        'Those with certain psychiatric conditions'
      ],
      description: 'Kundalini yoga combines dynamic breathing techniques, movement, meditation, and mantras to awaken energy at the base of the spine and draw it upward through the chakras.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Kundalini+Yoga'
    },
    {
      id: 'restorative',
      name: 'Restorative Yoga',
      origin: 'Developed from Iyengar Yoga',
      founded: '20th century',
      intensity: 1,
      focus: ['Deep relaxation', 'Passive stretching', 'Stress relief', 'Healing'],
      benefits: [
        'Deep relaxation of body and mind',
        'Reduced stress and anxiety',
        'Improved sleep',
        'Enhanced recovery',
        'Balanced nervous system'
      ],
      suitableFor: [
        'Everyone, regardless of experience level',
        'Those recovering from illness or injury',
        'People with high stress levels',
        'Those with fatigue or burnout',
        'Practitioners seeking balance to more active styles'
      ],
      notRecommendedFor: [
        'Those exclusively seeking physical exertion',
        'People who find stillness challenging without preparation'
      ],
      description: 'Restorative yoga uses props to support the body in passive poses held for extended periods (5-10+ minutes). This allows for complete relaxation and opening of the body without effort.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Restorative+Yoga'
    },
    {
      id: 'yin',
      name: 'Yin Yoga',
      origin: 'Blend of Hatha Yoga and Taoist principles',
      founded: 'Developed in the 1970s',
      intensity: 1,
      focus: ['Deep connective tissue', 'Meridians', 'Passive holding', 'Meditation'],
      benefits: [
        'Increased flexibility in connective tissues',
        'Joint mobility',
        'Balanced energy flow',
        'Stress reduction',
        'Meditative awareness'
      ],
      suitableFor: [
        'All levels of practitioners',
        'Those with tight muscles and joints',
        'Athletes (as complementary practice)',
        'People seeking mindfulness in practice',
        'Those with stress and anxiety'
      ],
      notRecommendedFor: [
        'People with specific joint injuries without modifications',
        'Those seeking active movement',
        'Practitioners with hypermobility without guidance'
      ],
      description: 'Yin yoga targets the deep connective tissues and fascia through passive poses held for 3-5 minutes. It works on the meridian lines of the body and creates space in the joints and ligaments.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Yin+Yoga'
    },
    {
      id: 'bikram',
      name: 'Bikram Yoga',
      origin: 'India/United States',
      founded: 'Developed by Bikram Choudhury in the 1970s',
      intensity: 4,
      focus: ['Heat', 'Set sequence', 'Endurance', 'Detoxification'],
      benefits: [
        'Increased flexibility due to heat',
        'Detoxification through sweating',
        'Cardiovascular endurance',
        'Weight management',
        'Mental discipline'
      ],
      suitableFor: [
        'Those who enjoy heat and sweating',
        'People seeking physical challenge',
        'Practitioners who prefer routine',
        'Those wanting detoxification'
      ],
      notRecommendedFor: [
        'People with heat sensitivity or certain medical conditions',
        'Those with cardiovascular issues',
        'Pregnant women without prior experience',
        'People who dehydrate easily'
      ],
      description: 'Bikram yoga consists of a specific series of 26 poses and two breathing exercises performed in a room heated to 105°F (40.6°C) with 40% humidity. Each class is 90 minutes and follows the exact same sequence.',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Bikram+Yoga'
    }
  ];

  const toggleStyleSelection = (styleId: string) => {
    setSelectedStyles(prev => {
      if (prev.includes(styleId)) {
        return prev.filter(id => id !== styleId);
      } else {
        // Limit to 3 selections
        if (prev.length >= 3) {
          return [...prev.slice(1), styleId];
        }
        return [...prev, styleId];
      }
    });
  };

  const toggleShowAllStyles = () => {
    setShowAllStyles(prev => !prev);
  };

  const clearSelection = () => {
    setSelectedStyles([]);
  };

  const renderIntensityLevel = (level: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((value) => (
          <div
            key={value}
            className={`w-6 h-2 mx-0.5 rounded-full ${
              value <= level
                ? 'bg-amber-500 dark:bg-amber-400'
                : 'bg-slate-200 dark:bg-slate-600'
            }`}
          ></div>
        ))}
        <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
          {level === 1 ? 'Gentle' : level === 5 ? 'Intense' : ''}
        </span>
      </div>
    );
  };

  // Get styles to display in the grid
  const displayStyles = showAllStyles
    ? yogaStyles
    : yogaStyles.slice(0, 4);

  // Get selected styles for comparison
  const stylesToCompare = yogaStyles.filter(style => selectedStyles.includes(style.id));

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Yoga <span className="text-amber-500 dark:text-amber-400">Style Comparison</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore different yoga styles to find the practice that best suits your needs, goals, and preferences.
            </p>
          </div>

          {/* Style selection grid */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
              Explore Yoga Styles
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {displayStyles.map((style) => (
                <div
                  key={style.id}
                  className={`bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md transition-all duration-300 ${
                    selectedStyles.includes(style.id)
                      ? 'ring-2 ring-amber-500 dark:ring-amber-400'
                      : 'hover:shadow-lg'
                  }`}
                >
                  <div className="relative h-48">
                    <img
                      src={style.image}
                      alt={style.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-0 right-0 p-2">
                      <button
                        onClick={() => toggleStyleSelection(style.id)}
                        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                          selectedStyles.includes(style.id)
                            ? 'bg-amber-500 text-white'
                            : 'bg-white/80 text-slate-700 hover:bg-amber-500/80 hover:text-white'
                        }`}
                        aria-label={`Select ${style.name}`}
                      >
                        {selectedStyles.includes(style.id) ? (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                        ) : (
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="p-6">
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">
                      {style.name}
                      {style.sanskrit && (
                        <span className="text-sm font-normal text-amber-500 dark:text-amber-400 ml-2">
                          {style.sanskrit}
                        </span>
                      )}
                    </h4>

                    <div className="mb-4">
                      <div className="text-sm text-slate-600 dark:text-slate-400 mb-1">Intensity</div>
                      {renderIntensityLevel(style.intensity)}
                    </div>

                    <p className="text-slate-700 dark:text-slate-300 text-sm mb-4 line-clamp-3">
                      {style.description}
                    </p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {style.focus.slice(0, 3).map((item, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300 text-xs rounded-full"
                        >
                          {item}
                        </span>
                      ))}
                      {style.focus.length > 3 && (
                        <span className="inline-block px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 text-xs rounded-full">
                          +{style.focus.length - 3} more
                        </span>
                      )}
                    </div>

                    <button
                      onClick={() => toggleStyleSelection(style.id)}
                      className={`w-full py-2 rounded-lg text-sm font-medium transition-colors ${
                        selectedStyles.includes(style.id)
                          ? 'bg-amber-500 text-white hover:bg-amber-600'
                          : 'bg-slate-200 text-slate-800 hover:bg-slate-300 dark:bg-slate-600 dark:text-white dark:hover:bg-slate-500'
                      }`}
                    >
                      {selectedStyles.includes(style.id) ? 'Selected' : 'Compare'}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {yogaStyles.length > 4 && (
              <div className="text-center mb-8">
                <button
                  onClick={toggleShowAllStyles}
                  className="inline-flex items-center px-4 py-2 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-800 dark:text-slate-200 rounded-lg text-sm font-medium transition-all duration-300"
                >
                  {showAllStyles ? 'Show Fewer Styles' : 'Show All Styles'}
                  <svg className={`w-4 h-4 ml-2 transition-transform ${showAllStyles ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
              </div>
            )}
          </div>
          {/* Comparison section */}
          <div className="mb-12">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white">
                Compare Styles {selectedStyles.length > 0 && `(${selectedStyles.length}/3)`}
              </h3>
              {selectedStyles.length > 0 && (
                <button
                  onClick={clearSelection}
                  className="text-sm text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-white"
                >
                  Clear Selection
                </button>
              )}
            </div>

            {selectedStyles.length === 0 ? (
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-8 text-center">
                <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">No Styles Selected</h4>
                <p className="text-slate-700 dark:text-slate-300 max-w-md mx-auto">
                  Select yoga styles from the grid above to compare their features, benefits, and suitability.
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="text-left p-4 bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white rounded-tl-xl">Feature</th>
                      {stylesToCompare.map(style => (
                        <th key={style.id} className="text-left p-4 bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white last:rounded-tr-xl">
                          {style.name}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border-b border-slate-200 dark:border-slate-600 p-4 font-medium text-slate-700 dark:text-slate-300">Origin</td>
                      {stylesToCompare.map(style => (
                        <td key={style.id} className="border-b border-slate-200 dark:border-slate-600 p-4 text-slate-700 dark:text-slate-300">
                          {style.origin}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border-b border-slate-200 dark:border-slate-600 p-4 font-medium text-slate-700 dark:text-slate-300">Founded</td>
                      {stylesToCompare.map(style => (
                        <td key={style.id} className="border-b border-slate-200 dark:border-slate-600 p-4 text-slate-700 dark:text-slate-300">
                          {style.founded}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border-b border-slate-200 dark:border-slate-600 p-4 font-medium text-slate-700 dark:text-slate-300">Intensity</td>
                      {stylesToCompare.map(style => (
                        <td key={style.id} className="border-b border-slate-200 dark:border-slate-600 p-4 text-slate-700 dark:text-slate-300">
                          {renderIntensityLevel(style.intensity)}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border-b border-slate-200 dark:border-slate-600 p-4 font-medium text-slate-700 dark:text-slate-300">Focus</td>
                      {stylesToCompare.map(style => (
                        <td key={style.id} className="border-b border-slate-200 dark:border-slate-600 p-4 text-slate-700 dark:text-slate-300">
                          <ul className="list-disc list-inside">
                            {style.focus.map((item, index) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border-b border-slate-200 dark:border-slate-600 p-4 font-medium text-slate-700 dark:text-slate-300">Benefits</td>
                      {stylesToCompare.map(style => (
                        <td key={style.id} className="border-b border-slate-200 dark:border-slate-600 p-4 text-slate-700 dark:text-slate-300">
                          <ul className="list-disc list-inside">
                            {style.benefits.map((item, index) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border-b border-slate-200 dark:border-slate-600 p-4 font-medium text-slate-700 dark:text-slate-300">Suitable For</td>
                      {stylesToCompare.map(style => (
                        <td key={style.id} className="border-b border-slate-200 dark:border-slate-600 p-4 text-slate-700 dark:text-slate-300">
                          <ul className="list-disc list-inside">
                            {style.suitableFor.map((item, index) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="p-4 font-medium text-slate-700 dark:text-slate-300 rounded-bl-xl">Not Recommended For</td>
                      {stylesToCompare.map((style, index) => (
                        <td key={style.id} className={`p-4 text-slate-700 dark:text-slate-300 ${index === stylesToCompare.length - 1 ? 'rounded-br-xl' : ''}`}>
                          <ul className="list-disc list-inside">
                            {style.notRecommendedFor.map((item, index) => (
                              <li key={index}>{item}</li>
                            ))}
                          </ul>
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Find your style quiz CTA */}
          <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8">
            <div className="max-w-3xl mx-auto text-center">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Not Sure Which Style Is Right For You?
              </h3>
              <p className="text-slate-700 dark:text-slate-300 mb-6">
                Take our comprehensive yoga style quiz to discover which practice aligns best with your goals, preferences, and physical condition.
              </p>
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300">
                Take the Yoga Style Quiz
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YogaStyleComparison;
