import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { ArrowLeft, Clock, Wind, Leaf, Flame, Move } from 'lucide-react';
import activitiesData from '../data/activitiesData';
import MeditationTimer from './MeditationTimer';
import BreathingExercise from './BreathingExercise';
import AyurvedicProfile from './AyurvedicProfile';
import YogaStyleComparison from './YogaStyleComparison';
import YogaPoseLibrary from './YogaPoseLibrary';

const SingleActivityPage: React.FC = () => {
  const { activitySlug } = useParams<{ activitySlug: string }>();
  const activity = activitiesData.find(a => a.slug === activitySlug);

  // Function to render the appropriate activity component
  const renderActivityComponent = (slug: string) => {
    switch (slug) {
      case 'meditation-timer':
        return <MeditationTimer />;
      case 'breathing-exercise':
        return <BreathingExercise />;
      case 'ayurvedic-profile':
        return <AyurvedicProfile />;
      case 'yoga-style-comparison':
        return <YogaStyleComparison />;
      case 'yoga-pose-library':
        return <YogaPoseLibrary />;
      default:
        return <div>Activity not found</div>;
    }
  };

  if (!activity) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-indigo-50 to-amber-50 dark:from-slate-900 dark:to-slate-800 pt-0">
        <div className="container mx-auto px-4 md:px-6 pt-20 md:pt-16 pb-12 text-center">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">Activity Not Found</h1>
          <p className="text-slate-600 dark:text-slate-300 mb-8">The activity you're looking for doesn't exist.</p>
          <Link
            to="/digitalashram"
            className="inline-flex items-center text-amber-500 dark:text-amber-400 hover:text-amber-600 dark:hover:text-amber-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Digital Ashram
          </Link>
        </div>
      </div>
    );
  }

  // Get next and previous activities for navigation
  const currentIndex = activitiesData.findIndex(a => a.slug === activitySlug);
  const prevActivity = currentIndex > 0 ? activitiesData[currentIndex - 1] : null;
  const nextActivity = currentIndex < activitiesData.length - 1 ? activitiesData[currentIndex + 1] : null;

  return (
    <div className={`min-h-screen bg-gradient-to-b ${activity.gradientFrom} ${activity.gradientTo} dark:from-slate-900 dark:to-slate-800 pt-0`}>
      <Helmet>
        <title>{activity.title} | Digital Ashram | Shanti Yog Peeth</title>
        <meta name="description" content={activity.description} />
      </Helmet>

      <div className="container mx-auto px-4 md:px-6 pt-20 md:pt-20 pb-6">
        <div className="flex justify-between items-center mb-8">
          <Link to="/digitalashram" className="inline-flex items-center text-slate-700 dark:text-slate-300 hover:text-amber-500 dark:hover:text-amber-400 transition-colors">
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Digital Ashram
          </Link>

          {/* Activity Navigation */}
          <div className="flex items-center space-x-4">
            {prevActivity && (
              <Link
                to={`/digitalashram/${prevActivity.slug}`}
                className="px-4 py-2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-white dark:hover:bg-slate-700 transition-colors shadow-sm"
              >
                ← {prevActivity.shortTitle}
              </Link>
            )}

            {nextActivity && (
              <Link
                to={`/digitalashram/${nextActivity.slug}`}
                className="px-4 py-2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg text-sm font-medium text-slate-700 dark:text-slate-300 hover:bg-white dark:hover:bg-slate-700 transition-colors shadow-sm"
              >
                {nextActivity.shortTitle} →
              </Link>
            )}
          </div>
        </div>

        <div className="mx-auto">
          {/* Render the activity component */}
          {renderActivityComponent(activity.slug)}

          {/* About Section */}
          <div className="mt-12 bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-6 md:p-8">
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
              {activity.aboutTitle}
            </h2>
            <div className="prose dark:prose-invert max-w-none">
              {activity.aboutContent.map((paragraph, index) => (
                <p key={index} className={index < activity.aboutContent.length - 1 ? 'mb-4' : ''}>
                  {paragraph}
                </p>
              ))}

              {activity.instructions && (
                <>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white mt-6 mb-3">How to Use This Tool</h3>
                  <p>{activity.instructions}</p>
                </>
              )}

              {activity.benefits && activity.benefits.length > 0 && (
                <>
                  <h3 className="text-xl font-semibold text-slate-900 dark:text-white mt-6 mb-3">Benefits</h3>
                  <ul className="space-y-2">
                    {activity.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-amber-500 dark:text-amber-400 mr-2">•</span>
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </>
              )}
            </div>
          </div>

          {/* Other Activities Section */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-center text-slate-900 dark:text-white mb-6">
              Explore Other Activities
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {activitiesData
                .filter(a => a.slug !== activity.slug)
                .map(otherActivity => (
                  <Link
                    key={otherActivity.id}
                    to={`/digitalashram/${otherActivity.slug}`}
                    className="flex items-center p-4 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-slate-200 dark:border-slate-700"
                  >
                    <div className={`w-12 h-12 rounded-full ${otherActivity.iconBgColor} flex items-center justify-center mr-4`}>
                      {otherActivity.icon === 'Clock' ? <Clock className="w-6 h-6 text-white" /> :
                       otherActivity.icon === 'Wind' ? <Wind className="w-6 h-6 text-white" /> :
                       otherActivity.icon === 'Leaf' ? <Leaf className="w-6 h-6 text-white" /> :
                       otherActivity.icon === 'Flame' ? <Flame className="w-6 h-6 text-white" /> :
                       otherActivity.icon === 'Move' ? <Move className="w-6 h-6 text-white" /> :
                       <Leaf className="w-6 h-6 text-white" />}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-900 dark:text-white mb-1">{otherActivity.title}</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">{otherActivity.shortDescription}</p>
                    </div>
                  </Link>
                ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SingleActivityPage;
