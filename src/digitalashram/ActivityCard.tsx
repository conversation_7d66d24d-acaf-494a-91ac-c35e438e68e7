import React from 'react';
import { Link } from 'react-router-dom';

interface ActivityCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
  iconBgColor: string;
}

const ActivityCard: React.FC<ActivityCardProps> = ({
  title,
  description,
  icon,
  path,
  bgColor,
  textColor,
  borderColor,
  iconBgColor,
}) => {
  return (
    <div className={`${bgColor} rounded-2xl shadow-xl overflow-hidden transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] border ${borderColor}`}>
      <div className="p-6 md:p-8">
        <div className="flex items-center mb-6">
          <div className={`w-14 h-14 rounded-full ${iconBgColor} flex items-center justify-center mr-4`}>
            {icon}
          </div>
          <h3 className={`text-2xl font-bold ${textColor}`}>{title}</h3>
        </div>
        
        <p className={`${textColor} opacity-90 mb-6`}>
          {description}
        </p>
        
        <Link 
          to={path} 
          className={`inline-flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-300 ${textColor} bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/30`}
        >
          Experience Now
          <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default ActivityCard;
