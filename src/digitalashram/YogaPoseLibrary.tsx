import React, { useState, useRef, useEffect } from 'react';

interface YogaPose {
  id: string;
  name: string;
  sanskritName: string;
  image: string;
  description: string;
  benefits: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  categories: string[];
  steps: string[];
  modifications: string[];
  contraindications?: string[];
}

const YogaPoseLibrary: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [activeDifficulty, setActiveDifficulty] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedPose, setSelectedPose] = useState<YogaPose | null>(null);
  const [showModal, setShowModal] = useState<boolean>(false);

  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setShowModal(false);
      }
    };

    if (showModal) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showModal]);

  // Sample yoga poses data
  const yogaPoses: YogaPose[] = [
    {
      id: 'downward-dog',
      name: 'Downward-Facing Dog',
      sanskritName: 'Adho Mukha Svanasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Downward+Dog',
      description: 'A common pose in many yoga sequences that stretches and strengthens the entire body.',
      benefits: [
        'Strengthens the arms, shoulders, and legs',
        'Stretches the hamstrings, calves, and spine',
        'Improves circulation',
        'Calms the nervous system',
        'Energizes the body'
      ],
      difficulty: 'beginner',
      categories: ['standing', 'inversion'],
      steps: [
        'Start on your hands and knees with your wrists under your shoulders and knees under your hips.',
        'Spread your fingers wide and press firmly through your palms and knuckles.',
        'Tuck your toes and lift your knees off the floor.',
        'Straighten your legs as much as possible and lift your hips high.',
        'Press your heels toward the floor (they don\'t need to touch).',
        'Keep your head between your arms, not hanging down.',
        'Hold for 5-10 breaths.'
      ],
      modifications: [
        'Bend your knees if your hamstrings are tight.',
        'Use blocks under your hands if your wrists are sensitive.',
        'Widen your stance for more stability.'
      ]
    },
    {
      id: 'warrior-2',
      name: 'Warrior II',
      sanskritName: 'Virabhadrasana II',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Warrior+II',
      description: 'A powerful standing pose that builds strength and stamina while improving focus and concentration.',
      benefits: [
        'Strengthens the legs, ankles, and feet',
        'Opens the hips and chest',
        'Improves concentration and balance',
        'Builds stamina and endurance',
        'Stimulates abdominal organs'
      ],
      difficulty: 'beginner',
      categories: ['standing'],
      steps: [
        'Stand with your feet wide apart, about 4-5 feet.',
        'Turn your right foot out 90 degrees and your left foot in slightly.',
        'Align your right heel with the arch of your left foot.',
        'Extend your arms parallel to the floor, reaching actively through the fingertips.',
        'Bend your right knee until it is directly over the ankle.',
        'Turn your head to gaze over your right fingertips.',
        'Hold for 5-10 breaths, then repeat on the other side.'
      ],
      modifications: [
        'Reduce the stance width if you feel unstable.',
        'Don\'t bend the knee as deeply if you have knee issues.',
        'Rest your hands on your hips if your shoulders are tight.'
      ]
    },
    {
      id: 'tree-pose',
      name: 'Tree Pose',
      sanskritName: 'Vrksasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Tree+Pose',
      description: 'A balancing pose that improves focus, concentration, and stability while strengthening the legs and core.',
      benefits: [
        'Improves balance and stability',
        'Strengthens the legs, ankles, and feet',
        'Stretches the inner thighs and groin',
        'Improves focus and concentration',
        'Builds confidence'
      ],
      difficulty: 'beginner',
      categories: ['standing', 'balance'],
      steps: [
        'Start standing with your feet together and weight evenly distributed.',
        'Shift your weight to your left foot and lift your right foot off the floor.',
        'Place the sole of your right foot on your inner left thigh, calf, or ankle (avoid the knee).',
        'Bring your palms together at your heart center or extend your arms overhead.',
        'Fix your gaze on a point in front of you to help with balance.',
        'Hold for 5-10 breaths, then repeat on the other side.'
      ],
      modifications: [
        'Place your foot on your ankle or use a wall for support if balance is challenging.',
        'Keep your hands on your hips if raising them is difficult.',
        'Use a chair or wall for support if needed.'
      ]
    },
    {
      id: 'seated-forward-bend',
      name: 'Seated Forward Bend',
      sanskritName: 'Paschimottanasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Seated+Forward+Bend',
      description: 'A classic seated forward fold that stretches the entire back of the body and calms the nervous system.',
      benefits: [
        'Stretches the spine, hamstrings, and calves',
        'Calms the mind and reduces stress',
        'Stimulates the liver, kidneys, and digestive organs',
        'Improves digestion',
        'Relieves headache and fatigue'
      ],
      difficulty: 'beginner',
      categories: ['seated', 'forward-bends'],
      steps: [
        'Sit on the floor with your legs extended in front of you.',
        'Flex your feet and engage your leg muscles.',
        'Inhale and lengthen your spine, sitting up tall.',
        'Exhale and hinge at your hips to fold forward.',
        'Reach for your feet, ankles, or shins, wherever is comfortable.',
        'Relax your head and neck, focusing on lengthening your spine.',
        'Hold for 30 seconds to 3 minutes, breathing deeply.'
      ],
      modifications: [
        'Bend your knees if your hamstrings are tight.',
        'Use a strap around your feet if you can\'t reach them.',
        'Sit on a folded blanket to tilt your pelvis forward.'
      ],
      contraindications: [
        'Avoid with lower back injuries',
        'Practice with caution if you have sciatica'
      ]
    },
    {
      id: 'childs-pose',
      name: 'Child\'s Pose',
      sanskritName: 'Balasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Childs+Pose',
      description: 'A restful pose that gently stretches the back, hips, and thighs while calming the mind and relieving stress.',
      benefits: [
        'Gently stretches the lower back, hips, and thighs',
        'Calms the nervous system',
        'Relieves stress and fatigue',
        'Promotes relaxation',
        'Can help relieve back and neck pain'
      ],
      difficulty: 'beginner',
      categories: ['resting', 'forward-bends'],
      steps: [
        'Kneel on the floor with your big toes touching and knees about hip-width apart.',
        'Sit back on your heels and fold forward, extending your arms in front of you.',
        'Rest your forehead on the mat and relax your shoulders toward the floor.',
        'Breathe deeply, feeling the expansion of your back with each inhale.',
        'Hold for 1-5 minutes or as long as comfortable.'
      ],
      modifications: [
        'Place a cushion or folded blanket between your thighs and calves if there\'s discomfort.',
        'Rest your arms alongside your body if shoulder tension exists.',
        'Use a bolster under your chest and head for support.'
      ],
      contraindications: [
        'Modify if you have knee injuries',
        'Use caution during pregnancy (especially third trimester)'
      ]
    },
    {
      id: 'cobra-pose',
      name: 'Cobra Pose',
      sanskritName: 'Bhujangasana',
      image: 'https://placehold.co/600x400/e2e8f0/1e293b?text=Cobra+Pose',
      description: 'A gentle backbend that strengthens the spine and opens the chest and shoulders.',
      benefits: [
        'Strengthens the spine and back muscles',
        'Opens the chest and shoulders',
        'Stimulates abdominal organs',
        'Improves posture',
        'Elevates mood'
      ],
      difficulty: 'beginner',
      categories: ['prone', 'backbends'],
      steps: [
        'Lie on your stomach with your forehead touching the floor.',
        'Place your hands under your shoulders, elbows close to your body.',
        'Press your palms firmly into the floor.',
        'Inhale and lift your chest off the floor, keeping your lower ribs on the mat.',
        'Keep a slight bend in your elbows and your shoulders away from your ears.',
        'Gaze forward or slightly upward without straining your neck.',
        'Hold for 15-30 seconds, breathing deeply.'
      ],
      modifications: [
        'Keep your elbows bent for a gentler stretch.',
        'Lower your height if you feel compression in your lower back.',
        'Use forearms on the floor instead of hands if wrists are sensitive.'
      ],
      contraindications: [
        'Avoid with recent or chronic back injury',
        'Modify during pregnancy',
        'Use caution with carpal tunnel syndrome'
      ]
    }
  ];

  // Available categories for filtering
  const categories = [
    { id: 'all', name: 'All Poses' },
    { id: 'standing', name: 'Standing' },
    { id: 'seated', name: 'Seated' },
    { id: 'prone', name: 'Prone' },
    { id: 'supine', name: 'Supine' },
    { id: 'balance', name: 'Balance' },
    { id: 'forward-bends', name: 'Forward Bends' },
    { id: 'backbends', name: 'Backbends' },
    { id: 'twists', name: 'Twists' },
    { id: 'inversion', name: 'Inversions' },
    { id: 'resting', name: 'Resting' }
  ];

  // Difficulty levels for filtering
  const difficultyLevels = [
    { id: 'all', name: 'All Levels' },
    { id: 'beginner', name: 'Beginner' },
    { id: 'intermediate', name: 'Intermediate' },
    { id: 'advanced', name: 'Advanced' }
  ];

  // Filter poses based on category, difficulty, and search query
  const filteredPoses = yogaPoses.filter(pose => {
    const matchesCategory = activeCategory === 'all' || pose.categories.includes(activeCategory);
    const matchesDifficulty = activeDifficulty === 'all' || pose.difficulty === activeDifficulty;
    const matchesSearch = pose.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          pose.sanskritName.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesCategory && matchesDifficulty && matchesSearch;
  });

  const handlePoseClick = (pose: YogaPose) => {
    setSelectedPose(pose);
    setShowModal(true);
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Yoga <span className="text-amber-500 dark:text-amber-400">Pose Library</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 max-w-2xl mx-auto">
              Explore our collection of yoga poses with detailed instructions, benefits, and modifications for all levels.
            </p>
          </div>

          {/* Search and filters */}
          <div className="mb-10">
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search poses..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-3 bg-slate-50 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-500 dark:focus:ring-amber-400"
                  />
                  <svg className="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
              </div>

              <div className="flex gap-4">
                <div className="w-full md:w-48">
                  <select
                    value={activeCategory}
                    onChange={(e) => setActiveCategory(e.target.value)}
                    className="w-full px-4 py-3 bg-slate-50 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-500 dark:focus:ring-amber-400"
                  >
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <div className="w-full md:w-48">
                  <select
                    value={activeDifficulty}
                    onChange={(e) => setActiveDifficulty(e.target.value)}
                    className="w-full px-4 py-3 bg-slate-50 dark:bg-slate-700 border border-slate-200 dark:border-slate-600 rounded-lg text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-amber-500 dark:focus:ring-amber-400"
                  >
                    {difficultyLevels.map(level => (
                      <option key={level.id} value={level.id}>{level.name}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Poses grid */}
          {filteredPoses.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPoses.map(pose => (
                <div
                  key={pose.id}
                  onClick={() => handlePoseClick(pose)}
                  className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer transform hover:scale-[1.02]"
                >
                  <div className="relative h-48">
                    <img
                      src={pose.image}
                      alt={pose.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-2 right-2">
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                        pose.difficulty === 'beginner'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : pose.difficulty === 'intermediate'
                          ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      }`}>
                        {pose.difficulty.charAt(0).toUpperCase() + pose.difficulty.slice(1)}
                      </span>
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-1">
                      {pose.name}
                    </h3>
                    <p className="text-amber-500 dark:text-amber-400 text-sm mb-3">
                      {pose.sanskritName}
                    </p>

                    <p className="text-slate-700 dark:text-slate-300 text-sm mb-4 line-clamp-2">
                      {pose.description}
                    </p>

                    <div className="flex flex-wrap gap-2">
                      {pose.categories.map((category, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 text-xs rounded-full"
                        >
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-8 text-center">
              <svg className="w-16 h-16 text-slate-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-2">No Poses Found</h4>
              <p className="text-slate-700 dark:text-slate-300 max-w-md mx-auto">
                Try adjusting your search criteria or filters to find yoga poses.
              </p>
            </div>
          )}

          {/* Pose detail modal */}
          {showModal && selectedPose && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
              <div
                ref={modalRef}
                className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              >
                <div className="flex justify-between items-center p-6 border-b border-slate-200 dark:border-slate-700">
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                    {selectedPose.name}
                  </h3>
                  <button
                    onClick={() => setShowModal(false)}
                    className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-white"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>

                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <img
                        src={selectedPose.image}
                        alt={selectedPose.name}
                        className="w-full h-auto rounded-lg"
                      />
                    </div>
                    <div>
                      <p className="text-amber-500 dark:text-amber-400 text-lg mb-4">{selectedPose.sanskritName}</p>
                      <p className="text-slate-700 dark:text-slate-300 mb-4">{selectedPose.description}</p>

                      <div className="mb-4">
                        <h4 className="font-semibold text-slate-900 dark:text-white mb-2">Difficulty</h4>
                        <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          selectedPose.difficulty === 'beginner'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : selectedPose.difficulty === 'intermediate'
                            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {selectedPose.difficulty.charAt(0).toUpperCase() + selectedPose.difficulty.slice(1)}
                        </span>
                      </div>

                      <div className="mb-4">
                        <h4 className="font-semibold text-slate-900 dark:text-white mb-2">Categories</h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedPose.categories.map((category, index) => (
                            <span
                              key={index}
                              className="inline-block px-2 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 text-sm rounded-full"
                            >
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Benefits</h4>
                      <ul className="list-disc list-inside text-slate-700 dark:text-slate-300 space-y-2">
                        {selectedPose.benefits.map((benefit, index) => (
                          <li key={index}>{benefit}</li>
                        ))}
                      </ul>
                    </div>

                    {selectedPose.contraindications && (
                      <div>
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Cautions</h4>
                        <ul className="list-disc list-inside text-slate-700 dark:text-slate-300 space-y-2">
                          {selectedPose.contraindications.map((contraindication, index) => (
                            <li key={index}>{contraindication}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Step-by-Step Instructions</h4>
                    <ol className="list-decimal list-inside text-slate-700 dark:text-slate-300 space-y-2">
                      {selectedPose.steps.map((step, index) => (
                        <li key={index}>{step}</li>
                      ))}
                    </ol>
                  </div>

                  <div>
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3">Modifications</h4>
                    <ul className="list-disc list-inside text-slate-700 dark:text-slate-300 space-y-2">
                      {selectedPose.modifications.map((modification, index) => (
                        <li key={index}>{modification}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* CTA section */}
          <div className="mt-12 bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8">
            <div className="max-w-3xl mx-auto text-center">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Deepen Your Practice
              </h3>
              <p className="text-slate-700 dark:text-slate-300 mb-6">
                Join our online yoga classes to learn these poses with expert guidance and proper alignment.
              </p>
              <button className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300">
                Explore Online Classes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default YogaPoseLibrary;
