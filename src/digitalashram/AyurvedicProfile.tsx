import React, { useState, useRef, useEffect } from 'react';
import {
  Question,
  DoshaResult,
  SavedProfile,
  DoshaInfo,
  ayurvedicQuestions,
  doshaInfo as doshaInfoData
} from '../data/ayurvedicProfileData';

const AyurvedicProfile: React.FC = () => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [answers, setAnswers] = useState<string[]>([]);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [doshaResult, setDoshaResult] = useState<DoshaResult>({ vata: 0, pitta: 0, kapha: 0 });
  const [dominantDosha, setDominantDosha] = useState<string>('');
  const [secondaryDosha, setSecondaryDosha] = useState<string>('');

  const [userName, setUserName] = useState<string>('');
  const [savedProfiles, setSavedProfiles] = useState<SavedProfile[]>([]);
  const [showNameInput, setShowNameInput] = useState<boolean>(false);
  // Daily routine is always displayed

  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

// Use imported quiz questions and dosha information
const questions: Question[] = ayurvedicQuestions;

  // Use imported dosha information
  const doshaInfo: { [key: string]: DoshaInfo } = doshaInfoData;

  // Animation on scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (contentRef.current) contentRef.current.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    const currentSectionRef = sectionRef.current;

    if (currentSectionRef) {
      observer.observe(currentSectionRef);
    }

    return () => {
      if (currentSectionRef) {
        observer.unobserve(currentSectionRef);
      }
    };
  }, []);

  // Scroll to results when they're shown
  useEffect(() => {
    if (showResults && resultsRef.current) {
      resultsRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [showResults]);

  // Check localStorage on component mount to load saved profiles
  useEffect(() => {
    // Load all saved profiles
    const loadSavedProfiles = () => {
      const profiles: SavedProfile[] = [];

      // Check for legacy single profile
      const legacyProfile = localStorage.getItem('ayurvedicProfile');
      if (legacyProfile) {
        try {
          const parsedProfile = JSON.parse(legacyProfile);
          if (parsedProfile.doshaResult && parsedProfile.dominantDosha && parsedProfile.secondaryDosha) {
            // Convert legacy profile to new format
            profiles.push({
              name: 'My Profile', // Default name for legacy
              doshaResult: parsedProfile.doshaResult,
              dominantDosha: parsedProfile.dominantDosha,
              secondaryDosha: parsedProfile.secondaryDosha,
              timestamp: parsedProfile.timestamp || new Date().toISOString()
            });

            // Remove legacy profile
            localStorage.removeItem('ayurvedicProfile');

            // Save in new format (will be overwritten if new profiles exist, or saved if not)
            localStorage.setItem('ayurvedicProfiles', JSON.stringify(profiles));
          }
        } catch (error) {
          console.error('Error parsing legacy profile:', error);
          localStorage.removeItem('ayurvedicProfile');
        }
      }

      // Load profiles from new format
      const savedProfilesStr = localStorage.getItem('ayurvedicProfiles');
      if (savedProfilesStr) {
        try {
          const parsedProfiles = JSON.parse(savedProfilesStr) as SavedProfile[];
          if (Array.isArray(parsedProfiles)) {
             // Merge with any legacy profiles loaded, avoiding duplicates by timestamp
            parsedProfiles.forEach(profile => {
              if (!profiles.some(p => p.timestamp === profile.timestamp)) {
                profiles.push(profile);
              }
            });
          }
        } catch (error) {
          console.error('Error parsing saved profiles:', error);
        }
      }
      return profiles.sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()); // Sort by most recent
    };

    const profiles = loadSavedProfiles();
    setSavedProfiles(profiles);
  }, []);

  // Clear saved results from localStorage
  const clearSavedResults = () => {
    if (window.confirm('Are you sure you want to delete all saved profiles?')) {
      localStorage.removeItem('ayurvedicProfiles');
      setSavedProfiles([]);
    }
  };

  // Load a saved profile
  const loadProfile = (profile: SavedProfile) => {
    setDoshaResult(profile.doshaResult);
    setDominantDosha(profile.dominantDosha);
    setSecondaryDosha(profile.secondaryDosha);
    setUserName(profile.name);
    setShowResults(true);
    setShowNameInput(false); // Ensure quiz/name input is not shown
    setCurrentQuestionIndex(0); // Reset quiz progress
    setAnswers([]);
  };

  // Delete a specific profile
  const deleteProfile = (timestamp: string) => {
    const updatedProfiles = savedProfiles.filter(profile => profile.timestamp !== timestamp);
    setSavedProfiles(updatedProfiles);
    localStorage.setItem('ayurvedicProfiles', JSON.stringify(updatedProfiles));
  };




  const handleOptionSelect = (optionId: string) => {
    const newAnswers = [...answers];
    newAnswers[currentQuestionIndex] = optionId;
    setAnswers(newAnswers);

    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      calculateResults(newAnswers);
    }
  };

  const handlePrevQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const calculateResults = (finalAnswers: string[]) => {
    let vataScore = 0;
    let pittaScore = 0;
    let kaphaScore = 0;

    finalAnswers.forEach((answerId, index) => {
      const question = questions[index];
      const selectedOption = question.options.find(option => option.id === answerId);

      if (selectedOption) {
        vataScore += selectedOption.v;
        pittaScore += selectedOption.p;
        kaphaScore += selectedOption.k;
      }
    });

    const result = { vata: vataScore, pitta: pittaScore, kapha: kaphaScore };
    setDoshaResult(result);

    // Determine dominant and secondary doshas
    const scores = [
      { dosha: 'vata', score: vataScore },
      { dosha: 'pitta', score: pittaScore },
      { dosha: 'kapha', score: kaphaScore }
    ].sort((a, b) => b.score - a.score);

    const dominant = scores[0].dosha;
    const secondary = scores[1].dosha;

    setDominantDosha(dominant);
    setSecondaryDosha(secondary);

    // Show name input instead of immediately showing results
    setShowNameInput(true);
  };

  // Handle name submission, save profile, and show results
  const handleNameSubmit = () => {
    if (!userName.trim()) {
      alert('Please enter your name to continue.');
      return;
    }

    // Save profile automatically
    const newProfile: SavedProfile = {
      name: userName,
      doshaResult,
      dominantDosha,
      secondaryDosha,
      timestamp: new Date().toISOString()
    };

    // Get existing profiles
    const existingProfilesStr = localStorage.getItem('ayurvedicProfiles');
    let profiles: SavedProfile[] = [];

    if (existingProfilesStr) {
      try {
        const parsedProfiles = JSON.parse(existingProfilesStr);
        if (Array.isArray(parsedProfiles)) {
          profiles = parsedProfiles;
        }
      } catch (error) {
        console.error('Error parsing existing profiles:', error);
      }
    }

    // Check if profile with same name already exists
    const existingProfileIndex = profiles.findIndex(p => p.name === userName);
    if (existingProfileIndex !== -1) {
      // Replace existing profile
      profiles[existingProfileIndex] = newProfile;
    } else {
      // Add new profile
      profiles.push(newProfile);
    }
    // Sort profiles by timestamp (most recent first) before saving
    profiles.sort((a,b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());


    // Save updated profiles
    localStorage.setItem('ayurvedicProfiles', JSON.stringify(profiles));
    setSavedProfiles(profiles);

    setShowNameInput(false);
    setShowResults(true);
  };

  const resetQuiz = () => {
    setCurrentQuestionIndex(0);
    setAnswers([]);
    setShowResults(false);
    setShowNameInput(false);
    setUserName('');
    setDoshaResult({ vata: 0, pitta: 0, kapha: 0 });
    setDominantDosha('');
    setSecondaryDosha('');
     if (sectionRef.current) { // Scroll to top of section
      sectionRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const getProgressPercentage = () => {
    return ((currentQuestionIndex + 1) / questions.length) * 100;
  };

  const getDoshaPercentage = (dosha: keyof DoshaResult) => {
    const total = doshaResult.vata + doshaResult.pitta + doshaResult.kapha;
    return total > 0 ? Math.round((doshaResult[dosha] / total) * 100) : 0;
  };

  return (
    <div ref={sectionRef} className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl overflow-hidden">
      <div ref={contentRef} className="opacity-0">
        <div className="p-6 md:p-10">
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Discover Your <span className="text-amber-500 dark:text-amber-400">Ayurvedic Constitution (Prakriti)</span>
            </h2>
            <p className="text-slate-700 dark:text-slate-300 mx-auto">
              Embark on a journey of self-discovery with this Ayurvedic quiz. Understand your unique mind-body type (Prakriti) – Vata, Pitta, or Kapha – and learn how to foster balance and well-being according to the ancient wisdom of Ayurveda, the science of life.
            </p>
          </div>

          {/* Saved Profiles Section */}
          {savedProfiles.length > 0 && !showResults && !showNameInput && currentQuestionIndex === 0 && (
            <div className="mx-auto mb-10">
              <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                Saved Profiles
              </h3>
              <div className="flex flex-wrap gap-3 mb-6">
                {savedProfiles.map((profile) => (
                  <button
                    key={profile.timestamp}
                    onClick={() => loadProfile(profile)}
                    className="flex items-center px-4 py-2 bg-amber-100 hover:bg-amber-200 dark:bg-amber-900/30 dark:hover:bg-amber-900/50 text-amber-800 dark:text-amber-300 rounded-lg transition-all duration-300"
                  >
                    <span className="mr-2">{profile.name}</span>
                    <span className="text-xs opacity-70">
                      ({profile.dominantDosha}-{profile.secondaryDosha})
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (window.confirm(`Delete profile for ${profile.name}?`)) {
                          deleteProfile(profile.timestamp);
                        }
                      }}
                      className="ml-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                      aria-label="Delete profile"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
                      </svg>
                    </button>
                  </button>
                ))}
              </div>
              {savedProfiles.length > 0 && (
                <button
                  onClick={clearSavedResults}
                  className="text-sm text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd"></path>
                  </svg>
                  Clear All Saved Profiles
                </button>
              )}
            </div>
          )}

          {showNameInput ? (
            <div className="mx-auto">
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 md:p-8 mb-6">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                  What's your name?
                </h3>
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Please enter your name to personalize and save your Ayurvedic profile.
                </p>
                <div className="mb-6">
                  <input
                    type="text"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    placeholder="Your name"
                    className="w-full p-3 border border-slate-300 dark:border-slate-600 rounded-lg bg-white dark:bg-slate-800 text-slate-900 dark:text-white focus:ring-2 focus:ring-amber-500 dark:focus:ring-amber-400 focus:border-transparent"
                  />
                </div>
                <div className="flex justify-between">
                  <button
                    onClick={() => {
                      setShowNameInput(false);
                      // setCurrentQuestionIndex(questions.length - 1); // No need to go back to last question, just show quiz
                    }}
                    className="px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300"
                  >
                    Back to Quiz
                  </button>
                  <button
                    onClick={handleNameSubmit}
                    className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300"
                  >
                    View My Profile
                  </button>
                </div>
              </div>
            </div>
          ) : !showResults ? (
            <div className="mx-auto">
              {/* Progress bar */}
              <div className="mb-8">
                <div className="flex justify-between text-sm text-slate-600 dark:text-slate-400 mb-2">
                  <span>Question {currentQuestionIndex + 1} of {questions.length}</span>
                  <span>{getProgressPercentage().toFixed(0)}% Complete</span>
                </div>
                <div className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-amber-500 dark:bg-amber-400 transition-all duration-300"
                    style={{ width: `${getProgressPercentage()}%` }}
                  ></div>
                </div>
              </div>

              {/* Current question */}
              <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 md:p-8 mb-6 transition-all duration-300">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                  {questions[currentQuestionIndex].text}
                </h3>
                <div className="space-y-4">
                  {questions[currentQuestionIndex].options.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => handleOptionSelect(option.id)}
                      className={`w-full text-left p-4 rounded-lg border transition-all duration-300 ${
                        answers[currentQuestionIndex] === option.id
                          ? 'border-amber-500 bg-amber-50 dark:bg-amber-900/20 dark:border-amber-400 ring-2 ring-amber-500 dark:ring-amber-400'
                          : 'border-slate-300 dark:border-slate-600 hover:border-amber-300 dark:hover:border-amber-500 bg-white dark:bg-slate-800'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`w-5 h-5 rounded-full border-2 flex-shrink-0 mr-3 flex items-center justify-center ${
                          answers[currentQuestionIndex] === option.id
                            ? 'border-amber-500 dark:border-amber-400'
                            : 'border-slate-400 dark:border-slate-500'
                        }`}>
                          {answers[currentQuestionIndex] === option.id && (
                            <div className="w-2.5 h-2.5 rounded-full bg-amber-500 dark:bg-amber-400"></div>
                          )}
                        </div>
                        <span className="text-slate-800 dark:text-slate-200">{option.text}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Navigation buttons */}
              <div className="flex justify-between">
                <button
                  onClick={handlePrevQuestion}
                  disabled={currentQuestionIndex === 0}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                    currentQuestionIndex === 0
                      ? 'bg-slate-200 dark:bg-slate-700 text-slate-400 dark:text-slate-500 cursor-not-allowed'
                      : 'bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300'
                  }`}
                >
                  Previous
                </button>
                {currentQuestionIndex === questions.length - 1 && answers[currentQuestionIndex] && (
                  <button
                    onClick={() => calculateResults(answers)}
                    className="px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300"
                  >
                    See My Results
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div ref={resultsRef} className="mx-auto">
              <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-6 md:p-8 mb-10 text-center">
                <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
                  {userName ? `${userName}'s` : 'Your'} Ayurvedic Constitution
                </h3>
                {dominantDosha && secondaryDosha && doshaInfo[dominantDosha] && doshaInfo[secondaryDosha] && (
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Based on your responses, your dominant dosha appears to be <span className="font-semibold text-amber-600 dark:text-amber-400">{doshaInfo[dominantDosha].name} ({doshaInfo[dominantDosha].sanskrit})</span>, with <span className="font-semibold text-amber-600 dark:text-amber-400">{doshaInfo[secondaryDosha].name} ({doshaInfo[secondaryDosha].sanskrit})</span> as your secondary influence. Understanding these energies can help you tailor your lifestyle for optimal health and harmony.
                </p>
                )}

                {/* Dosha distribution chart */}
                <div className="mb-8">
                  <div className="flex h-6 rounded-full overflow-hidden mb-2">
                    <div
                      className="bg-blue-400 dark:bg-blue-500 transition-all duration-500"
                      style={{ width: `${getDoshaPercentage('vata')}%` }}
                      title={`Vata: ${getDoshaPercentage('vata')}%`}
                    ></div>
                    <div
                      className="bg-red-400 dark:bg-red-500 transition-all duration-500"
                      style={{ width: `${getDoshaPercentage('pitta')}%` }}
                      title={`Pitta: ${getDoshaPercentage('pitta')}%`}
                    ></div>
                    <div
                      className="bg-green-400 dark:bg-green-500 transition-all duration-500"
                      style={{ width: `${getDoshaPercentage('kapha')}%` }}
                      title={`Kapha: ${getDoshaPercentage('kapha')}%`}
                    ></div>
                  </div>
                  <div className="flex justify-between text-sm">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-blue-400 dark:bg-blue-500 rounded-full mr-1"></div>
                      <span className="text-slate-700 dark:text-slate-300">Vata: {getDoshaPercentage('vata')}%</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-red-400 dark:bg-red-500 rounded-full mr-1"></div>
                      <span className="text-slate-700 dark:text-slate-300">Pitta: {getDoshaPercentage('pitta')}%</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-green-400 dark:bg-green-500 rounded-full mr-1"></div>
                      <span className="text-slate-700 dark:text-slate-300">Kapha: {getDoshaPercentage('kapha')}%</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={resetQuiz}
                  className="flex items-center px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Retake Quiz
                </button>
              </div>

              {/* Dominant dosha details */}
              {dominantDosha && doshaInfo[dominantDosha] && (
              <div className="mb-12">
                <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6 border-b border-slate-200 dark:border-slate-700 pb-3">
                  Understanding Your Dominant Dosha: <span className="text-amber-500 dark:text-amber-400">{doshaInfo[dominantDosha].name} ({doshaInfo[dominantDosha].sanskrit})</span>
                </h3>
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Your dominant dosha, {doshaInfo[dominantDosha].name}, significantly shapes your physical characteristics, mental tendencies, and overall health patterns. Learning about its qualities can empower you to make choices that support its balance.
                </p>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div className="lg:col-span-1">
                    <div className="bg-slate-50 dark:bg-slate-700 rounded-xl overflow-hidden shadow-md">
                      <img
                        src={doshaInfo[dominantDosha].image}
                        alt={`${doshaInfo[dominantDosha].name} Dosha`}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h4 className="text-xl font-semibold text-slate-900 dark:text-white mb-2">
                          {doshaInfo[dominantDosha].name} <span className="text-slate-500 dark:text-slate-400">({doshaInfo[dominantDosha].sanskrit})</span>
                        </h4>
                        <p className="text-slate-700 dark:text-slate-300 mb-4">
                          <strong>Elements:</strong> {doshaInfo[dominantDosha].elements.join(' & ')}
                        </p>
                        <div className="mb-4">
                          <h5 className="font-medium text-slate-900 dark:text-white mb-2">Core Qualities (Gunas):</h5>
                          <div className="flex flex-wrap gap-2">
                            {doshaInfo[dominantDosha].qualities.map((quality, index) => (
                              <span
                                key={index}
                                className="inline-block px-3 py-1 bg-slate-200 dark:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-full text-sm"
                              >
                                {quality}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="lg:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                          <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                          </svg>
                          Natural Strengths
                        </h4>
                        <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                          {doshaInfo[dominantDosha].strengths.map((strength, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                              {strength}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                          <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                          </svg>
                          Potential Challenges (When Imbalanced)
                        </h4>
                        <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                          {doshaInfo[dominantDosha].challenges.map((challenge, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                              {challenge}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md md:col-span-2">
                        <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-4 flex items-center">
                          <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd"></path>
                          </svg>
                          Key Balancing Tips
                        </h4>
                        <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                          {doshaInfo[dominantDosha].balanceTips.map((tip, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                              {tip}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              )}

              {/* Secondary dosha influence */}
              {secondaryDosha && doshaInfo[secondaryDosha] && (
              <div className="mb-12">
                <h3 className="text-xl font-semibold text-slate-900 dark:text-white mb-4">
                  Your Secondary Influence: <span className="text-amber-500 dark:text-amber-400">{doshaInfo[secondaryDosha].name} ({doshaInfo[secondaryDosha].sanskrit})</span>
                </h3>
                <p className="text-slate-700 dark:text-slate-300 mb-6">
                  Your secondary dosha, {doshaInfo[secondaryDosha].name}, also plays an important role in your overall constitution, adding its unique qualities to your profile. Understanding this influence can provide further insights for maintaining balance.
                </p>

                <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-3">Key Strengths of {doshaInfo[secondaryDosha].name}</h4>
                      <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                        {doshaInfo[secondaryDosha].strengths.slice(0, 3).map((strength, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                            {strength}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-3">Potential Challenges of {doshaInfo[secondaryDosha].name}</h4>
                      <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                        {doshaInfo[secondaryDosha].challenges.slice(0, 3).map((challenge, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                            {challenge}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-medium text-slate-900 dark:text-white mb-3">Balancing Tips for {doshaInfo[secondaryDosha].name}</h4>
                      <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                        {doshaInfo[secondaryDosha].balanceTips.slice(0, 3).map((tip, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              )}

              {/* Herbal Recommendations Section */}
              {dominantDosha && doshaInfo[dominantDosha] && doshaInfo[dominantDosha].herbs && (
              <div className="mb-12">
                <h3 className="text-2xl font-semibold text-slate-900 dark:text-white mb-6">
                  <span className="text-green-500 dark:text-green-400 mr-2">🌿</span> Herbal Allies for Your {doshaInfo[dominantDosha].name} Constitution
                </h3>

                <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                  <p className="text-slate-700 dark:text-slate-300 mb-6">
                    Ayurveda utilizes a vast pharmacopoeia of herbs to help maintain doshic balance. The following traditional formulations and practices are particularly beneficial for pacifying your dominant {doshaInfo[dominantDosha].name} dosha. Each is chosen for its specific qualities (gunas) that counteract potential imbalances.
                  </p>

                  <div className="space-y-10">
                    {doshaInfo[dominantDosha].herbs.map((herb, index) => (
                      <div key={index} className={`herb-card bg-white dark:bg-slate-800 rounded-xl overflow-hidden shadow-lg border border-slate-200 dark:border-slate-700 hover:shadow-xl transition-shadow duration-300 herb-card-${index}`}>
                        <div className="flex flex-col lg:flex-row">
                          {/* Herb Image and Header Section */}
                          <div className="lg:w-1/3 relative">
                            <img
                              src={herb.image}
                              alt={herb.name}
                              className="w-full h-64 lg:h-full object-cover"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex flex-col justify-end p-5">
                              <div className="flex items-center gap-2 mb-1">
                                {herb.category && (
                                  <span className="px-2 py-1 text-xs rounded-full bg-green-100/90 text-green-800 font-medium shadow">
                                    {herb.category}
                                  </span>
                                )}
                              </div>
                              <h3 className="text-2xl font-bold text-white shadow-sm">{herb.name}</h3>
                              {herb.sanskrit && (
                                <span className="text-sm text-white/80 italic">{herb.sanskrit}</span>
                              )}
                            </div>
                          </div>

                          {/* Herb Details Section */}
                          <div className="lg:w-2/3 p-6">
                            <div className="mb-6">
                              <p className="text-slate-700 dark:text-slate-300 mb-4 leading-relaxed">{herb.description}</p>
                            </div>

                            {/* Tabs for Benefits, Ingredients, Preparation */}
                            <div className="border-b border-slate-200 dark:border-slate-700 mb-4">
                              <div className="flex flex-wrap -mb-px">
                                <button
                                  className={`tab-button-${index} mr-2 inline-block py-2 px-4 text-sm font-medium rounded-t-lg active-tab`}
                                  type="button"
                                  data-tab={`benefits-${index}`}
                                  onClick={(e) => {
                                    const target = e.currentTarget;
                                    const herbCard = target.closest(`.herb-card-${index}`);
                                    if (herbCard) {
                                      herbCard.querySelectorAll(`.tab-button-${index}`).forEach(btn => btn.classList.remove('active-tab'));
                                      target.classList.add('active-tab');
                                      herbCard.querySelectorAll(`.tab-content-${index}`).forEach(content => content.classList.add('hidden'));
                                      const activeTabContent = herbCard.querySelector(`#${target.dataset.tab}`);
                                      if (activeTabContent) activeTabContent.classList.remove('hidden');
                                    }
                                  }}
                                >
                                  <svg className="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                                  </svg>
                                  Benefits
                                </button>
                                <button
                                  className={`tab-button-${index} mr-2 inline-block py-2 px-4 text-sm font-medium rounded-t-lg`}
                                  type="button"
                                  data-tab={`ingredients-${index}`}
                                  onClick={(e) => {
                                    const target = e.currentTarget;
                                    const herbCard = target.closest(`.herb-card-${index}`);
                                     if (herbCard) {
                                      herbCard.querySelectorAll(`.tab-button-${index}`).forEach(btn => btn.classList.remove('active-tab'));
                                      target.classList.add('active-tab');
                                      herbCard.querySelectorAll(`.tab-content-${index}`).forEach(content => content.classList.add('hidden'));
                                      const activeTabContent = herbCard.querySelector(`#${target.dataset.tab}`);
                                      if (activeTabContent) activeTabContent.classList.remove('hidden');
                                    }
                                  }}
                                >
                                  <svg className="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"></path>
                                  </svg>
                                  Ingredients
                                </button>
                                <button
                                  className={`tab-button-${index} inline-block py-2 px-4 text-sm font-medium rounded-t-lg`}
                                  type="button"
                                  data-tab={`preparation-${index}`}
                                   onClick={(e) => {
                                    const target = e.currentTarget;
                                    const herbCard = target.closest(`.herb-card-${index}`);
                                     if (herbCard) {
                                      herbCard.querySelectorAll(`.tab-button-${index}`).forEach(btn => btn.classList.remove('active-tab'));
                                      target.classList.add('active-tab');
                                      herbCard.querySelectorAll(`.tab-content-${index}`).forEach(content => content.classList.add('hidden'));
                                      const activeTabContent = herbCard.querySelector(`#${target.dataset.tab}`);
                                      if (activeTabContent) activeTabContent.classList.remove('hidden');
                                    }
                                  }}
                                >
                                  <svg className="w-4 h-4 mr-1 inline-block" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
                                  </svg>
                                  Preparation
                                </button>
                              </div>
                            </div>

                            {/* Tab Contents */}
                            <div className="herb-tabs">
                              {/* Benefits Tab Content */}
                              <div id={`benefits-${index}`} className={`tab-content-${index}`}>
                                <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-3">Key Benefits</h4>
                                <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                                  {herb.benefits.map((benefit, idx) => (
                                    <li key={idx} className="flex items-start">
                                      <span className="text-green-500 dark:text-green-400 mr-2 text-xl leading-none">•</span>
                                      {benefit}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              {/* Ingredients Tab Content */}
                              <div id={`ingredients-${index}`} className={`tab-content-${index} hidden`}>
                                <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-3">Core Ingredients</h4>
                                <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                                  {herb.ingredients.map((ingredient, idx) => (
                                    <li key={idx} className="flex items-start">
                                      <span className="text-green-500 dark:text-green-400 mr-2 text-xl leading-none">•</span>
                                      {ingredient}
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              {/* Preparation Tab Content */}
                              <div id={`preparation-${index}`} className={`tab-content-${index} hidden`}>
                                <h4 className="text-lg font-medium text-slate-900 dark:text-white mb-3">Traditional Preparation</h4>
                                <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-900/30">
                                  <p className="text-slate-700 dark:text-slate-300 leading-relaxed">{herb.preparation}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-8 p-5 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-100 dark:border-green-900/30">
                    <h4 className="text-lg font-semibold text-slate-900 dark:text-white mb-3 flex items-center">
                      <svg className="w-5 h-5 text-green-500 dark:text-green-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"></path>
                      </svg>
                      Important Guidelines for Herbal Use
                    </h4>
                    <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                      <li className="flex items-start">
                        <span className="text-green-500 dark:text-green-400 mr-2 text-sm">1.</span>
                        Source high-quality, preferably organic, herbs from reputable suppliers to ensure purity and potency.
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-500 dark:text-green-400 mr-2 text-sm">2.</span>
                        Store herbs in airtight containers away from direct sunlight, heat, and moisture to preserve their medicinal properties.
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-500 dark:text-green-400 mr-2 text-sm">3.</span>
                        Begin with smaller doses to observe how your body responds, especially when introducing new herbs.
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-500 dark:text-green-400 mr-2 text-sm">4.</span>
                        If you are pregnant, nursing, taking medications, or have a pre-existing health condition, consult with a qualified Ayurvedic practitioner or healthcare provider before using herbal remedies.
                      </li>
                      <li className="flex items-start">
                        <span className="text-green-500 dark:text-green-400 mr-2 text-sm">5.</span>
                        Consistency is key; follow your herbal routine regularly for optimal benefits, but also listen to your body and adjust as needed under guidance.
                      </li>
                       <li className="flex items-start">
                        <span className="text-green-500 dark:text-green-400 mr-2 text-sm">6.</span>
                        Be mindful of the specific properties of herbs; for example, warming herbs are generally for Vata/Kapha and cooling herbs for Pitta.
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              )}

              {/* Dinacharya (Daily Routine) Section */}
              {dominantDosha && doshaInfo[dominantDosha] && doshaInfo[dominantDosha].dinacharya && (
              <div className="mb-12">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-2xl font-semibold text-slate-900 dark:text-white">
                    <span className="text-amber-500 dark:text-amber-400 mr-2">🌞</span> Aligning with Nature: Your Ideal {doshaInfo[dominantDosha].name} Dinacharya (Daily Routine)
                  </h3>
                </div>

                  <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6 shadow-md">
                    <p className="text-slate-700 dark:text-slate-300 mb-6">
                      Dinacharya, the Ayurvedic daily routine, is a powerful tool for maintaining health and balance by aligning our activities with the natural rhythms of the day and our specific doshic constitution. Following a routine tailored to your dominant {doshaInfo[dominantDosha].name} dosha can help prevent imbalances and promote vitality.
                    </p>

                    <div className="bg-white dark:bg-slate-800 rounded-lg overflow-hidden shadow-sm border border-slate-200 dark:border-slate-700 mb-6">
                      <div className="bg-amber-500 dark:bg-amber-600 text-white p-4">
                        <h4 className="text-xl font-semibold flex items-center">
                          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
                          </svg>
                          Optimal Wake-up Time: {doshaInfo[dominantDosha].dinacharya.wakeUpTime}
                        </h4>
                      </div>

                      <div className="p-5">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-8">
                          <div>
                            <h5 className="font-medium text-slate-900 dark:text-white mb-3 flex items-center">
                              <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"></path>
                              </svg>
                              Morning Rituals (Pratahkarma)
                            </h5>
                            <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                              {doshaInfo[dominantDosha].dinacharya.morningRoutine.map((item, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                                  {item}
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h5 className="font-medium text-slate-900 dark:text-white mb-3 flex items-center">
                              <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                              </svg>
                              Nourishing Meals (Ahara)
                            </h5>
                            <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                              {doshaInfo[dominantDosha].dinacharya.meals.map((meal, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                                  {meal}
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div className="md:col-span-2 border-t border-slate-200 dark:border-slate-700 pt-6"> {/* Full width for activities on medium screens */}
                            <h5 className="font-medium text-slate-900 dark:text-white mb-3 flex items-center">
                              <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                              </svg>
                              Daily Activities & Lifestyle (Vihara)
                            </h5>
                            <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                              {doshaInfo[dominantDosha].dinacharya.activities.map((activity, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                                  {activity}
                                </li>
                              ))}
                            </ul>
                          </div>

                          <div>
                            <h5 className="font-medium text-slate-900 dark:text-white mb-3 flex items-center">
                              <svg className="w-5 h-5 text-amber-500 dark:text-amber-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                              </svg>
                              Evening Routine (Sandhya Karma) & Sleep (Nidra)
                            </h5>
                            <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                              {doshaInfo[dominantDosha].dinacharya.eveningRoutine.map((item, index) => (
                                <li key={index} className="flex items-start">
                                  <span className="text-amber-500 dark:text-amber-400 mr-2 text-xl leading-none">•</span>
                                  {item}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>

                        <div className="mt-8 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-900/30">
                          <h5 className="font-medium text-slate-900 dark:text-white mb-3 flex items-center">
                            <svg className="w-5 h-5 text-red-500 dark:text-red-400 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
                            </svg>
                            Practices to Moderate or Avoid for {doshaInfo[dominantDosha].name} Balance
                          </h5>
                          <ul className="space-y-2 text-slate-700 dark:text-slate-300">
                            {doshaInfo[dominantDosha].dinacharya.avoidances.map((item, index) => (
                              <li key={index} className="flex items-start">
                                <span className="text-red-500 dark:text-red-400 mr-2 text-xl leading-none">•</span>
                                {item}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
              </div>
              )}

              {/* Save and Email Options */}
              <div className="mb-8 flex flex-wrap gap-4 justify-center">
                <button
                  onClick={() => {
                    alert('Email functionality will be added in the future. Your profile would be sent to your email.');
                  }}
                  id="email-button"
                  className="flex items-center px-6 py-3 bg-amber-400 hover:bg-amber-500 dark:bg-amber-600 dark:hover:bg-amber-700 text-slate-900 dark:text-white rounded-lg font-medium transition-all duration-300"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  Send Profile to Email
                </button>

                <button
                  onClick={resetQuiz}
                  className="flex items-center px-6 py-3 bg-slate-200 hover:bg-slate-300 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-lg font-medium transition-all duration-300"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  Start New Profile / Retake Quiz
                </button>
              </div>

              {/* Disclaimer */}
              <div className="bg-slate-100 dark:bg-slate-700/50 rounded-xl p-6 text-sm text-slate-600 dark:text-slate-400">
                <p className="mb-2">
                  <strong>Important Disclaimer:</strong> This quiz and the information provided are intended for educational and general informational purposes only, offering a simplified assessment of your potential Ayurvedic constitution (Prakriti). It is not a substitute for professional medical advice, diagnosis, or treatment from a qualified Ayurvedic practitioner or other healthcare provider.
                </p>
                <p className="mb-2">
                  Your unique constitution (Prakriti) and current state of balance (Vikriti) can be influenced by many factors including age, season, lifestyle, diet, and underlying health conditions. For a comprehensive and personalized Ayurvedic assessment, diagnosis, and treatment plan, it is essential to consult with an experienced Ayurvedic professional.
                </p>
                <p>
                  Do not disregard professional medical advice or delay in seeking it because of something you have read or interpreted from this quiz. Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

    </div>
  );
};

export default AyurvedicProfile;