<?php
header('Access-Control-Allow-Origin: *'); // Replace * with your React app's domain in production
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// If it's a preflight request, send OK response and exit
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

// Get POST data
$data = json_decode(file_get_contents('php://input'), true);
if (!$data) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit();
}

// Validate request type
if (!isset($data['type']) || !in_array($data['type'], ['contact', 'application', 'newsletter', 'group-booking', 'beginner-class', 'retreat-inquiry'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid form type']);
    exit();
}

// Email configuration
$adminEmail = "<EMAIL>"; // Replace with your admin email
$headers = "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: text/html; charset=UTF-8\r\n";
$headers .= "From: Shanti Yog Peeth Website <<EMAIL>>\r\n";

if ($data['type'] === 'contact') {
    // Validate contact form data
    if (!isset($data['name']) || !isset($data['email']) || !isset($data['subject']) || !isset($data['message'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit();
    }

    // Sanitize inputs
    $name = filter_var($data['name'], FILTER_SANITIZE_STRING);
    $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    $subject = filter_var($data['subject'], FILTER_SANITIZE_STRING);
    $message = filter_var($data['message'], FILTER_SANITIZE_STRING);
    $phone = isset($data['phone']) ? filter_var($data['phone'], FILTER_SANITIZE_STRING) : 'Not provided';

    // Add Reply-To header
    $headers .= "Reply-To: $name <$email>\r\n";

    // Prepare email content
    $emailSubject = "New Contact Form Submission: $subject";
    $emailBody = "<h2>New Contact Form Submission</h2>
                 <p><strong>Name:</strong> $name</p>
                 <p><strong>Email:</strong> $email</p>
                 <p><strong>Phone:</strong> $phone</p>
                 <p><strong>Subject:</strong> $subject</p>
                 <p><strong>Message:</strong></p>
                 <p>$message</p>";

} else if ($data['type'] === 'application') {
    // Validate application form data
    if (!isset($data['fullName']) || !isset($data['email']) || !isset($data['courseId']) || !isset($data['gender']) || !isset($data['dob']) || !isset($data['country']) || !isset($data['phone'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit();
    }

    // Sanitize inputs
    $fullName = filter_var($data['fullName'], FILTER_SANITIZE_STRING);
    $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    $phone = filter_var($data['phone'], FILTER_SANITIZE_STRING);
    $country = filter_var($data['country'], FILTER_SANITIZE_STRING);
    $courseId = filter_var($data['courseId'], FILTER_SANITIZE_STRING);
    $gender = filter_var($data['gender'], FILTER_SANITIZE_STRING);
    $dob = filter_var($data['dob'], FILTER_SANITIZE_STRING);
    $startDate = isset($data['startDate']) ? filter_var($data['startDate'], FILTER_SANITIZE_STRING) : '';
    $programTitle = isset($data['programTitle']) ? filter_var($data['programTitle'], FILTER_SANITIZE_STRING) : '';
    $programType = isset($data['programType']) ? filter_var($data['programType'], FILTER_SANITIZE_STRING) : '';

    // Add Reply-To header
    $headers .= "Reply-To: $fullName <$email>\r\n";

    // Prepare email content
    $emailSubject = "New Application: $programType - $programTitle ($fullName)";
    $emailBody = "<h2>New Application - $programType</h2>
                 <p><strong>Program:</strong> $programTitle</p>
                 <p><strong>Program Type:</strong> $programType</p>
                 <p><strong>Name:</strong> $fullName</p>
                 <p><strong>Email:</strong> $email</p>
                 <p><strong>Mobile/WhatsApp:</strong> $phone</p>
                 <p><strong>Gender:</strong> $gender</p>
                 <p><strong>Date of Birth:</strong> $dob</p>
                 <p><strong>Country:</strong> $country</p>
                 <p><strong>Program ID:</strong> $courseId</p>
                 <p><strong>Start Date:</strong> $startDate</p>";

    if (isset($data['yogaExperience'])) {
        $yogaExp = filter_var($data['yogaExperience'], FILTER_SANITIZE_STRING);
        $emailBody .= "<p><strong>Yoga Experience:</strong></p><p>$yogaExp</p>";
    }

    if (isset($data['selectedPackage'])) {
        $package = filter_var($data['selectedPackage'], FILTER_SANITIZE_STRING);
        $emailBody .= "<p><strong>Selected Package:</strong> $package</p>";
    }

    if (isset($data['teachingExperience'])) {
        $teachingExp = filter_var($data['teachingExperience'], FILTER_SANITIZE_STRING);
        $emailBody .= "<p><strong>Teaching Experience:</strong></p><p>$teachingExp</p>";
    }

    if (isset($data['expectations'])) {
        $expectations = filter_var($data['expectations'], FILTER_SANITIZE_STRING);
        $emailBody .= "<p><strong>Expectations:</strong></p><p>$expectations</p>";
    }

    if (isset($data['dietaryRestrictions'])) {
        $dietary = filter_var($data['dietaryRestrictions'], FILTER_SANITIZE_STRING);
        $emailBody .= "<p><strong>Dietary Restrictions:</strong></p><p>$dietary</p>";
    }

    if (isset($data['medicalConditions'])) {
        $medical = filter_var($data['medicalConditions'], FILTER_SANITIZE_STRING);
        $emailBody .= "<p><strong>Medical Conditions:</strong></p><p>$medical</p>";
    }
} else if ($data['type'] === 'group-booking') {
    // Validate group booking form data
    if (!isset($data['name']) || !isset($data['email']) || !isset($data['groupType']) || !isset($data['requirements'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit();
    }

    // Sanitize inputs
    $name = filter_var($data['name'], FILTER_SANITIZE_STRING);
    $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    $groupType = filter_var($data['groupType'], FILTER_SANITIZE_STRING);
    $requirements = filter_var($data['requirements'], FILTER_SANITIZE_STRING);
    
    // Add Reply-To header
    $headers .= "Reply-To: $name <$email>\r\n";

    // Prepare email content
    $emailSubject = "New Group Booking Inquiry: $groupType";
    $emailBody = "<h2>New Group Booking Inquiry</h2>
                 <p><strong>Name:</strong> $name</p>
                 <p><strong>Email:</strong> $email</p>
                 <p><strong>Group Type:</strong> $groupType</p>
                 <p><strong>Requirements:</strong></p>
                 <p>$requirements</p>";

} else if ($data['type'] === 'beginner-class') {
    // Validate beginner class registration
    if (!isset($data['name']) || !isset($data['email'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit();
    }

    // Sanitize inputs
    $name = filter_var($data['name'], FILTER_SANITIZE_STRING);
    $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    
    // Add Reply-To header
    $headers .= "Reply-To: $name <$email>\r\n";

    // Prepare email content
    $emailSubject = "New Beginner Class Registration";
    $emailBody = "<h2>New Beginner Class Registration</h2>
                 <p><strong>Name:</strong> $name</p>
                 <p><strong>Email:</strong> $email</p>";

} else if ($data['type'] === 'retreat-inquiry') {
    // Validate retreat inquiry
    if (!isset($data['name']) || !isset($data['email'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        exit();
    }

    // Sanitize inputs
    $name = filter_var($data['name'], FILTER_SANITIZE_STRING);
    $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    
    // Add Reply-To header
    $headers .= "Reply-To: $name <$email>\r\n";

    // Prepare email content
    $emailSubject = "New Retreat Inquiry";
    $emailBody = "<h2>New Retreat Inquiry</h2>
                 <p><strong>Name:</strong> $name</p>
                 <p><strong>Email:</strong> $email</p>";

} else if ($data['type'] === 'newsletter') {
    // Validate newsletter subscription
    if (!isset($data['email'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing email address']);
        exit();
    }

    // Sanitize input
    $email = filter_var($data['email'], FILTER_SANITIZE_EMAIL);
    
    // Add Reply-To header
    $headers .= "Reply-To: <$email>\r\n";

    // Prepare email content
    $emailSubject = "New Newsletter Subscription";
    $emailBody = "<h2>New Newsletter Subscription</h2>
                 <p><strong>Email:</strong> $email</p>";
}

// Send email to admin
$mailSent = mail($adminEmail, $emailSubject, $emailBody, $headers);

// Send confirmation email to user
$userSubject = match($data['type']) {
    'contact' => "Thank you for contacting Shanti Yog Peeth",
    'application' => "Thank you for your course application",
    'newsletter' => "Welcome to Shanti Yog Peeth Newsletter",
    'group-booking' => "Thank you for your group booking inquiry",
    'beginner-class' => "Thank you for registering for beginner classes",
    'retreat-inquiry' => "Thank you for your retreat inquiry"
};

$userMessage = match($data['type']) {
    'contact' => "<p>Dear $name,</p>
                 <p>Thank you for contacting Shanti Yog Peeth. We have received your message and will get back to you shortly.</p>
                 <p>Best regards,<br>Shanti Yog Peeth Team</p>",
    'application' => "<p>Dear $firstName,</p>
                     <p>Thank you for applying to our course. We have received your application and will review it shortly.</p>
                     <p>Best regards,<br>Shanti Yog Peeth Team</p>",
    'newsletter' => "<p>Dear Subscriber,</p>
                    <p>Thank you for subscribing to our newsletter. You will now receive our latest updates, yoga tips, and special offers.</p>
                    <p>Best regards,<br>Shanti Yog Peeth Team</p>",
    'group-booking' => "<p>Dear $name,</p>
                       <p>Thank you for your group booking inquiry. We have received your request and will get back to you with available options soon.</p>
                       <p>Best regards,<br>Shanti Yog Peeth Team</p>",
    'beginner-class' => "<p>Dear $name,</p>
                        <p>Thank you for registering for our beginner yoga classes. We will contact you shortly with class schedule and details.</p>
                        <p>Best regards,<br>Shanti Yog Peeth Team</p>",
    'retreat-inquiry' => "<p>Dear $name,</p>
                         <p>Thank you for your interest in our retreats. We will get back to you with more information soon.</p>
                         <p>Best regards,<br>Shanti Yog Peeth Team</p>"
};

// Reset headers to remove Reply-To for user confirmation email
$headers = "MIME-Version: 1.0\r\n";
$headers .= "Content-Type: text/html; charset=UTF-8\r\n";
$headers .= "From: Shanti Yog Peeth <<EMAIL>>\r\n";

$userMailSent = mail($email, $userSubject, $userMessage, $headers);

if ($mailSent && $userMailSent) {
    http_response_code(200);
    echo json_encode(['message' => 'Email sent successfully']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to send email']);
}
